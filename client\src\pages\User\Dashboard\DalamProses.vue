<template>
  <div>
    <v-list>
      <List
        v-show="datalist.length"
        dbref="UJI_SelPermohonanList"
        style="height: calc(100vh - 136px)"
        :items.sync="datalist"
        :dbparams="dbparams"
        :rebind="rebind"
        :selectOnLoad="true"
      >
        <template v-slot="{ row }">
          <v-list-item>
            <v-list-item-content>
              <v-list-item-title>{{ row.NamaPelanggan }}</v-list-item-title>
              <v-list-item-subtitle>
                {{ row.NoPengujian }} | {{ row.JenisUji }}
              </v-list-item-subtitle>
              <v-list-item-subtitle style="font-weight: bold">
                Rp. {{ row.TotalBayar | format }}
              </v-list-item-subtitle>
            </v-list-item-content>
            <v-list-item-action>
              <v-btn
                small
                outlined
                color="primary"
                @click="ShowDetail(row.PermohonanID)"
                >DETAIL</v-btn
              >
            </v-list-item-action>
          </v-list-item>
        </template>
      </List>
    </v-list>
    <ProsesDetail :data="detail"> </ProsesDetail>
  </div>
</template>
<script>
import ProsesDetail from './DalamProsesDetail.vue'
export default {
  components: {
    ProsesDetail,
  },
  data: () => ({
    detail: {
      show: false,
      PermohonanID: '',
    },
    datalist: [],
    statusId: '4,5,6,7,8',
    rebind: 1,
  }),
  computed: {
    dbparams() {
      return { StatusID: this.statusId }
    },
  },
  watch: {
    'billing.show'(val) {
      if (!val) {
        this.rebind++
        this.billing.TotalBayar = 0
      }
    },
  },
  methods: {
    async ShowDetail(permohonanId) {
      this.detail.show = true
      this.detail.PermohonanID = permohonanId
      // let ret = await this.$api.call('UJI_SelPermohonanDet', {
      //   PermohonanID: permohonanId,
      // })
      // console.log(ret)
    },
  },
}
</script>
