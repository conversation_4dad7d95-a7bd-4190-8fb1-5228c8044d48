var express = require("express");
var app = express();
var bodyParser = require("body-parser");
var api = require("./api/call");
var appex = require("./api/appex");
var reports = require("./api/reports");
var batch = require("./api/batch");
var otherReports = require("./api/reports/others")
var ujiReports = require("./api/reports/uji")
var db = require("./common/db");
const wamod = require("./api/whatsapp");
var {websocket} = require("./common/ws");
var timeout = require("connect-timeout");
var cookieParser = require("cookie-parser");
const {Client, LocalAuth} = require('whatsapp-web.js');
const telegram = require("./common/telegram");
app.locals.telegram = telegram
require('dotenv').config()

const PORT = 8001;

app.use(bodyParser.json());
app.use(cookieParser('eriganteng'));

app.use("/uploads", express.static("uploads"));
app.use("/sertifikat", express.static("sertifikat"));
process.env.DEVELOPMENT = true;

app.set("etag", false);

const cors = require("cors");
app.use(
  cors({
    origin: function(origin, callback) {
      if (!origin) return callback(null, true);
      // if (allowedOrigins.indexOf(origin) === -1) {
      //   var msg = "The CORS policy for this site does not " + "allow access from the specified Origin.";
      //   return callback(new Error(msg), false);
      // }
      return callback(null, true);
    },
    credentials: true,
    allowedHeaders: "Origin,X-Requested-With,Content-Type,Accept,Authorization",
    methods: "GET,PUT,POST,DELETE,OPTIONS"
  })
);

let wss = websocket();
const wa = new Client({
  authStrategy: new LocalAuth(),
  puppeteer: {headless: true, ignoreHTTPSErrors: true, args: ["--no-sandbox"]}
});

wa.on('qr', (qr) => {
  // Generate and scan this code with your phone
  console.log('QR RECEIVED', qr);
  wa.qrcode = qr
});

wa.on('ready', () => {
  wa.qrcode = 'No QRCODE, WA is connected'
  console.log('Whatsapp is ready!');
});

wa.on('message', message => {
  wamod.command(wa, message)
});

// wa.initialize()

app.use("/api", api(wa));
app.use("/reports", reports);
app.use("/reports/others", otherReports);
app.use("/reports/uji", ujiReports(wa));
app.use("/report", reports);
// app.use("/batch", batch);
app.use("/", appex(wss, wa));
app.use(timeout("30000s"));
app.listen(PORT);

console.log(`Server Ready at port ${PORT}.`);
