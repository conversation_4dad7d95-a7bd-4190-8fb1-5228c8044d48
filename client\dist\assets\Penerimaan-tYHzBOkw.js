import{n as f,R as v,S as h,a as r,b as u,c as d,d as p,e as o,f as x,_ as i,g as m,h as c,j as l,k as _,i as g}from"./index-DYIZrBBo.js";const S={components:{SidePane:h,ReportPopup:v},data:()=>({datagrid:[],dbparams:{PermohonanID:0},spudatagrid:[],rebindSidebar:0,rebindUpload:0,forms:{},reportUrl:"",showReport:!1,spu:{show:!1},jenis:[{val:"A",txt:"Mutu Air & Lingkungan"},{val:"B",txt:"Bahan Bangunan"},{val:"Ba",txt:"Aspal"},{val:"T",txt:"Tanah (Geoteknik)"}],satker:[{val:"1",txt:"APBN"},{val:"2",txt:"APBD I - BMCK"},{val:"3",txt:"APBD I - NON BMCK"},{val:"4",txt:"APBD II"},{val:"5",txt:"Swasta"},{val:"6",txt:"Perorangan"},{val:"7",txt:"Lainnya"}]}),methods:{async ItemClick(s){this.rebindUpload++,this.dbparams={PermohonanID:s.PermohonanID};var t=await this.$api.call("UJI.SelPermohonan",{PermohonanID:s.PermohonanID});t.data.length?this.forms=t.data[0]:this.forms={}},async Download(s){this.$api.download(this.$api.url+s,!0)},async Save(){if(!confirm("Sudah yakin pada hasilnya?"))return;this.$api.call("UJI_SavPermohonan",{...this.forms,XmlPermohonanDet:this.datagrid}).success&&this.rebindSidebar++},async CetakSPU(){this.spu.show=!1,this.reportUrl="/reports/uji/spu/"+this.forms.PermohonanID,this.showReport=!0},async CloseReport(){this.showReport=!1}}};var w=function(){var t=this,a=t._self._c;return a("div",{staticStyle:{display:"flex"}},[a("SidePane",{attrs:{statusId:",1,2,3,4,",rebind:t.rebindSidebar},on:{"item-click":t.ItemClick}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.PermohonanID,expression:"forms.PermohonanID"}],staticClass:"right-pane col-12 col-lg-10 col-md-9 col-sm-12",class:t.$api.isMobile()?"":"form-inline",staticStyle:{padding:"20px 20px",width:"calc(100vw - 368px)",overflow:"auto",height:"calc(100vh - 66px)"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.BayarStatus==2,expression:"forms.BayarStatus == 2"}],staticClass:"dvWarn"},[t._v(" "+t._s(t.forms.CatatanKhusus)+" ")]),a("div",{staticStyle:{float:"right","font-family":"raleway",background:"white","margin-top":"-20px"},attrs:{id:"dvRightBox"}},[a("div",{staticStyle:{padding:"10px 20px","text-align":"center"}},[a("div",{staticStyle:{"font-size":"xx-large"}},[t._v(t._s(t.forms.NoPengujian))]),a("div",[t._v(t._s(t.forms.ShareCode))])]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.StatusID>=4&&t.forms.BayarStatus==1,expression:"forms.StatusID >= 4 && forms.BayarStatus == 1"}],staticStyle:{padding:"10px 20px","border-top":"1px solid #e3e3e3",background:"#4caf50",color:"white","text-align":"center"}},[a("span",[t._v("Sudah Dibayarkan")])]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.StatusID==9,expression:"forms.StatusID == 9"}],staticStyle:{padding:"10px 20px","border-top":"1px solid #e3e3e3",background:"#8bc34a",color:"white","text-align":"center"}},[a("span",[t._v("Sudah Diserahkan")])])]),a(r,{attrs:{type:"text",label:"Nama Pelanggan",value:t.forms.Nama,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"Nama",e)}}}),a(u,{attrs:{label:"Alamat",value:t.forms.Alamat,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"Alamat",e)}}}),t._v(" "),a(r,{attrs:{type:"text",label:"No. Ponsel",value:t.forms.Phone,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"Phone",e)}}}),a(d,{attrs:{items:t.jenis,label:"Nama/Jenis Contoh",value:t.forms.JenisID,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"JenisID",e)}}}),a(p,{staticStyle:{"padding-top":"4px"},attrs:{label:"Tanggal Masuk",value:t.forms.TglMasuk},on:{"update:value":function(e){return t.$set(t.forms,"TglMasuk",e)}}}),a(u,{attrs:{label:"Kegiatan/Paket Pekerjaan",value:t.forms.NamaKegiatan,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"NamaKegiatan",e)}}}),t._v(" "),a(o,{attrs:{label:"Sumber/SatKer"}},[a(d,{attrs:{items:t.satker,value:t.forms.SumberDana,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"SumberDana",e)}}}),a(r,{directives:[{name:"show",rawName:"v-show",value:t.forms.SumberDana<5,expression:"forms.SumberDana < 5"}],staticStyle:{"margin-left":"5px"},attrs:{type:"text",placeholder:"Satker / Kab / Kota",value:t.forms.SatKer,width:"295px"},on:{"update:value":function(e){return t.$set(t.forms,"SatKer",e)}}})],1),a(o,{attrs:{label:"Surat Permohonan/Tgl."}},[a(r,{attrs:{type:"text",value:t.forms.SuratNo,placeholder:"No. Surat",width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"SuratNo",e)}}}),a(p,{staticStyle:{"margin-left":"5px"},attrs:{value:t.forms.SuratTgl,width:"295px"},on:{"update:value":function(e){return t.$set(t.forms,"SuratTgl",e)}}})],1),a(o,{attrs:{label:"Surat Permohonan"}},[a(x,{key:t.rebindUpload,attrs:{value:t.forms.SuratUrl},on:{"update:value":function(e){return t.$set(t.forms,"SuratUrl",e)}},scopedSlots:t._u([{key:"default",fn:function({opener:e,fileName:n}){return[a(i,{directives:[{name:"show",rawName:"v-show",value:n,expression:"fileName"}],staticStyle:{"margin-right":"8px"},attrs:{small:"",text:"",outlined:""},on:{click:function(P){return t.Download(t.forms.SuratUrl)}}},[t._v(t._s(n||t.forms.SuratUrl))]),a(i,{attrs:{small:""},on:{click:e}},[a(m,[t._v("mdi-upload")])],1)]}}])})],1),a(c,{attrs:{datagrid:t.datagrid,dbref:"UJI_SelPermohonanDet",dbparams:t.dbparams,disabled:!0,groupBy:"NamaPaket",columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Jml",value:"JmlContoh"},{name:"Metode",value:"Metode"},{name:"Waktu",value:"Waktu"},{name:"Keterangan",value:"Keterangan"}]},on:{"update:datagrid":function(e){t.datagrid=e}},scopedSlots:t._u([{key:"group-row",fn:function({row:e,columns:n}){return[a("td",{staticStyle:{background:"#f3f3f3"},attrs:{colspan:n.length}},[a("div",{staticStyle:{padding:"0",display:"flex"}},[a("div",{staticStyle:{"font-weight":"bold"}},[t._v(" "+t._s(e.NamaPaket||"(NON PAKET)")+" ")])])])]}},{key:"row-NamaParameter",fn:function({row:e}){return[a("div",{staticStyle:{display:"flex"}},[a(l,{staticStyle:{position:"relative",top:"-2px"},attrs:{value:e.IsVerified},on:{"update:value":function(n){return t.$set(e,"IsVerified",n)}}}),a("div",{staticStyle:{"margin-left":"10px",position:"relative",top:"2px"}},[a("div",{class:e.IsVerified?"":"coret",staticStyle:{"font-size":"14px"}},[t._v(" "+t._s(e.NamaParameter)+" ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.NamaContoh,expression:"row.NamaContoh"}],staticStyle:{color:"gray"}},[t._v(" "+t._s(e.NamaContoh)+" ")])])],1)]}},{key:"row-Keterangan",fn:function({row:e}){return[a(r,{attrs:{type:"text",value:e.Keterangan,width:"300px"},on:{"update:value":function(n){return t.$set(e,"Keterangan",n)}}})]}}])}),a("div",{staticClass:"form-inline",attrs:{id:"dvBP2"}},[a("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","margin-bottom":"5px","margin-top":"10px"}},[t._v(" PENERIMAAN SAMPLE / CONTOH UJI ")]),a(o,{attrs:{label:"Jumlah"}},[a(l,{staticStyle:{width:"250px"},attrs:{text:t.forms.KondisiJumlah?"Cukup":"Tidak Cukup",value:t.forms.KondisiJumlah},on:{"update:value":function(e){return t.$set(t.forms,"KondisiJumlah",e)}}}),a(r,{staticStyle:{position:"relative",top:"-2px"},attrs:{placeholder:"Keterangan",width:"370px",value:t.forms.KetJumlah},on:{"update:value":function(e){return t.$set(t.forms,"KetJumlah",e)}}})],1),a(o,{attrs:{label:"Kondisi"}},[a(l,{staticStyle:{width:"250px"},attrs:{text:t.forms.KondisiKondisi?"Cukup":"Tidak Cukup",value:t.forms.KondisiKondisi},on:{"update:value":function(e){return t.$set(t.forms,"KondisiKondisi",e)}}}),a(r,{staticStyle:{position:"relative",top:"-2px"},attrs:{placeholder:"Keterangan",width:"370px",value:t.forms.KetKondisi},on:{"update:value":function(e){return t.$set(t.forms,"KetKondisi",e)}}})],1),a(o,{attrs:{label:"Tempat Contoh / Wadah"}},[a(l,{staticStyle:{width:"250px"},attrs:{text:t.forms.KondisiWadah?"Cukup":"Tidak Cukup",value:t.forms.KondisiWadah},on:{"update:value":function(e){return t.$set(t.forms,"KondisiWadah",e)}}}),a(r,{staticStyle:{position:"relative",top:"-2px"},attrs:{placeholder:"Keterangan",width:"370px",value:t.forms.KetWadah},on:{"update:value":function(e){return t.$set(t.forms,"KetWadah",e)}}})],1),a(u,{attrs:{placeholder:"Catatan Penerimaan",value:t.forms.KetPenerimaan,width:"820px"},on:{"update:value":function(e){return t.$set(t.forms,"KetPenerimaan",e)}}})],1),a("br"),a("br"),a("div",{staticStyle:{display:"flex"}},[a(i,{staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:t.Save}},[t._v(" SIMPAN ")]),a(_),t.forms.BayarStatus!=1?a(i,{attrs:{text:"",outlined:"",color:"warning"}},[a(m,{attrs:{left:""}},[t._v("mdi-alert")]),t._v(" BELUM DIBAYAR ")],1):t._e()],1)],1),t.showReport?a("ReportPopup",{directives:[{name:"click-outside",rawName:"v-click-outside",value:t.CloseReport,expression:"CloseReport"}],attrs:{reportUrl:t.reportUrl}}):t._e(),a(g,{attrs:{show:t.spu.show,title:"Surat Perintah Uji",submitText:"CETAK"},on:{"update:show":function(e){return t.$set(t.spu,"show",e)},submit:t.CetakSPU}},[a(c,{attrs:{datagrid:t.spudatagrid,dbref:"UJI.SpuPenguji",dbparams:t.dbparams,columns:[{name:"Nama Penguji",value:"NamaPenguji",width:"200px !important",editable:{com:"Select",dbref:"UJI_SelPenguji",value:"PengujiID",text:"NamaPenguji",width:"200px !important"}},{name:"Jabatan",value:"Jabatan",width:"130px !important",editable:{com:"Select",items:[{val:"Penyelia",txt:"Penyelia"},{val:"Teknisi",txt:"Teknisi"}],width:"130px !important"}}]},on:{"update:datagrid":function(e){t.spudatagrid=e}}})],1)],1)},y=[],b=f(S,w,y,!1,null,null);const K=b.exports;export{K as default};
