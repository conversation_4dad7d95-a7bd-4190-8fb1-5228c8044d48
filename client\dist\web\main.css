/* prettier-ignore */
@import url(https://fonts.googleapis.com/css?family=Roboto:400,300,100,700,100italic,300italic,400italic,700italic);

html,
body {
  height: 100%;
  padding-left:0;
}
@media screen and (min-width: 1024px) {
  .navbar-right ul.navx {
    margin: 37.5px 0 !important;
  }
}
.navbar-right ul.navx {
  list-style: none !important;
  margin: 10px 0;
}
.navbar-right ul.navx li {
  padding: 5px;
}
.navbar-right ul li {
  margin-left: 20px;
}
.navx li ul {
  position: absolute;
  width: 250px;
  background-color: white;
  list-style-type: none;
  margin-top: 25px !important;
  margin-left: -92px !important;
  border-bottom: 1px solid silver;
  display: none;
}
.navx li ul li {
  margin-left: -25px;
  padding: 5px 0;
}
.navx li:hover ul {
  display: initial;
}
.gx1,
div.grid row cl:first-child {
  background: #ddd;
}
.gx2,
div.grid row cl {
  background: #fff;
}
.gx4,
div.grid row.selected cl:first-child,
div.grid row.selected cl {
  background: none repeat scroll 0 0 lightyellow;
  color: #333333;
}
[type="list"]:focus,
[type="grid"]:focus {
  -moz-outline-style: none;
  outline: none;
  outline: 0;
}

cl {
  display: inline-block;
  margin-left: -1px;
}
div.grid {
  padding: 1px 0 0 1px;
}
div.grid row {
  margin-top: -1px;
  display: block;
}
div.grid row cl[cn="__id"] {
  display: none;
}
div.grid[type="list"] {
  margin-left: 4px;
}
div.grid[type="list"] row cl {
  /*margin-left:-4px;*/
}
div.grid row cl {
  padding: 4px 6px;
  display: inline-block;
  border: 1px solid #dee;
  line-height: 20px;
  height: 29px;
  vertical-align: top;
  width: 130px;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
}
.grid header row:first-child cl,
.grid items row:first-child cl {
  border-top: 1px solid silver;
}
.grid header input[type="text"] {
  background: transparent;
  border: 0;
  margin-top: -2px;
  box-shadow: 0 0;
  padding-left: 6px;
}
div.grid row cl:first-child {
  width: 40px;
}
div.grid insert cl:first-child,
div.grid cl.fa:first-child {
  font-size: 20px;
  text-align: center;
  cursor: pointer;
}
cl.fa-save {
  font-size: 14px !important;
  text-align: center;
  cursor: pointer;
}
div.grid header cl {
  background: rgba(238, 238, 238, 1);
  font-weight: 600;
  color: #444;
  text-transform: uppercase;
}
div.grid insert,
div.grid footer {
  top: 5px;
  position: relative;
}
div.grid > items {
  display: block;
  overflow: auto;
  overflow-x: hidden;
}
div.grid cl > input {
  /*margin-left: -6px;*/
  margin-left: -6px;
  margin-top: -4px;
  width: calc(100% + 2px);
  border-radius: 0px;
  font-size: 12px;
}
cl > textarea {
  margin-left: -6px;
  margin-top: -4px;
  width: 100%;
  position: relative;
  z-index: 10;
}
cl > .input-append {
  margin-left: -6px;
  margin-top: -4px;
  width: calc(100% + 17px);
}
div.grid cl > [type="checkbox"] {
  margin-top: -4px;
}
/*
Template Name: Multi
Version: 1.0
Author: ShapeBootstrap
Author URL: http://shapebootstrap.net
*/
/*************************
*******Typography******
**************************/
body {
  padding-top: 115px;
  background: #fff;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  color: #64686d;
  line-height: 26px;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  font-family: "Roboto", sans-serif;
  color: #272727;
}
a {
  color: #45aed6;
  -webkit-transition: color 400ms, background-color 400ms;
  -moz-transition: color 400ms, background-color 400ms;
  -o-transition: color 400ms, background-color 400ms;
  transition: color 400ms, background-color 400ms;
}
a:hover,
a:focus {
  text-decoration: none;
  color: #2a95be;
}
hr {
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #fff;
}
.gradiant {
  background-image: -moz-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -webkit-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -ms-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
}
.gradiant-horizontal {
  background-image: -moz-linear-gradient(4deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -webkit-linear-gradient(4deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -ms-linear-gradient(4deg, #2caab3 0%, #2c8cb3 100%);
}
.section-header {
  margin-bottom: 50px;
}
.section-header .section-title {
  font-size: 44px;
  color: #272727;
  text-transform: uppercase;
  position: relative;
  padding-bottom: 20px;
  margin: 0 0 20px;
}
.section-header .section-title:before {
  content: "";
  position: absolute;
  width: 140px;
  bottom: 0;
  left: 50%;
  margin-left: -70px;
  height: 1px;
  background: #ebebeb;
}
.section-header .section-title:after {
  content: "";
  position: absolute;
  width: 24px;
  height: 24px;
  bottom: -11px;
  left: 50%;
  margin-left: -12px;
  border: 5px solid #fff;
  border-radius: 20px;
  background: #45aed6;
}
.btn {
  border-width: 0;
  border-bottom-width: 3px;
  border-radius: 3px;
}
.btn.btn-primary {
  background: #45aed6;
  border-color: #2a95be;
}
.btn.btn-primary:hover,
.btn.btn-primary:focus {
  background: #2a95be;
}
.column-title {
  margin-top: 0;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  margin-bottom: 15px;
  position: relative;
}
.column-title:after {
  content: " ";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 40%;
  border-bottom: 1px solid #45aed6;
}
ul.nostyle {
  list-style: none;
  padding: 0;
  margin-bottom: 20px;
}
ul.nostyle i {
  color: #45aed6;
}
.scaleIn {
  -webkit-animation-name: scaleIn;
  animation-name: scaleIn;
}
@-webkit-keyframes scaleIn {
  0% {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes scaleIn {
  0% {
    opacity: 0;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}
/*************************
*******Header******
**************************/
.navbar.navbar-default {
  border: 0;
  border-radius: 0;
  margin-bottom: 0;
}
.navbar.navbar-default .navbar-toggle {
  margin-top: 32px;
}
.navbar.navbar-default .navbar-brand {
  height: auto;
  padding: 22px 15px 21px;
}
@media only screen and (min-width: 768px) {
  #main-menu.navbar-default {
    background: rgba(255, 255, 255, 0.9);
    -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.1);
  }
  #main-menu.navbar-default .navbar-nav > li > a {
    padding-top: 40px;
    padding-bottom: 40px;
  }
  #main-menu.navbar-default .navbar-nav > li.active > a,
  #main-menu.navbar-default .navbar-nav > li.open > a,
  #main-menu.navbar-default .navbar-nav > li:hover > a {
    background: transparent;
    padding-top: 37px;
    border-top: 3px solid #45aed6;
  }
  #main-menu.navbar-default .dropdown-menu {
    padding: 0 20px;
    min-width: 220px;
    background-color: rgba(26, 28, 40, 0.9);
    border: 0;
    border-radius: 0;
    box-shadow: none;
    -webkit-box-shadow: none;
    background-clip: inherit;
  }
  #main-menu.navbar-default .dropdown-menu > li {
    border-left: 3px solid transparent;
    margin-left: -20px;
    padding-left: 17px;
    -webit-transition: border-color 400ms;
    transition: border-color 400ms;
  }
  #main-menu.navbar-default .dropdown-menu > li > a {
    border-top: 1px solid #404455;
    padding: 15px 0;
    color: #eeeeee;
  }
  #main-menu.navbar-default .dropdown-menu > li:first-child > a {
    border-top: 0;
  }
  #main-menu.navbar-default .dropdown-menu > li.active,
  #main-menu.navbar-default .dropdown-menu > li.open,
  #main-menu.navbar-default .dropdown-menu > li:hover {
    border-left-color: #45aed6;
  }
  #main-menu.navbar-default .dropdown-menu > li.active > a,
  #main-menu.navbar-default .dropdown-menu > li.open > a,
  #main-menu.navbar-default .dropdown-menu > li:hover > a {
    color: #45aed6;
    background-color: transparent;
  }
}
#main-slider {
  overflow: hidden;
}
#main-slider .item {
  height: 500px;
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: cover;
}
.is-mobile #main-slider .item {
  height: 60vw;
}
.is-mobile .carousel-content {
  position: relative;
  top: -20vw;
}
#main-slider .slider-inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  color: #fff;
}
#main-slider .slider-inner h2 {
  margin-top: 180px;
  font-size: 36px;
  line-height: 1;
  text-transform: uppercase;
  color: #fff;
}
#main-slider .slider-inner h2 > span {
  color: #45aed6;
}
#main-slider .slider-inner .btn {
  margin-top: 10px;
}
#main-slider .owl-prev,
#main-slider .owl-next {
  position: absolute;
  top: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
  font-size: 30px;
  display: inline-block;
  margin-top: -35px;
  height: 70px;
  width: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 100px;
  z-index: 5;
  -webkit-transition: background-color 400ms;
  transition: background-color 400ms;
}
#main-slider .owl-prev:hover,
#main-slider .owl-next:hover {
  background-color: #45aed6;
}
#main-slider .owl-prev {
  left: -35px;
  text-indent: 14px;
}
#main-slider .owl-next {
  right: -35px;
  text-indent: -14px;
}
#bar {
  width: 0%;
  max-width: 100%;
  height: 4px;
  background: #45aed6;
}
#progressBar {
  margin-top: -4px;
  position: relative;
  z-index: 999;
  width: 100%;
  background: rgba(0, 0, 0, 0.1);
}
#cta {
  padding: 50px 0;
  background-color: #eeeeee;
}
#cta h2 {
  margin-top: 0;
}
#cta .btn {
  margin-top: 40px;
}
#cta2 {
  background: #242a33 url(../images/cta2/cta2-bg.jpg) no-repeat 50% 50%;
  background-size: cover;
  color: #fff;
  padding-top: 70px;
}
#cta2 .btn {
  margin-top: 10px;
}
#cta2 h2 {
  color: #fff;
  font-size: 44px;
  line-height: 1;
}
#cta2 h2 > span {
  color: #45aed6;
}
#features {
  padding: 100px 0;
}
#features .media.service-box:first-child {
  margin-top: 80px;
}
#services {
  padding: 100px 0 75px;
}
.media.service-box {
  margin: 25px 0;
}
.media.service-box .pull-left {
  margin-right: 20px;
}
.media.service-box .pull-left > i {
  font-size: 24px;
  height: 64px;
  line-height: 64px;
  text-align: center;
  width: 64px;
  border-radius: 100%;
  color: #45aed6;
  box-shadow: inset 0 0 0 1px #d7d7d7;
  -webkit-box-shadow: inset 0 0 0 1px #d7d7d7;
  transition: background-color 400ms, background-color 400ms;
  position: relative;
}
.media.service-box .pull-left > i:after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  margin-top: -10px;
  right: -10px;
  border: 4px solid #fff;
  border-radius: 20px;
  background: #45aed6;
}
.media.service-box:hover .pull-left > i {
  background-image: -moz-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -webkit-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -ms-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  color: #fff;
  box-shadow: inset 0 0 0 5px rgba(255, 255, 255, 0.8);
  -webkit-box-shadow: inset 0 0 0 5px rgba(255, 255, 255, 0.8);
}
#portfolio {
  padding: 100px 0;
  background: #f5f5f5;
}
#portfolio .portfolio-filter {
  list-style: none;
  padding: 0;
  margin: 0 0 50px;
  display: inline-block;
}
#portfolio .portfolio-filter > li {
  float: left;
  display: block;
}
#portfolio .portfolio-filter > li a {
  display: block;
  padding: 7px 15px;
  background: #e5e5e5;
  color: #64686d;
  position: relative;
}
#portfolio .portfolio-filter > li a:hover,
#portfolio .portfolio-filter > li a.active {
  background: #45aed6;
  color: #fff;
  box-shadow: 0 -3px 0 0 #318daf inset;
  -webkit-box-shadow: 0 -3px 0 0 #318daf inset;
}
#portfolio .portfolio-filter > li a.active:after {
  content: " ";
  position: absolute;
  bottom: -17px;
  left: 50%;
  margin-left: -10px;
  border-width: 10px;
  border-style: solid;
  border-color: #318daf transparent transparent transparent;
}
#portfolio .portfolio-filter > li:first-child a {
  border-radius: 4px 0 0 4px;
}
#portfolio .portfolio-filter > li:last-child a {
  border-radius: 0 4px 4px 0;
}
#portfolio .portfolio-items {
  margin: -15px;
}
#portfolio .portfolio-item {
  width: 24.9%;
  float: left;
  padding: 15px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}
#portfolio .portfolio-item .portfolio-item-inner {
  position: relative;
}
#portfolio .portfolio-item .portfolio-item-inner .portfolio-info {
  opacity: 0;
  transition: opacity 400ms;
  -webkit-transition: opacity 400ms;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
}
#portfolio .portfolio-item .portfolio-item-inner .portfolio-info h3 {
  font-size: 16px;
  line-height: 1;
  margin: 0;
  color: #fff;
}
#portfolio .portfolio-item .portfolio-item-inner .portfolio-info .preview {
  position: absolute;
  top: -18px;
  right: 12px;
  border-radius: 50%;
  background-image: -moz-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -webkit-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -ms-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #fff;
  box-shadow: 0 0 0 2px #fff;
  -webkit-box-shadow: 0 0 0 2px #fff;
}
#portfolio .portfolio-item:hover .portfolio-info {
  opacity: 1;
}
/* Start: Recommended Isotope styles */
/**** Isotope Filtering ****/
.isotope-item {
  z-index: 2;
}
.isotope-hidden.isotope-item {
  pointer-events: none;
  z-index: 1;
}
/**** Isotope CSS3 transitions ****/
.isotope,
.isotope .isotope-item {
  -webkit-transition-duration: 0.8s;
  -moz-transition-duration: 0.8s;
  -ms-transition-duration: 0.8s;
  -o-transition-duration: 0.8s;
  transition-duration: 0.8s;
}
.isotope {
  -webkit-transition-property: height, width;
  -moz-transition-property: height, width;
  -ms-transition-property: height, width;
  -o-transition-property: height, width;
  transition-property: height, width;
}
.isotope .isotope-item {
  -webkit-transition-property: -webkit-transform, opacity;
  -moz-transition-property: -moz-transform, opacity;
  -ms-transition-property: -ms-transform, opacity;
  -o-transition-property: -o-transform, opacity;
  transition-property: transform, opacity;
}
/**** disabling Isotope CSS3 transitions ****/
.isotope.no-transition,
.isotope.no-transition .isotope-item,
.isotope .isotope-item.no-transition {
  -webkit-transition-duration: 0s;
  -moz-transition-duration: 0s;
  -ms-transition-duration: 0s;
  -o-transition-duration: 0s;
  transition-duration: 0s;
}
/* End: Recommended Isotope styles */
/* disable CSS transitions for containers with infinite scrolling*/
.isotope.infinite-scrolling {
  -webkit-transition: none;
  -moz-transition: none;
  -ms-transition: none;
  -o-transition: none;
  transition: none;
}
#testimonial {
  background: #333333 url(../images/testimonial/bg.jpg) no-repeat 0 0;
  background-size: cover;
  padding: 100px 0;
  color: #fff;
}
#testimonial h4 {
  color: #fff;
  margin-bottom: 0;
}
#testimonial small {
  display: block;
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.7);
}
#testimonial .btns {
  margin-top: 10px;
}
#about {
  padding: 100px 0;
}
#work-process {
  padding: 100px 0 50px;
  background: #20222e url(../images/work-process/bg.jpg) no-repeat 0 0;
  background-size: cover;
  color: #fff;
}
#work-process h2 {
  color: #fff;
}
#work-process h3 {
  color: #fff;
  margin-bottom: 0;
}
#work-process .icon-circle {
  display: inline-block;
  width: 80px;
  height: 80px;
  line-height: 80px;
  border: 2px solid #45aed6;
  border-radius: 100px;
  position: relative;
}
#work-process .icon-circle > span {
  border-style: solid;
  border-width: 2px;
  border-color: #45aed6;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  width: 24px;
  height: 24px;
  line-height: 20px;
  top: -12px;
  color: #64686d;
}
#work-process h3 {
  margin-bottom: 50px;
}
#meet-team {
  padding: 100px 0;
}
#meet-team .team-member {
  padding: 13px;
  background: #eeeeee;
  border: 2px solid #eeeeee;
  transition: border-color 400ms;
  -webkit-transition: border-color 400ms;
}
#meet-team .team-member .team-img {
  margin: -15px -15px 0 -15px;
}
#meet-team .team-member .team-info {
  padding-bottom: 10px;
  border-bottom: 1px solid #dbdbdb;
  margin-bottom: 10px;
}
#meet-team .team-member:hover {
  border-color: #45aed6;
}
#meet-team .team-member:hover .social-icons > li > a {
  background: #45aed6;
}
#meet-team .social-icons {
  list-style: none;
  padding: 0;
  margin: 0;
}
#meet-team .social-icons > li {
  display: inline-block;
}
#meet-team .social-icons > li > a {
  display: block;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  color: #fff;
  background: #222534;
  border-radius: 3px;
}
.divider {
  margin-top: 50px;
  margin-bottom: 50px;
  background-image: -moz-linear-gradient(
    180deg,
    #ffffff 0%,
    #e3e3e3 49%,
    #ffffff 100%
  );
  background-image: -webkit-linear-gradient(
    180deg,
    #ffffff 0%,
    #e3e3e3 49%,
    #ffffff 100%
  );
  background-image: -ms-linear-gradient(
    180deg,
    #ffffff 0%,
    #e3e3e3 49%,
    #ffffff 100%
  );
  width: 95%;
  height: 1px;
}
.progress {
  -webkit-box-shadow: none;
  box-shadow: none;
  background: #eeeeee;
}
.progress .progress-bar.progress-bar-primary {
  background-image: -moz-linear-gradient(4deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -webkit-linear-gradient(4deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -ms-linear-gradient(4deg, #2caab3 0%, #2c8cb3 100%);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.nav.main-tab {
  background: #eee;
  border-bottom: 3px solid #222534;
  border-radius: 3px 3px 0 0;
}
.nav.main-tab > li > a {
  color: #272727;
}
.nav.main-tab > li.active > a {
  background: #222534;
  color: #45aed6;
}
.nav.main-tab > li:first-child > a {
  border-radius: 3px 0 0 0;
}
.nav.main-tab > li:last-child > a {
  border-radius: 0 3px 0 0;
}
.tab-content {
  border: 1px solid #eee;
  border-top: 0;
  padding: 20px 20px 10px;
  border-radius: 0 0 3px 3px;
}
.panel-default {
  border-color: #eee;
}
.panel-default > .panel-heading {
  background-color: #fff;
  border-color: #eee;
}
.panel-default > .panel-heading .panel-title {
  font-size: 14px;
  font-weight: normal;
}
.panel-default > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #eee;
}
#animated-number {
  padding: 100px 0 70px;
  background: #132125 url(../images/animated-number/bg.jpg) no-repeat 0 0;
  background-size: cover;
  color: #fff;
}
#animated-number h1,
#animated-number h2,
#animated-number h3,
#animated-number h4 {
  color: #fff;
}
#animated-number strong {
  display: block;
  margin-bottom: 30px;
}
.animated-number {
  display: inline-block;
  width: 140px;
  height: 140px;
  font-size: 24px;
  line-height: 140px;
  border: 3px solid #fff;
  border-radius: 100px;
  margin-bottom: 20px;
}
#pricing {
  padding: 100px 0 70px;
}
#pricing ul.pricing {
  list-style: none;
  padding: 0;
  margin: 70px 0 30px;
  border: 1px solid #eee;
  border-radius: 5px 5px 4px 4px;
  padding: 15px;
  text-align: center;
}
#pricing ul.pricing li {
  display: block;
  padding: 10px;
}
#pricing ul.pricing li.plan-header {
  background: #eee;
  border-radius: 4px 4px 0 0;
  margin: -15px -15px 10px;
  padding: 15px 15px 30px;
  border: 0;
}
#pricing ul.pricing li.plan-header .price-duration {
  position: relative;
  margin-top: -63px;
  top: -10px;
  display: inline-block;
  width: 116px;
  height: 116px;
  background-image: -moz-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -webkit-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -ms-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  color: #fff;
  border-radius: 100px;
  border: 5px solid #f6f6f6;
}
#pricing ul.pricing li.plan-header .price-duration > span {
  display: block;
  line-height: 1;
}
#pricing ul.pricing li.plan-header .price-duration > span.price {
  font-size: 24px;
  font-weight: 700;
  margin-top: 35px;
}
#pricing ul.pricing li.plan-header .price-duration > span.duration {
  margin-top: 5px;
}
#pricing ul.pricing li.plan-header .plan-name {
  margin-top: 10px;
  font-size: 24px;
  color: #272727;
  font-weight: bold;
  line-height: 1;
  text-transform: uppercase;
}
#pricing ul.pricing.featured {
  background: #222534;
  color: #fff;
  border: 0;
}
#pricing ul.pricing.featured li.plan-header {
  background-image: -moz-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -webkit-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -ms-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
}
#pricing ul.pricing.featured li.plan-header .plan-name {
  color: #fff;
}
#get-in-touch {
  padding: 100px 0 50px;
  background-image: -moz-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -webkit-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  background-image: -ms-linear-gradient(90deg, #2caab3 0%, #2c8cb3 100%);
  color: #fff;
}
#get-in-touch h1,
#get-in-touch h2 {
  color: #fff;
}
#blog {
  padding: 100px 0;
}
#blog .blog-post {
  border: 1px solid #eee;
  padding: 15px;
}
#blog .blog-post .post-format {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 34px;
  text-align: center;
  color: #fff;
  background: #222534;
  border: 3px solid #fff;
  border-radius: 100px;
}
#blog .blog-post.blog-large .entry-thumbnail {
  margin: -15px -15px 15px;
  position: relative;
}
#blog .blog-post.blog-large .post-format {
  width: 66px;
  height: 66px;
  line-height: 60px;
  position: absolute;
  right: 20px;
  bottom: -33px;
}
#blog .blog-post.blog-large .post-format > i {
  font-size: 20px;
}
#blog .blog-post.blog-large .entry-date {
  font-size: 12px;
  display: block;
  margin-bottom: 5px;
  text-transform: uppercase;
  color: #45aed6;
}
#blog .blog-post.blog-large .entry-title {
  margin-top: 0;
  font-size: 18px;
  line-height: 1;
  margin-bottom: 15px;
}
#blog .blog-post.blog-large .entry-title a {
  color: #64686d;
}
#blog .blog-post.blog-large .entry-title a:hover {
  color: #45aed6;
}
#blog .blog-post.blog-media {
  margin-bottom: 30px;
}
#blog .blog-post.blog-media .entry-thumbnail {
  margin: -15px 15px -15px -15px;
  position: relative;
}
#blog .blog-post.blog-media .post-format {
  position: absolute;
  top: 20px;
  right: -10px;
}
#blog .blog-post.blog-media .entry-date {
  font-size: 12px;
  display: block;
  margin-bottom: 5px;
  text-transform: uppercase;
  color: #45aed6;
}
#blog .blog-post.blog-media .entry-title {
  margin-top: 0;
  font-size: 18px;
  line-height: 1;
  margin-bottom: 15px;
}
#blog .blog-post.blog-media .entry-title a {
  color: #64686d;
}
#blog .blog-post.blog-media .entry-title a:hover {
  color: #45aed6;
}
#blog .blog-post .entry-meta {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}
#blog .blog-post .entry-meta > span {
  display: inline-block;
  margin-right: 10px;
  color: #999;
}
#blog .blog-post .entry-meta > span > a {
  color: #999;
}
#blog .blog-post .entry-meta > span > a:hover {
  color: #45aed6;
}
#contact {
  position: relative;
  overflow: hidden;
}
#contact .container-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(69, 174, 214, 0.3);
  z-index: 1;
}
#contact .contact-form {
  background: rgba(255, 255, 255, 0.8);
  padding: 20px;
  margin-top: 50px;
}
#contact .contact-form h3 {
  margin-top: 0;
}
/***********************
********* Footer ******
************************/
#footer {
  padding-top: 30px;
  padding-bottom: 30px;
  color: #fff;
  background: #2e2e2e;
}
#footer a {
  color: #fff;
}
#footer a:hover {
  color: #45aed6;
}
#footer ul {
  list-style: none;
  padding: 0;
  margin: 0 -7.5px;
}
#footer ul > li {
  display: inline-block;
  margin: 0 7.5px;
}
@media only screen and (min-width: 768px) {
  #footer .social-icons {
    float: right;
  }
}
.form-control {
  box-shadow: none;
  -webkit-box-shadow: none;
}