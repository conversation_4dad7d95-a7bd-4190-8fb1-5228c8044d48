{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "vite --host --port 8000", "denorun": "deno run --allow-env --allow-read --allow-sys --allow-ffi --allow-run --allow-write --allow-net npm:vite --host --port 8000", "build": "vite build", "preview": "vite preview --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@simplewebauthn/browser": "^9.0.1", "axios": "^1.7.7", "chart.js": "^2", "crypto-js": "^4.2.0", "exif-js": "^2.3.0", "exifr": "^7.1.3", "material-design-icons-iconfont": "^6.7.0", "moment": "^2.30.1", "ol": "^10.3.1", "qrcode-vue": "^1.2.0", "unplugin-vue-components": "^0.27.4", "v-click-outside": "^3.2.0", "v-tooltip": "^2.1.3", "vue": "^2.7.16", "vue-chartjs": "^3", "vue-router": "^3.6.5", "vue-toast-notification": "^1", "vuetify": "^2.7.2", "vuex": "^3.6.2"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.4", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue2": "^2.3.1", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^9.15.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^9.30.0", "prettier": "^3.3.3", "sass": "^1.81.0", "terser": "^5.36.0", "vite": "^5.4.11", "vite-plugin-pwa": "^0.20.5", "vue-eslint-parser": "^9.4.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false}, "rules": {"vue/no-reserved-component-names": ["off"], "vue/multi-word-component-names": ["off"], "vue/no-unused-components": ["off"]}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}