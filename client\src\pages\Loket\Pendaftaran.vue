<template>
  <div style="display: flex">
    <SidePane
      @item-click="ItemClick"
      statusId=",1,2,3,8,9,"
      :rebind="rebindSidebar"
      :addButton="true"
    />
    <div
      v-show="forms.PermohonanID || forms.PermohonanID === 0"
      class="right-pane col-12 col-lg-10 col-md-9 col-sm-12"
      :class="$api.isMobile() ? '' : 'form-inline'"
      style="
        padding: 20px 20px;
        width: calc(100% - 300px);
        height: calc(100vh - 66px);
        overflow: auto;
        width: ;
      "
    >
      <div
        v-if="versiPerubahan"
        style="
          padding: 10px;
          border: 1px solid red;
          margin-bottom: 20px;
          border-radius: 8px;
          display: flex;
        "
      >
        <v-icon slot="icon" color="warning" size="36" style="margin-left: 10px">
          mdi-history
        </v-icon>
        <div style="padding: 5px; margin-left: 10px">
          Ini adalah tampilan versi sebelumnya ({{
            versiPerubahan | format('DD MMM YYYY HH:mm')
          }})
        </div>
        <v-spacer />
        <v-btn color="primary" text @click="Populate(forms.PermohonanID)">
          Tampilkan Versi Saat Ini
        </v-btn>
      </div>
      <div v-show="forms.BayarStatus == 2" class="dvWarn">
        {{ forms.CatatanKhusus }}
      </div>
      <div
        id="dvRightBox"
        style="
          float: right;
          font-family: raleway;
          background: white;
          margin-top: -20px;
          width: 210px;
        "
      >
        <div style="padding: 10px 20px; text-align: center">
          <div style="font-size: xx-large">{{ forms.NoPengujian || '?' }}</div>
          <div>{{ forms.ShareCode || '-' }}</div>
        </div>
        <v-btn
          text
          small
          color="primary"
          v-show="!forms.NoPengujian"
          style="
            border-top: 1px solid #e3e3e3;
            text-align: center;
            width: 210px;
          "
          @click="BuatNoPengujian"
        >
          Buat No. Pengujian
        </v-btn>
        <v-btn
          text
          small
          color="warning"
          v-show="!forms.BayarStatus"
          style="
            border-top: 1px solid #e3e3e3;
            text-align: center;
            width: 210px;
          "
          @click="BayarDibelakang"
        >
          Bayar Dibelakang
        </v-btn>
        <v-btn
          v-if="!forms.BayarStatus && forms.Kesimpulan == 1"
          text
          small
          color="primary"
          style="
            border-top: 1px solid #e3e3e3;
            text-align: center;
            width: 210px;
          "
          @click="GenerateBilling"
        >
          {{ forms.BillingID ? 'Lihat Kode Billing' : 'Buat Kode Billing' }}
        </v-btn>
        <v-btn
          v-if="!forms.BayarStatus && forms.Kesimpulan == 1"
          text
          small
          color="primary"
          style="
            border-top: 1px solid #e3e3e3;
            text-align: center;
            display: block;
            width: 210px;
          "
          @click="UpdBayar"
        >
          Lakukan Pembayaran
        </v-btn>
        <!-- <div
          v-show="forms.StatusID >= 4 && forms.BayarStatus == 1"
          style="padding: 10px 20px; border-top:1px solid #e3e3e3;
            background: #4CAF50; color:white; text-align: center;"
        >
          <span>Sudah Dibayarkan</span>
        </div> -->
        <v-btn
          v-if="forms.BayarDate"
          small
          color="success"
          style="
            border-top: 1px solid #e3e3e3;
            text-align: center;
            display: block;
            width: 100%;
          "
          @click="DownloadKwitansi"
        >
          Download Kwitansi
        </v-btn>
        <v-btn
          text
          small
          color="primary"
          v-if="forms.StatusID == 8"
          style="
            border-top: 1px solid #e3e3e3;
            text-align: center;
            display: block;
            width: 100%;
          "
          @click="UpdPengambilan"
        >
          Serahkan Sertifikat
        </v-btn>
        <v-btn
          text
          small
          color="primary"
          v-if="forms.StatusID >= 7"
          style="
            border-top: 1px solid #e3e3e3;
            text-align: center;
            display: block;
            width: 100%;
          "
          @click="ResendNotif"
        >
          Kirim Notifikasi
        </v-btn>
        <div
          v-if="forms.StatusID == 9"
          style="
            padding: 10px 20px;
            border-top: 1px solid #e3e3e3;
            background: #8bc34a;
            color: white;
            text-align: center;
          "
        >
          <span>Sudah Diserahkan</span>
        </div>
        <v-btn
          text
          small
          v-if="forms.StatusID >= 7"
          color="primary"
          style="
            border-top: 1px solid #e3e3e3;
            text-align: center;
            display: block;
            width: 100%;
          "
          @click="showSertifikat = true"
        >
          Lihat Sertifikat
        </v-btn>
      </div>
      <Input
        type="text"
        label="Nama Pelanggan"
        :value.sync="forms.Nama"
        width="600px"
      />
      <TextArea label="Alamat" :value.sync="forms.Alamat" width="600px" />
      <Input
        type="text"
        label="No. Ponsel"
        :value.sync="forms.Phone"
        width="600px"
      />
      <Input
        type="text"
        label="Email"
        :value.sync="forms.Email"
        width="600px"
      />
      <Select
        :items="jenis"
        label="Nama/Jenis Contoh"
        :value.sync="forms.JenisID"
        width="600px"
      />
      <DatePicker
        label="Tanggal Masuk"
        :value.sync="forms.TglMasuk"
        style="padding-top: 4px"
      />
      <TextArea
        label="Kegiatan/Paket Pekerjaan"
        :value.sync="forms.NamaKegiatan"
        width="600px"
        height="100px"
      />
      <Label label="Sumber/SatKer">
        <Select :items="satker" :value.sync="forms.SumberDana" width="300px" />
        <Input
          v-if="['1', '3', '4'].includes(forms.SumberDana) || satkerNotExist"
          type="text"
          placeholder="Satker / Kab / Kota"
          :value.sync="forms.SatKer"
          style="margin-left: 5px"
          width="295px"
        />
        <Select
          v-else-if="forms.SumberDana == 2"
          :items="bmck"
          :value.sync="forms.SatKer"
          style="margin-left: 5px"
          width="295px"
          :placeholder="forms.SatKer"
        />
      </Label>
      <Label label="Surat Permohonan/Tgl.">
        <Input
          type="text"
          :value.sync="forms.SuratNo"
          placeholder="No. Surat"
          width="300px"
        />
        <DatePicker
          :value.sync="forms.SuratTgl"
          style="margin-left: 5px"
          width="295px"
        />
      </Label>
      <Label label="Surat Permohonan">
        <Uploader :value.sync="forms.SuratUrl" :key="rebindUpload">
          <template v-slot="{ opener, fileName }">
            <v-btn
              small
              text
              outlined
              v-show="fileName"
              style="margin-right: 8px"
              @click="Download(forms.SuratUrl)"
              >{{ fileName || forms.SuratUrl }}</v-btn
            >
            <v-btn small @click="opener">
              <v-icon>mdi-upload</v-icon>
            </v-btn>
          </template>
        </Uploader>
      </Label>
      <Grid
        :datagrid.sync="datagrid"
        :dbref="dbref"
        :dbparams="dbparams"
        :disabled="true"
        groupBy="NamaPaket"
        :columns="[
          {
            name: 'Parameter Uji',
            value: 'NamaParameter',
          },
          {
            name: 'Nama Contoh',
            value: 'NamaContoh',
            width: '300px',
          },
          {
            name: 'Jml',
            value: 'JmlContoh',
          },
          {
            name: 'Metode',
            value: 'Metode',
          },
          {
            name: 'Harga',
            value: 'Harga',
            class: 'align-right',
          },
          {
            name: '',
            value: 'Delete',
          },
        ]"
      >
        <template v-slot:group-row="{ row, columns }">
          <td :colspan="columns.length" style="background: #f3f3f3">
            <div style="padding: 0; display: flex">
              <div style="font-weight: bold">
                {{ row.NamaPaket || '(NON PAKET)' }}
              </div>
            </div>
          </td>
        </template>
        <template v-slot:row-NamaContoh="{ row }">
          <TextArea
            width="300px"
            height="auto"
            :value.sync="row.NamaContoh"
            placeholder="(asal/ukuran contoh)"
          />
        </template>
        <template v-slot:row-JmlContoh="{ row }">
          <Input
            type="number"
            :value.sync="row.JmlContoh"
            placeholder="Jml"
            width="60px"
          />
        </template>
        <template v-slot:row-Harga="{ row }">
          {{ (row.Harga * row.JmlContoh) | format }}
        </template>
        <template v-slot:row-Delete="{ idx }">
          <v-icon color="error" @click="DelParameter(idx)"
            >mdi-trash-can</v-icon
          >
        </template>
        <template v-slot:footer>
          <tr>
            <td colspan="4">
              <v-btn text small @click="paramUji.show = true">
                <v-icon left>mdi-plus-circle</v-icon>
                Tambah Parameter Uji
              </v-btn>
            </td>
            <td
              class="align-right"
              style="font-weight: bold; padding: 8px 12px"
            >
              {{
                datagrid.reduce((res, d) => {
                  return res + d.Harga * d.JmlContoh
                }, 0) | format
              }}
            </td>
            <td></td>
          </tr>
        </template>
      </Grid>
      <br />
      <br />
      <div style="display: flex">
        <v-btn color="primary" @click="OpenReport($event)">
          <v-icon>mdi-printer</v-icon>
        </v-btn>
        <v-btn color="primary" style="margin-left: 5px" @click="Save(false)">
          SIMPAN
        </v-btn>
        <!-- <v-btn color="primary" text outlined style="margin-left:5px" v-show="!forms.PermohonanID" @click="Save(true)">
          SIMPAN & DAFTAR LAGI
        </v-btn> -->
        <v-btn
          color="primary"
          text
          outlined
          style="margin-left: 5px"
          v-show="forms.PermohonanID"
          @click="showHistory = true"
        >
          <v-icon>mdi-history</v-icon>
        </v-btn>
        <v-spacer />
        <v-btn
          color="error"
          style="margin-left: 5px"
          @click="deletion.show = true"
        >
          HAPUS
        </v-btn>
      </div>
    </div>
    <ReportPopup
      v-show="showReport"
      :reportUrl="reportUrl"
      v-click-outside="CloseReport"
    />
    <ParameterUji
      :forms="paramUji"
      :jenisId="forms.JenisID"
      @submit="AddParameter"
    />
    <Modal title="Pembatalan" :show.sync="deletion.show" @submit="Delete">
      <TextArea
        label="Alasan Pembatalan"
        :value.sync="forms.Keterangan"
        width="300px"
      />
    </Modal>
    <Bayar :forms.sync="billing" @refresh="$emit('refresh')"> </Bayar>
    <Sertifikat
      :show.sync="showSertifikat"
      :permohonanId="forms.PermohonanID"
    ></Sertifikat>
    <History
      :show.sync="showHistory"
      :permohonanId="forms.PermohonanID"
      @apply="applyData"
    ></History>
  </div>
</template>
<script>
import ReportPopup from '../ReportPopup.vue'
import SidePane from './SidePane.vue'
import ParameterUji from './ParameterUji.vue'
import Bayar from '../User/Dashboard/Bayar.vue'
import Sertifikat from '../Sertifikasi/History.vue'
import History from './History.vue'
import moment from 'moment'
export default {
  components: {
    SidePane,
    ParameterUji,
    ReportPopup,
    Bayar,
    Sertifikat,
    History,
  },
  data: () => ({
    datagrid: [],
    rebindSidebar: 0,
    rebindUpload: 0,
    dbref: 'UJI_SelPermohonanDet',
    dbparams: { PermohonanID: 0 },
    forms: {},
    billing: {},
    deletion: { show: false },
    paramUji: {
      show: false,
    },
    showReport: false,
    showSertifikat: false,
    showHistory: false,
    daftarLagi: false,
    versiPerubahan: null,
    jenis: [
      { val: 'A', txt: 'Mutu Air & Lingkungan' },
      { val: 'B', txt: 'Bahan Bangunan' },
      { val: 'Ba', txt: 'Aspal' },
      { val: 'T', txt: 'Tanah (Geoteknik)' },
    ],
    satker: [
      { val: '1', txt: 'APBN' },
      { val: '2', txt: 'APBD I - BMCK' },
      { val: '3', txt: 'APBD I - NON BMCK' },
      { val: '4', txt: 'APBD II' },
      { val: '5', txt: 'Swasta' },
      { val: '6', txt: 'Perorangan' },
      { val: '7', txt: 'Lainnya' },
    ],
    bmck: [
      { val: 'RANCANG BANGUN', txt: 'RANCANG BANGUN' },
      { val: 'WILAYAH TIMUR', txt: 'WILAYAH TIMUR' },
      { val: 'WILAYAH BARAT', txt: 'WILAYAH BARAT' },
      { val: 'SARANA PRASARANA', txt: 'SARANA PRASARANA' },
      { val: 'BPJ SEMARANG', txt: 'BPJ SEMARANG' },
      { val: 'BPJ PATI', txt: 'BPJ PATI' },
      { val: 'BPJ PURWODADI', txt: 'BPJ PURWODADI' },
      { val: 'BPJ SURAKARTA', txt: 'BPJ SURAKARTA' },
      { val: 'BPJ MAGELANG', txt: 'BPJ MAGELANG' },
      { val: 'BPJ WONOSOBO', txt: 'BPJ WONOSOBO' },
      { val: 'BPJ CILACAP', txt: 'BPJ CILACAP' },
      { val: 'BPJ TEGAL', txt: 'BPJ TEGAL' },
      { val: 'BPJ PEKALONGAN', txt: 'BPJ PEKALONGAN' },
    ],
  }),
  computed: {
    reportUrl() {
      if (this.showReport) {
        if (this.forms.StatusID >= 3) {
          return '/reports/uji/permohonan-paid/' + this.forms.PermohonanID
        }
        return '/reports/uji/permohonan/' + this.forms.PermohonanID
      } else {
        return '/tunggu-sebentar'
      }
    },
    satkerNotExist() {
      return (
        this.forms.SumberDana == 2 &&
        this.forms.SatKer &&
        !this.bmck.map((v) => v.val).includes(this.forms.SatKer)
      )
    },
  },
  methods: {
    async ItemClick(val) {
      this.Populate(val.PermohonanID)
    },
    async Download(val) {
      this.$api.download(this.$api.url + val, true)
    },
    async Populate(id) {
      this.rebindUpload++
      this.dbref = 'UJI_SelPermohonanDet'
      this.dbparams = { PermohonanID: id }
      this.versiPerubahan = null
      var ret = await this.$api.call('UJI.SelPermohonan', {
        PermohonanID: id,
      })
      if (ret.data.length) {
        this.forms = ret.data[0]
      } else {
        this.forms = { PermohonanID: 0 }
      }
    },
    applyData(row) {
      this.forms = row
      this.versiPerubahan = row.CreatedAt
      this.dbref = 'UJI_SelPermohonanDetHist'
      this.dbparams = {
        PermohonanID: row.PermohonanID,
        CreatedAt: row.CreatedAt,
      }
    },
    async Save() {
      var res = await this.$api.call('UJI.SavPermohonan', {
        ...this.forms,
        XmlPermohonanDet: this.datagrid,
      })
      if (res.success) {
        this.rebindSidebar++
        if (this.forms.PermohonanID) this.Populate(this.forms.PermohonanID)
      }
    },
    async BuatNoPengujian() {
      await this.$api.call('UJI_UpdNoPengujian', {
        PermohonanID: this.forms.PermohonanID,
      })
      this.Populate(this.forms.PermohonanID)
    },
    async BayarDibelakang() {
      if (
        !confirm('Ini hanya untuk pengujian yg dibayar dibelakang, anda yakin?')
      )
        return
      await this.$api.call('UJI_UpdCatatanKhusus', {
        PermohonanID: this.forms.PermohonanID,
        CatatanKhsusus: 'Belum Dibayarkan',
      })
      this.Populate(this.forms.PermohonanID)
    },
    async UpdBayar() {
      if (!confirm('Anda yakin melakukan pembayaran?')) return
      let ret = await this.$api.call('UJI_UpdBayar', {
        PermohonanID: this.forms.PermohonanID,
        BayarStatus: 1,
      })
      if (ret.success) this.Populate(this.forms.PermohonanID)
    },
    async UpdPengambilan() {
      // $A.Page.Redirect("#/Main/Loket/Feedback");
    },
    async Delete() {
      var res = await this.$api.call('UJI_DelPermohonan', this.forms)
      if (res.success) {
        this.deletion.show = false
        this.forms.PermohonanID = 0
        this.Populate(this.forms.PermohonanID)
        this.rebindSidebar++
      }
    },
    async ResendNotif() {
      let res = await this.$api.post('/reports/uji/resend', {
        PermohonanID: this.forms.PermohonanID,
      })
      if (res.success) {
        this.$api.notify(res.message, 'success')
      } else {
        this.$api.notify(res.message, 'error')
      }
    },
    async GenerateBilling() {
      let ret = await this.$api.call('UJI_SelBilling', this.forms)
      if (ret.success) {
        if (!ret.data.length) {
          ret = await this.$api.call('UJI_SavBillingID', this.forms)
        }
        this.billing = {
          show: true,
          step: 1,
          total: ret.data[0].TotalBayar,
          billingId: ret.data[0].BillingID,
          expiredDate: moment(ret.data[0].ExpiredDate)
            .add(7, 'hour')
            .format('DD-MMM-YYYY HH:mm:ss'),
          PaymentType: 'transfer',
        }
        this.Populate(this.forms.PermohonanID)
        this.rebindSidebar++
      }
    },
    async DownloadKwitansi() {
      this.$api.prompt(this, {
        title: 'Tujuan Pembayaran',
        type: 'textarea',
        onsave: this.OpenKwitansi,
      })
      // let keterangan = prompt('Tujuan Pembayaran:', this.forms.Kwitansi)
      // if(keterangan) {
      //   this.forms.Kwitansi = keterangan
      //   this.$api.call('UJI_SavKwitansi', this.forms)
      //   // this.$api.download(this.$api.url + '/report/uji/kwitansi/' + this.forms.PermohonanID)
      //   window.open(this.$api.url + '/reports/uji/kwitansi/' + this.forms.PermohonanID +'?keterangan='+keterangan, '_blank')
      // }
    },
    OpenKwitansi(keterangan) {
      if (keterangan) {
        this.forms.Kwitansi = keterangan
        this.$api.call('UJI_SavKwitansi', this.forms)
        window.open(
          this.$api.url +
            '/reports/uji/kwitansi/' +
            this.forms.PermohonanID +
            '?keterangan=' +
            keterangan,
          '_blank'
        )
      }
    },
    AddParameter(params) {
      this.datagrid.push(...params)
      this.paramUji.show = false
    },
    DelParameter(idx) {
      this.datagrid.splice(idx, 1)
    },
    OpenReport() {
      setTimeout(() => {
        this.showReport = true
      }, 100)
    },
    CloseReport() {
      this.showReport = false
    },
  },
}
</script>
<style lang="scss" scoped>
.dvWarn {
  position: absolute;
  top: -48px;
  color: white;
  background: red;
  padding: 5px 10px;
  border-radius: 5px;
}
</style>
