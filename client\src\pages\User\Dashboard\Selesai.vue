<template>
  <div>
    <v-list>
      <List
        v-show="datalist.length"
        dbref="UJI_SelPermohonanList"
        style="height: calc(100vh - 136px)"
        :items.sync="datalist"
        :dbparams="dbparams"
        :rebind="rebind"
        :selectOnLoad="true"
      >
        <template v-slot="{ row }">
          <v-list-item>
            <v-list-item-content>
              <v-list-item-title>{{ row.NamaPelanggan }}</v-list-item-title>
              <v-list-item-subtitle>
                <span v-if="row.LkNama">{{
                  row.LkNama.replace(/\.pdf$/, '')
                }}</span>
                <span v-else>{{ row.NoPengujian }}</span>
                | {{ row.JenisUji }}
              </v-list-item-subtitle>
              <v-list-item-subtitle style="font-weight: bold">
                SELESAI
              </v-list-item-subtitle>
            </v-list-item-content>
            <v-list-item-action>
              <v-btn
                small
                outlined
                color="primary"
                v-if="row.LkSignedUrl"
                @click="ShowLembarKerja(row)"
              >
                <v-icon>mdi-file-cad</v-icon>
              </v-btn>
            </v-list-item-action>
            <v-list-item-action>
              <v-btn
                small
                outlined
                color="primary"
                v-if="row.SignedUrl"
                @click="ShowSertifikat(row)"
              >
                <v-icon>mdi-certificate</v-icon>
              </v-btn>
            </v-list-item-action>
          </v-list-item>
        </template>
      </List>
    </v-list>
    <Bayar :forms.sync="billing"> </Bayar>
  </div>
</template>
<script>
import Bayar from './Bayar.vue'
export default {
  components: {
    Bayar,
  },
  data: () => ({
    billing: {
      show: false,
      PaymentType: 'cash',
      PermohonanID: '',
      TotalBayar: 0,
      step: 0,
    },
    datalist: [],
    statusId: '4,5,6,7,8',
    rebind: 1,
  }),
  computed: {
    dbparams() {
      return { StatusID: this.statusId }
    },
  },
  watch: {
    'billing.show'(val) {
      if (!val) {
        this.rebind++
        this.billing.TotalBayar = 0
      }
    },
  },
  methods: {
    async ShowLembarKerja(row) {
      window.open(this.$api.url + '/' + row.LkSignedUrl, '_blank')
    },
    async ShowSertifikat(row) {
      window.open(
        this.$api.url + row.SignedUrl.replace(/xlsx/, 'pdf'),
        '_blank'
      )
    },
  },
}
</script>
