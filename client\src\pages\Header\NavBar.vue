<template>
  <div class="nav" id="navbar">
    <nav class="nav__container">
      <div>
        <a href="#" class="nav__link nav__logo" style="display: flex">
          <img src="/imgs/logo-s.png" width="40px" />
          <span class="nav__logo-name">SILAKON</span>
          <v-spacer />
          <v-icon @click="CloseMenu" color="error" class="close-navbar">
            mdi-close
          </v-icon>
        </a>

        <div class="nav__list">
          <div class="nav__items">
            <h3 class="nav__subtitle">LAYANAN PENGUJIAN</h3>
            <template v-for="(m, idx) in menu">
              <div class="nav__dropdown" :key="idx" v-if="m.child.length">
                <a
                  href="javascript:void(0)"
                  class="nav__link"
                  @click="$router.push(m.MenuUrl)"
                >
                  <i
                    class="bx nav__icon"
                    :class="iconMap[m.MenuIcon] || m.MenuIcon"
                  ></i>
                  <span class="nav__name">{{ m.MenuName }}</span>
                  <i
                    class="bx bx-chevron-down nav__icon nav__dropdown-icon"
                  ></i>
                </a>

                <div class="nav__dropdown-collapse">
                  <div class="nav__dropdown-content">
                    <a
                      href="javascript:void(0)"
                      class="nav__dropdown-item"
                      v-for="(sm, sidx) in m.child"
                      :key="sidx"
                      v-show="m.MenuUrl !== 'component'"
                      @click="$router.push(sm.MenuUrl)"
                      >{{ sm.MenuName }}</a
                    >
                  </div>
                </div>
              </div>
              <a
                v-else
                href="javascript:void(0)"
                class="nav__link"
                v-show="m.MenuUrl !== 'component'"
                :key="'x' + idx"
                @click="$router.push(m.MenuUrl)"
              >
                <i
                  class="bx nav__icon"
                  :class="iconMap[m.MenuIcon] || m.MenuIcon"
                ></i>
                <span class="nav__name">{{ m.MenuName }}</span>
              </a>
            </template>
          </div>
          <div class="nav__items">
            <h3 class="nav__subtitle">PROFILE</h3>
            <template v-for="(m, idx) in menuProfile">
              <div class="nav__dropdown" :key="idx" v-if="m.child.length">
                <a
                  href="javascript:void(0)"
                  class="nav__link"
                  @click="$router.push(m.MenuUrl)"
                >
                  <i
                    class="bx nav__icon"
                    :class="iconMap[m.MenuIcon] || m.MenuIcon"
                  ></i>
                  <span class="nav__name">{{ m.MenuName }}</span>
                  <i
                    class="bx bx-chevron-down nav__icon nav__dropdown-icon"
                  ></i>
                </a>

                <div class="nav__dropdown-collapse">
                  <div class="nav__dropdown-content">
                    <a
                      href="#"
                      class="nav__dropdown-item"
                      v-for="(sm, sidx) in m.child"
                      :key="sidx"
                      v-show="m.MenuUrl !== 'component'"
                      @click.stop="MenuProfileClick(sm.MenuUrl)"
                    >
                      {{ sm.MenuName }}
                    </a>
                  </div>
                </div>
              </div>
              <a
                v-else
                href="#"
                class="nav__link"
                v-show="m.MenuUrl !== 'component'"
                @click="MenuProfileClick(sm.MenuUrl)"
                :key="'y' + idx"
              >
                <i class="bx bx-home nav__icon"></i>
                <span class="nav__name">{{ m.MenuName }}</span>
              </a>
            </template>
          </div>
          <div class="nav__items">
            <a
              href="#"
              class="nav__link"
              @click="$router.push('/Main/App/Logout')"
            >
              <i class="bx bx-log-out nav__icon"></i>
              <span class="nav__name">Log Out</span>
            </a>
          </div>
        </div>
      </div>

      <!-- <a
        href="#"
        class="nav__link nav__logout"
        @click="$router.push('/Main/App/Logout')"
      >
        <i class="bx bx-log-out nav__icon"></i>
        <span class="nav__name">Log Out</span>
      </a> -->
    </nav>
    <ChangePassword :show.sync="showChangePassword" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import ChangePassword from '../Admin/ChangePassword.vue'
export default {
  components: {
    ChangePassword,
  },
  data: () => ({
    showChangePassword: false,
    iconMap: {
      home: 'bx-home',
      'address-book-o': 'bx-receipt',
      users: 'bxs-user-detail',
      flask: 'bxs-flask',
      cogs: 'bx-cog',
      cash: 'bx-money',
      pen: 'bx-pen',
      'file-text-o': 'bx-medal',
      'bar-chart': 'bxs-report',
      'question-circle': 'bx-help-circle',
    },
  }),
  computed: {
    ...mapGetters({
      menu: 'getMenu',
      menuProfile: 'getMenuProfile',
      user: 'getUser',
    }),
  },
  methods: {
    CloseMenu() {
      let $n = document.getElementById('navbar')
      $n.style.left = '-100%'
    },
    MenuProfileClick(url) {
      console.log(url)
      if (url.match(/ChangePassword/)) {
        this.showChangePassword = true
      } else {
        this.$router.push(url)
      }
    },
  },
}
</script>
<style lang="scss">
.close-navbar {
  display: none !important;
}
.is-mobile {
  .close-navbar {
    display: block !important;
  }
}
</style>
