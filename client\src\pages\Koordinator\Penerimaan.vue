<template>
  <div style="display: flex">
    <SidePane
      @item-click="ItemClick"
      statusId=",1,2,3,4,"
      :rebind="rebindSidebar"
    />
    <div
      v-show="forms.PermohonanID"
      class="right-pane col-12 col-lg-10 col-md-9 col-sm-12"
      :class="$api.isMobile() ? '' : 'form-inline'"
      style="
        padding: 20px 20px;
        width: calc(100vw - 368px);
        overflow: auto;
        height: calc(100vh - 66px);
      "
    >
      <div v-show="forms.BayarStatus == 2" class="dvWarn">
        {{ forms.CatatanKhusus }}
      </div>
      <div
        id="dvRightBox"
        style="
          float: right;
          font-family: raleway;
          background: white;
          margin-top: -20px;
        "
      >
        <div style="padding: 10px 20px; text-align: center">
          <div style="font-size: xx-large">{{ forms.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> }}</div>
          <div>{{ forms.ShareCode }}</div>
        </div>
        <div
          v-show="forms.StatusID >= 4 && forms.BayarStatus == 1"
          style="
            padding: 10px 20px;
            border-top: 1px solid #e3e3e3;
            background: #4caf50;
            color: white;
            text-align: center;
          "
        >
          <span>Sudah Dibayarkan</span>
        </div>
        <div
          v-show="forms.StatusID == 9"
          style="
            padding: 10px 20px;
            border-top: 1px solid #e3e3e3;
            background: #8bc34a;
            color: white;
            text-align: center;
          "
        >
          <span>Sudah Diserahkan</span>
        </div>
      </div>
      <Input
        type="text"
        label="Nama Pelanggan"
        :value.sync="forms.Nama"
        width="600px"
      />
      <TextArea label="Alamat" :value.sync="forms.Alamat" width="600px" />
      <Input
        type="text"
        label="No. Ponsel"
        :value.sync="forms.Phone"
        width="600px"
      />
      <Select
        :items="jenis"
        label="Nama/Jenis Contoh"
        :value.sync="forms.JenisID"
        width="600px"
      />
      <DatePicker
        label="Tanggal Masuk"
        :value.sync="forms.TglMasuk"
        style="padding-top: 4px"
      />
      <TextArea
        label="Kegiatan/Paket Pekerjaan"
        :value.sync="forms.NamaKegiatan"
        width="600px"
      />
      <Label label="Sumber/SatKer">
        <Select :items="satker" :value.sync="forms.SumberDana" width="300px" />
        <Input
          type="text"
          placeholder="Satker / Kab / Kota"
          :value.sync="forms.SatKer"
          style="margin-left: 5px"
          v-show="forms.SumberDana < 5"
          width="295px"
        />
      </Label>
      <Label label="Surat Permohonan/Tgl.">
        <Input
          type="text"
          :value.sync="forms.SuratNo"
          placeholder="No. Surat"
          width="300px"
        />
        <DatePicker
          :value.sync="forms.SuratTgl"
          style="margin-left: 5px"
          width="295px"
        />
      </Label>
      <Label label="Surat Permohonan">
        <Uploader :value.sync="forms.SuratUrl" :key="rebindUpload">
          <template v-slot="{ opener, fileName }">
            <v-btn
              small
              text
              outlined
              v-show="fileName"
              style="margin-right: 8px"
              @click="Download(forms.SuratUrl)"
              >{{ fileName || forms.SuratUrl }}</v-btn
            >
            <v-btn small @click="opener">
              <v-icon>mdi-upload</v-icon>
            </v-btn>
          </template>
        </Uploader>
      </Label>
      <Grid
        :datagrid.sync="datagrid"
        dbref="UJI_SelPermohonanDet"
        :dbparams="dbparams"
        :disabled="true"
        groupBy="NamaPaket"
        :columns="[
          {
            name: 'Parameter Uji',
            value: 'NamaParameter',
          },
          {
            name: 'Jml',
            value: 'JmlContoh',
          },
          {
            name: 'Metode',
            value: 'Metode',
          },
          {
            name: 'Waktu',
            value: 'Waktu',
          },
          {
            name: 'Keterangan',
            value: 'Keterangan',
          },
        ]"
      >
        <template v-slot:group-row="{ row, columns }">
          <td :colspan="columns.length" style="background: #f3f3f3">
            <div style="padding: 0; display: flex">
              <div style="font-weight: bold">
                {{ row.NamaPaket || '(NON PAKET)' }}
              </div>
            </div>
          </td>
        </template>
        <template v-slot:row-NamaParameter="{ row }">
          <div style="display: flex">
            <Checkbox
              :value.sync="row.IsVerified"
              style="position: relative; top: -2px"
            />
            <div style="margin-left: 10px; position: relative; top: 2px">
              <div
                style="font-size: 14px"
                :class="!row.IsVerified ? 'coret' : ''"
              >
                {{ row.NamaParameter }}
              </div>
              <div v-show="row.NamaContoh" style="color: gray">
                {{ row.NamaContoh }}
              </div>
            </div>
          </div>
        </template>
        <template v-slot:row-Keterangan="{ row }">
          <Input type="text" :value.sync="row.Keterangan" width="300px" />
        </template>
      </Grid>
      <div id="dvBP2" class="form-inline">
        <div
          style="
            font-weight: bold;
            font-family: Raleway;
            margin-bottom: 5px;
            margin-top: 10px;
          "
        >
          PENERIMAAN SAMPLE / CONTOH UJI
        </div>

        <Label label="Jumlah">
          <Checkbox
            :text="forms.KondisiJumlah ? 'Cukup' : 'Tidak Cukup'"
            style="width: 250px"
            :value.sync="forms.KondisiJumlah"
          />
          <Input
            placeholder="Keterangan"
            style="position: relative; top: -2px"
            width="370px"
            :value.sync="forms.KetJumlah"
          />
        </Label>
        <Label label="Kondisi">
          <Checkbox
            :text="forms.KondisiKondisi ? 'Cukup' : 'Tidak Cukup'"
            style="width: 250px"
            :value.sync="forms.KondisiKondisi"
          />
          <Input
            placeholder="Keterangan"
            style="position: relative; top: -2px"
            width="370px"
            :value.sync="forms.KetKondisi"
          />
        </Label>
        <Label label="Tempat Contoh / Wadah">
          <Checkbox
            :text="forms.KondisiWadah ? 'Cukup' : 'Tidak Cukup'"
            style="width: 250px"
            :value.sync="forms.KondisiWadah"
          />
          <Input
            placeholder="Keterangan"
            style="position: relative; top: -2px"
            width="370px"
            :value.sync="forms.KetWadah"
          />
        </Label>
        <TextArea
          placeholder="Catatan Penerimaan"
          :value.sync="forms.KetPenerimaan"
          width="820px"
        />

        <!-- <div
          style="font-weight: bold; font-family: Raleway; margin-bottom: 5px; margin-top:10px;"
        >
          KAJI ULANG PERMINTAAN PENGUJIAN
        </div>
        <Checkbox
          label="Kemampuan SDM"
          :text="forms.KajiSDM ? 'Ya' : 'Tidak'"
          style="width:250px;"
          :value.sync="forms.KajiSDM"
        />
        <Checkbox
          label="Kesesuaian Metode"
          :text="forms.KajiMetode ? 'Ya' : 'Tidak'"
          style="width:250px;"
          :value.sync="forms.KajiMetode"
        />
        <Checkbox
          label="Kemampuan Peralatan"
          :text="forms.KajiPeralatan ? 'Ya' : 'Tidak'"
          style="width:250px;"
          :value.sync="forms.KajiPeralatan"
        />

        <div
          style="font-weight: bold; font-family: Raleway; margin-bottom: 5px; margin-top:10px;"
        >
          KESIMPULAN
        </div>
        <Checkbox
          label="Kesimpulan"
          :text="
            forms.Kesimpulan ? 'DAPAT DILANJUTKAN' : 'TIDAK DAPAT DILANJUTKAN'
          "
          :value.sync="forms.Kesimpulan"
        />
        <Input
          label="Perkiraan Waktu"
          postfix="Hari"
          width="80px"
          :value.sync="forms.Waktu"
        />
        <TextArea
          placeholder="Catatan"
          :value.sync="forms.KetKaji"
          width="630px"
        /> -->
      </div>
      <br />
      <br />
      <div style="display: flex">
        <v-btn color="primary" style="margin-left: 5px" @click="Save">
          SIMPAN
        </v-btn>
        <v-spacer />
        <v-btn text outlined color="warning" v-if="forms.BayarStatus != 1">
          <v-icon left>mdi-alert</v-icon>
          BELUM DIBAYAR
        </v-btn>
        <!-- <v-btn
          color="primary"
          text
          style="margin-left:5px"
          @click="spu.show = true"
        >
          CETAK SURAT PERINTAH UJI
        </v-btn> -->
      </div>
    </div>
    <ReportPopup
      v-if="showReport"
      :reportUrl="reportUrl"
      v-click-outside="CloseReport"
    />
    <Modal
      :show.sync="spu.show"
      title="Surat Perintah Uji"
      submitText="CETAK"
      @submit="CetakSPU"
    >
      <Grid
        :datagrid.sync="spudatagrid"
        dbref="UJI.SpuPenguji"
        :dbparams="dbparams"
        :columns="[
          {
            name: 'Nama Penguji',
            value: 'NamaPenguji',
            width: '200px !important',
            editable: {
              com: 'Select',
              dbref: 'UJI_SelPenguji',
              value: 'PengujiID',
              text: 'NamaPenguji',
              width: '200px !important',
            },
          },
          {
            name: 'Jabatan',
            value: 'Jabatan',
            width: '130px !important',
            editable: {
              com: 'Select',
              items: [
                { val: 'Penyelia', txt: 'Penyelia' },
                { val: 'Teknisi', txt: 'Teknisi' },
              ],
              width: '130px !important',
            },
          },
        ]"
      ></Grid>
    </Modal>
  </div>
</template>
<script>
import ReportPopup from '../ReportPopup.vue'
import SidePane from '../Loket/SidePane.vue'
export default {
  components: {
    SidePane,
    ReportPopup,
  },
  data: () => ({
    datagrid: [],
    dbparams: { PermohonanID: 0 },
    spudatagrid: [],
    rebindSidebar: 0,
    rebindUpload: 0,
    forms: {},
    reportUrl: '',
    showReport: false,
    spu: {
      show: false,
    },
    jenis: [
      { val: 'A', txt: 'Mutu Air & Lingkungan' },
      { val: 'B', txt: 'Bahan Bangunan' },
      { val: 'Ba', txt: 'Aspal' },
      { val: 'T', txt: 'Tanah (Geoteknik)' },
    ],
    satker: [
      { val: '1', txt: 'APBN' },
      { val: '2', txt: 'APBD I - BMCK' },
      { val: '3', txt: 'APBD I - NON BMCK' },
      { val: '4', txt: 'APBD II' },
      { val: '5', txt: 'Swasta' },
      { val: '6', txt: 'Perorangan' },
      { val: '7', txt: 'Lainnya' },
    ],
  }),
  methods: {
    async ItemClick(val) {
      this.rebindUpload++
      this.dbparams = { PermohonanID: val.PermohonanID }
      var ret = await this.$api.call('UJI.SelPermohonan', {
        PermohonanID: val.PermohonanID,
      })
      if (ret.data.length) {
        this.forms = ret.data[0]
      } else {
        this.forms = {}
      }
    },
    async Download(val) {
      this.$api.download(this.$api.url + val, true)
    },
    async Save() {
      if (!confirm('Sudah yakin pada hasilnya?')) return

      let res = this.$api.call('UJI_SavPermohonan', {
        ...this.forms,
        XmlPermohonanDet: this.datagrid,
      })
      if (res.success) this.rebindSidebar++
    },
    async CetakSPU() {
      this.spu.show = false
      this.reportUrl = '/reports/uji/spu/' + this.forms.PermohonanID
      this.showReport = true
    },
    async CloseReport() {
      this.showReport = false
    },
  },
}
</script>
<style lang="scss">
.coret {
  text-decoration: line-through;
}
td {
  .ui-checkbox {
    .--base {
      .--text {
        margin-top: -2px !important;
      }
    }
  }
}
.dvWarn {
  position: absolute;
  top: -48px;
  color: white;
  background: red;
  padding: 5px 10px;
  border-radius: 5px;
}
</style>
