<template>
  <v-col cols="12" lg="2" style="padding: 2px">
    <div
      style="display: flex; background: #ddd; padding: 8px; font-weight: bold"
    >
      <div>{{ title == 'PENGESAHAN' ? 'VERIFIKASI SAMPLE' : title }}</div>
      <v-spacer />
      <div style="padding: 0 8px; background: white; border-radius: 20px">
        {{ items.length }}
      </div>
    </div>
    <List
      :items="items"
      class="view-col"
      :selectOnLoad="true"
      style="margin-top: 5px"
    >
      <template v-slot="{ row }">
        <div style="font-size: 13px; padding: 10px">
          <div style="color: gray; float: right">
            {{ row.TglMasuk | format('DD MMM') }}
          </div>
          <div
            :style="{ color: getRowColor(row) }"
            style="
              font-weight: bold;
              font-family: Raleway;
              text-transform: uppercase;
              overflow: hidden;
              text-overflow: ellipsis;
              width: calc(100% - 60px);
              height: 20px;
              white-space: nowrap;
            "
          >
            {{ row.NamaPelanggan }}
          </div>
          <div
            v-show="row.StatusID >= 8 && row.BayarStatus != 1"
            style="color: gray; float: right"
          >
            {{
              row.StatusID >= 8 && row.BayarStatus != 1
                ? 'BELUM BAYAR'
                : row.StatusName
            }}
          </div>
          <div style="color: gray; display: flex">
            <span class="no-uji">
              {{ row.NoPengujian }}
            </span>
            <span
              v-if="row.LkStatus == 'rejected'"
              style="
                font-size: 10px;
                padding: 3px 5px 0 5px;
                background: red;
                border-radius: 5px;
                color: white;
                margin-right: 5px;
              "
            >
              LK
            </span>
            <span
              v-else-if="row.LkStatus == 'submitted'"
              style="
                font-size: 10px;
                padding: 3px 5px 0 5px;
                background: green;
                border-radius: 5px;
                color: white;
                margin-right: 5px;
              "
            >
              LK
            </span>
            <span
              v-else-if="row.LkStatus == 'signed'"
              style="
                font-size: 10px;
                padding: 3px 5px 0 5px;
                background: #00897b;
                border-radius: 5px;
                color: white;
                margin-right: 5px;
              "
            >
              LK
            </span>
            <div
              v-else
              style="
                overflow: hidden;
                text-overflow: ellipsis;
                width: 160px;
                height: 20px;
                white-space: nowrap;
              "
            >
              {{ row.JenisUji }}
            </div>
          </div>
        </div>
      </template>
    </List>
  </v-col>
</template>
<script>
import moment from 'moment'
export default {
  components: {},
  data: () => ({
    chartYear: new Date().getFullYear(),
    progress: {},
    datacollection: {},
    lastData: {},
    totalPAD: 0,
    chartVal: 0,
    rebind: 1,
  }),
  props: {
    title: String,
    items: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    'items.length'() {
      console.log('il', this.items.length)
      this.rebind++
    },
  },
  computed: {
    yearOptions() {
      const years = []
      for (let i = 0; i < 5; i++) {
        years.push({
          val: new Date().getFullYear() - i,
          txt: new Date().getFullYear() - i,
        })
      }
      return years
    },
  },
  methods: {
    getRowColor(row) {
      return !row.BayarDate && moment().diff(row.TglMasuk, 'day') > 14
        ? 'red'
        : '#333'
    },
  },
}
</script>
<style lang="scss" scoped>
.view-col {
  max-height: 500px;
}
</style>