<template>
  <Modal
    title="Pembayaran"
    :show.sync="forms.show"
    :submitText="submitText[forms.step]"
    @submit="Submit"
  >
    <div style="padding: 5px" v-show="forms.step == 0">
      Pilih metode pembayaran:
      <div style="padding: 10px">
        <Radio
          :value.sync="forms.PaymentType"
          data="cash"
          text="Tunai (datang ke tempat)"
        />
        <Radio
          :value.sync="forms.PaymentType"
          data="transfer"
          text="Transfer (Bank Jateng)"
        />
      </div>
    </div>
    <div style="padding: 5px" v-show="forms.step == 1">
      <div v-show="forms.PaymentType == 'cash'">
        Silahkan datang ke kantor BP2 untuk melakukan pembayaran. <br />
        Permohonan akan tetap dalam status "Belum Dibayar"<br />
        hingga nanti dibayarkan di loket BP2
      </div>
      <div v-show="forms.PaymentType == 'transfer'">
        Silahkan melakukan transfer sejumlah:
        <div
          style="
            font-size: larger;
            background: #f3f3f3;
            padding: 10px 15px;
            border-radius: 5px;
          "
        >
          Rp. {{ forms.total | format }},-
        </div>
        <br />
        <div>ke rekening Bank Jateng (Kode Bank: 113)</div>
        <div>dengan virtual account:</div>
        <div
          style="
            font-size: larger;
            background: #f3f3f3;
            padding: 10px 15px;
            border-radius: 5px;
          "
        >
          {{ forms.billingId }}
        </div>
        <div>
          sebelum:
          <span style="font-weight: bold">{{ forms.expiredDate }}</span>
        </div>
        <div>
          (lihat
          <a href="/web/TataCaraPembayaran.pdf" target="_blank"
            >cara pembayaran</a
          >)
        </div>
      </div>
    </div>
  </Modal>
</template>
<script>
import moment from 'moment'
export default {
  data: () => ({
    submitText: ['BAYAR', 'OK'],
  }),
  props: {
    forms: Object,
  },
  watch: {
    'forms.show'(val) {
      console.log(this.forms.PermohonanID)
      if (!val) this.forms.step = 0
    },
  },
  methods: {
    async Submit() {
      if (this.forms.step == 0) {
        if (this.forms.PaymentType == 'transfer') {
          // alert('akan tersedia dalam waktu dekat')
          let ret = await this.$api.call('UJI_SavBillingID', this.forms)
          if (ret.success) {
            this.forms.billingId = ret.data[0].BillingID
            this.forms.total = ret.data[0].TotalBayar
            this.forms.expiredDate = moment()
              .add(2, 'hour')
              .format('DD-MMM-YYYY HH:mm:ss')
            this.forms.step = 1
          }
        } else {
          this.forms.step = 1
        }
      } else {
        this.forms.show = false
      }
    },
  },
}
</script>
