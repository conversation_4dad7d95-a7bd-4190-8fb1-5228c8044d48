<template>
  <div class="sidepane">
    <div style="padding: 10px; display: flex">
      <Input
        type="text"
        :value.sync="keyword"
        placeholder="Cari .."
        width="270px"
        rightIcon="mdi-magnify"
        class="searchbar"
      />
    </div>
    <div style="height: calc(100% - 47px)">
      <List
        dbref="UJI_SelTandaTanganList"
        :dbparams="dbparams"
        @itemClick="ItemClick"
        :height="addButton ? 'calc(100% - 60px)' : '100%'"
        :rebind="rebind"
        :selectOnLoad="true"
      >
        <template v-slot="{ row }">
          <div style="font-size: 13px; margin-bottom: 10px; padding: 0 10px">
            <div style="color: gray; float: right">
              {{ row.TglMasuk | format }}
            </div>
            <div
              style="
                font-weight: bold;
                font-family: Raleway;
                text-transform: uppercase;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 180px;
                height: 20px;
                white-space: nowrap;
              "
            >
              {{ row.NamaPelanggan }}
            </div>
            <div style="color: gray; float: right">
              {{
                row.StatusID >= 8 && row.BayarStatus != 1
                  ? 'BELUM BAYAR'
                  : row.SignedUrl
                  ? 'SUDAH DITANDATANGAN'
                  : row.StatusName
              }}
            </div>
            <div style="color: gray; display: flex">
              <span
                style="
                  font-size: 10px;
                  padding: 3px 5px 0 5px;
                  background: #ddd;
                  border-radius: 5px;
                  color: #333;
                  margin-right: 5px;
                "
              >
                {{ row.NoPengujian }}
              </span>
              <div
                style="
                  overflow: hidden;
                  text-overflow: ellipsis;
                  width: 160px;
                  height: 20px;
                  white-space: nowrap;
                "
              >
                {{ row.NamaLK || '-' }}
              </div>
            </div>
          </div>
        </template>
      </List>
      <div style="padding: 10px; border-top: 1px solid #ddd" v-if="addButton">
        <v-btn
          outlined
          color="primary"
          style="width: calc(100% - 10px)"
          @click="DaftarBaru"
        >
          <v-icon left>mdi-plus</v-icon>
          DAFTAR BARU
        </v-btn>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data: () => ({
    keyword: '',
  }),
  props: {
    statusId: String,
    rebind: Number,
    addButton: Boolean,
  },
  computed: {
    dbparams() {
      return { IsApproved: 1, Keyword: this.keyword || '' }
    },
  },
  methods: {
    ItemClick(val) {
      this.$emit('item-click', val)
    },
    DaftarBaru() {
      this.$emit('item-click', { PengujianID: 0 })
    },
  },
}
</script>
<style lang="scss">
.sidepane {
  padding: 0px !important;
  width: 300px;
  border-right: 1px solid #ddd;
  /*margin-left: -20px;*/
  height: calc(100vh - 64px);
  overflow: hidden;

  .searchbar {
    margin-bottom: 0;
    input {
      background: transparent !important;
      border-bottom: 0 !important;
    }
  }
}
</style>
