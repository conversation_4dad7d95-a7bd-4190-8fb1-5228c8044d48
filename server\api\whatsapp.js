const db = require('../common/db')
const report = require('../api/reports/report')
const { MessageMedia } = require('whatsapp-web.js');
const path = require('path')

const mod = {
  getPAD: async (message) => {
    const up = await db.exec('XWA_SelUserPhone', { _Phone: message.from })
    if (!up || !up.length) return ''
    if (up[0].RoleID != 1) return ''

    let year = 2023
    let m = message.body.match(/pad\s?(\d+)?/i)
    if (m[1]) year = m[1]
    let d = await db.exec('XWA_GetPAD', { _Tahun: year })
    if (d.length) {
      return `Total PAD Laboratorium Pengujian Tahun ${year} adalah Rp. ${d[0].PAD.toLocaleString()}`
    } else {
      return ''
    }
  },
  getReportPAD: (message) => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve) => {
      const up = await db.exec('XWA_SelUserPhone', { _Phone: message.from })
      if (!up || !up.length) return ''
      if (up[0].RoleID != 1) return ''

      let year = 2023
      let m = message.body.match(/laporan pad\s?(\d+)?/i)
      if (m[1]) year = m[1]
      let d = await report.Generic({
        body: {
          sp: "UJI_RptDetail",
          rptname: "LAPORAN PAD",
          _UJI_SelTahunID: year
        }
      }, {
        header: () => { },
        send: (d) => {
          if (d.data) {
            console.log(d)
            const media = MessageMedia.fromFilePath(path.resolve(d.data.replace(/\/get\//, 'tmp/')))
            resolve(media)
          } else {
            resolve('')
          }
        }
      }, 'xlsx')
    })
  }
}

module.exports = {
  command: async (wa, message) => {
    let reply = ''
    if (message.body.match(/laporan pad\s?(\d{4})?/)) {
      reply = await mod.getReportPAD(message)
    } else if (message.body.match(/pad\s?(\d{4})?/)) {
      reply = await mod.getPAD(message)
    }
    // if (reply)
    //   wa.sendMessage(message.from, reply);
  }
}