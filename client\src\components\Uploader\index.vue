<template>
  <div class="ui-upload" style="position: relative" :id="id">
    <input
      :key="resetFile"
      type="file"
      ref="uploader"
      v-bind="$attrs"
      :multiple="multiple ? 'multiple' : ''"
      @change="previewFiles"
      v-show="false"
    />
    <slot
      :opener="handleOpen"
      :isLoading="loading"
      :uploadedFile="value"
      :fileName="fileName"
    >
      <div
        @click="handleOpen"
        class="imgbox opener dropbox"
        :style="imgboxStyle"
      ></div>
    </slot>
    <VueQrcode
      :id="id + `_qrcode`"
      :value="qrcode"
      v-if="showQRCode"
      :style="{
        width: '150px',
        height: '150px',
        position: 'absolute',
        top: '0px',
        left: '0px',
      }"
    />
    <Popover :targetId="id" on="contextmenu">
      <template>
        <div
          v-for="(item, idx) in opts"
          :key="idx"
          class="ui-dropdown--item popover-close"
          @click="handleItemClick(item)"
        >
          <div class="ui-dropdown--item-inner">
            {{ item.text }}
          </div>
        </div>
      </template>
    </Popover>
  </div>
</template>

<script>
import Popover from '../Forms/Popover.vue'
import VueQrcode from 'qrcode-vue'

//import Vue from "vue";

export default {
  components: {
    Popover,
    VueQrcode,
  },
  data: () => ({
    files: [],
    fileName: '',
    id: 'upload',
    uploadedFile: null,
    loading: false,
    showQRCode: false,
    resetFile: 1,
  }),
  props: {
    value: String,
    convert: String,
    width: {
      type: String,
      default: '150px',
    },
    height: {
      type: String,
      default: '150px',
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value(val) {
      if (!this.fileName && val)
        this.fileName = val.substr(val.lastIndexOf('/') + 1)
    },
  },
  computed: {
    qrcode() {
      return (
        window.location.protocol +
        '//' +
        window.location.host.replace(':8000', ':8001') +
        '/Main/App/Extender?clientKey=' +
        window.ClientKey +
        '&cmd=upload'
      )
    },
    opts() {
      if (this.showQRCode) return [{ text: 'Cancel' }]
      else if (this.value)
        return [{ text: 'Mobile Upload' }, { text: 'View' }, { text: 'Clear' }]
      else return [{ text: 'Mobile Upload' }]
    },
    imgboxStyle() {
      let def = {
        width: this.width,
        height: this.height,
      }
      if (this.loading) {
        return {
          ...def,
          'background-image': 'url(/img/icons/loading.gif)',
        }
      } else if (!this.value) {
        return {
          ...def,
          'background-image': 'url(/img/icons/camera.png)',
        }
      } else {
        let ext = this.value.substr(-3)
        if (
          ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(ext)
        ) {
          return {
            ...def,
            'background-image': 'url(/img/icons/' + ext + '-icon.png)',
          }
        }
        return {
          ...def,
          'background-image': 'url(' + this.$api.url + this.value + ')',
          'background-size': 'cover',
        }
      }
    },
  },
  created() {
    if (!window.uuid) window.uuid = 0
    this.id = 'upload-' + window.uuid++
  },
  methods: {
    handleOpen() {
      this.$refs.uploader.click()
    },
    async previewFiles(event) {
      let res = {}
      var form = new FormData()
      this.loading = true
      if (this.multiple) {
        for (const file of event.target.files) {
          form.append('files', file)
        }
      } else {
        form.append('file', event.target.files[0])
        this.fileName = event.target.files[0].name
      }
      res = await this.$api.upload(form, {
        multiple: this.multiple,
        convert: this.convert,
      })
      if (res.success) {
        let infos = []
        if (this.multiple) {
          for (const idx in event.target.files) {
            if (event.target.files[idx].name && res.data[idx])
              infos.push({
                name: event.target.files[idx].name,
                url: res.data[idx],
              })
          }
        } else {
          infos.push({
            name: event.target.files[0].name,
            url: res.data,
          })
        }
        this.uploadedFile = res.data
        res.infos = infos
        this.$emit('update:value', res.data)
        this.$emit('change', res, infos)
      }
      this.resetFile++
      this.loading = false
    },
    handleItemClick(item) {
      if (item.text == 'Mobile Upload') {
        this.showQRCode = true
        if (this.$options.sockets.onmessage)
          delete this.$options.sockets.onmessage
        this.$options.sockets.onmessage = (evt) => {
          let d = JSON.parse(evt.data)
          if (d.clientKey == window.ClientKey && this.showQRCode) {
            this.$emit('update:value', d.value)
            this.$emit('change', d)
            this.resetFile++
            this.showQRCode = false
          }
        }
      } else if (item.text == 'View') {
        window.open(this.value, '_blank')
      } else if (item.text == 'Cancel') {
        if (this.$options.sockets.onmessage)
          delete this.$options.sockets.onmessage
        this.showQRCode = false
      } else if (item.text == 'Clear') {
        this.$emit('update:value', '')
        this.resetFile++
      }
    },
  },
}
</script>
<style lang="scss">
.ui-upload {
  .upload-image {
    width: 200px;
    height: 200px;
    background-color: #f3f3f3;
    background-position: center;
    background-repeat: no-repeat;
  }
  .opener.dropbox {
    background-position: 50%;
  }
  .imgbox {
    width: 150px;
    max-width: 100%;
    height: 150px;
    max-height: 100%;
    background: silver;
    border-right: 1px solid white;
    box-sizing: border-box;
    background-repeat: no-repeat;
    background-position: center;

    &.empty {
      background-image: url('/img/icons/camera.png');
      background-size: initial;
    }
  }
  .ui-popover {
    border: 1px solid silver;
    max-height: 200px;
    overflow: auto;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.3);
    .ui-dropdown--item {
      padding: 0 8px;
      cursor: pointer;
      &-inner {
        padding: 5px 0;
        border-bottom: 1px solid #ddd;
        text-align: left;
        font-size: 14px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      &:last-child {
        .ui-dropdown--item-inner {
          border-bottom-color: transparent;
        }
      }
      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }
}
.rounded-forms {
  .ui-upload {
    .imgbox {
      border-radius: 8px;
    }
  }
}
</style>
