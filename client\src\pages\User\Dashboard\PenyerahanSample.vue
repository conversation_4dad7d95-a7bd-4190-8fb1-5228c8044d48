<template>
  <div>
    <div
      v-if="!datalist.length"
      style="padding: 50px; height: calc(100vh - 156px); text-align: center"
    >
      TIDAK ADA PERMOHONAN YANG BUTUH DIKIRIMKAN
    </div>
    <div
      v-else
      style="padding: 20px 50px; text-align: center; background-color: #f3f3f3"
    >
      Anda memiliki waktu <b>14 hari</b> untuk menyerahkan contoh uji
    </div>
    <v-list>
      <List
        v-show="datalist.length"
        dbref="UJI_SelPermohonanList"
        style="height: calc(100vh - 199px)"
        :items.sync="datalist"
        :dbparams="dbparams"
        :rebind="rebind"
        :selectOnLoad="true"
      >
        <template v-slot="{ row }">
          <v-list-item>
            <!-- <v-list-item-action>
              <Checkbox :value.sync="row.checked" @click="ItemClick" />
            </v-list-item-action> -->

            <v-list-item-content>
              <v-list-item-title>{{ row.NamaPelanggan }}</v-list-item-title>
              <v-list-item-subtitle>
                {{ row.NoPengujian }} | {{ row.JenisUji }}
              </v-list-item-subtitle>
              <v-list-item-subtitle style="font-weight: bold">
                Rp. {{ row.TotalBayar | format }}
              </v-list-item-subtitle>
            </v-list-item-content>
            <v-list-item-icon>
              <v-menu offset-y>
                <template v-slot:activator="{ on }">
                  <v-icon v-on="on"> mdi-dots-vertical</v-icon>
                </template>
                <v-list dense>
                  <v-list-item
                    v-for="(item, idx) in itemMenu"
                    :key="idx"
                    @click="ItemMenuClick(item, row)"
                  >
                    <v-list-item-content
                      :style="{ color: item.color || '#333' }"
                    >
                      <v-list-item-title>{{ item.text }}</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-menu>
            </v-list-item-icon>
          </v-list-item>
          <!-- <div
            style="padding: 12px 20px;
                font-weight: bold;
                width: 200px;
                text-align: right;"
          >
            Rp. {{ row.TotalBayar | format }}
          </div>
          <div style="padding:8px 20px">
            <v-btn
              text
              outlined
              small
              color="primary"
              @click="ShowDetail(row.PermohonanID)"
            >
              DETAIL
            </v-btn>
            <v-btn text small color="error">
              <v-icon>mdi-trash-can</v-icon>
            </v-btn>
          </div>
        </div> -->
        </template>
      </List>
    </v-list>
    <div
      style="
        position: fixed;
        bottom: 0px;
        padding: 10px 0;
        width: calc(100vw - 33px);
      "
    >
      <v-btn
        style="margin-left: 5px"
        color="primary"
        class="btn-full"
        v-show="this.billing.TotalBayar"
        @click="billing.show = true"
      >
        BAYAR: Rp {{ this.billing.TotalBayar | format }},-
      </v-btn>
    </div>
    <Bayar :forms.sync="billing" @refresh="$emit('refresh')"> </Bayar>
    <Pengajuan
      v-if="showPengajuan"
      :permohonanId="permohonanId"
      @close="ClosePengajuan"
    />
  </div>
</template>
<script>
import Bayar from './Bayar.vue'
import Pengajuan from '../../App/Public/Pengajuan.vue'
export default {
  components: {
    Bayar,
    Pengajuan,
  },
  data: () => ({
    showPengajuan: false,
    billing: {
      show: false,
      PaymentType: 'cash',
      PermohonanID: '',
      TotalBayar: 0,
      step: 0,
    },
    permohonanId: 0,
    itemMenu: [{ text: 'Detail' } /*, { text: 'Hapus', color: 'red' }*/],
    datalist: [],
    statusId: '2',
    rebind: 1,
  }),
  computed: {
    dbparams() {
      return { StatusID: this.statusId }
    },
  },
  watch: {
    'billing.show'(val) {
      if (!val) {
        this.rebind++
        this.billing.TotalBayar = 0
      }
    },
  },
  mounted() {
    this.statusId = '2'
  },
  methods: {
    ItemClick() {
      this.billing.TotalBayar = 0
      let permohonanId = []
      for (let item of this.datalist) {
        if (item.checked) {
          this.billing.TotalBayar += item.TotalBayar
          permohonanId.push(item.PermohonanID)
        }
      }
      this.billing.PermohonanID = permohonanId.join(',')
    },
    ChangeStatus(status) {
      this.statusId = status
    },
    async ShowBilling(permohonanId) {
      let ret = await this.$api.call('UJI_SelBilling', {
        PermohonanID: permohonanId,
      })
      if (ret.success) {
        this.billing = {
          show: true,
          step: 1,
          total: ret.data[0].TotalBayar,
          billingId: ret.data[0].BillingID,
          PaymentType: 'transfer',
        }
      }
    },
    ShowDetail(id) {
      this.permohonanId = id
      this.showPengajuan = true
    },
    async Delete(id) {
      if (confirm('Hapus Pengujian?')) {
        await this.$api.call('UJI_DelPermohonan', {
          PermohonanID: id,
          Keterangan: 'Dihapus Pelanggan',
        })
        this.rebind++
        this.billing.TotalBayar = 0
      }
    },
    ItemMenuClick(val, row) {
      if (val.text == 'Detail') {
        this.ShowDetail(row.PermohonanID)
      } else if (val.text == 'Hapus') {
        this.Delete(row.PermohonanID)
      }
    },
    ClosePengajuan() {
      this.showPengajuan = false
      this.rebind++
      this.billing.TotalBayar = 0
    },
  },
}
</script>
<style lang="scss">
.is-mobile {
  .btn-full {
    width: 100%;
    margin-top: 10px;
  }
  .panel-pengajuan {
    width: 100vw !important;
    top: 60px !important;
    height: calc(100vh - 60px) !important;
    left: 0 !important;
  }
}
</style>
