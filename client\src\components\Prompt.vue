<template>
  <Modal :title="pData.title" :show.sync="xshow" width="400px" @submit="Save">
    <div>
      <TextArea
        v-if="pData.type == 'textarea'"
        :value.sync="value"
        width="300px"
        rows="5"
      />
      <Input
        v-else
        :type="pData.type"
        :label="pData.label"
        :value.sync="value"
      />
    </div>
  </Modal>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'

export default {
  data: () => ({
    xshow: false,
    value: '',
  }),
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.setPromptShow(val)
    },
  },
  computed: {
    ...mapGetters({
      pData: 'getPromptData',
      show: 'getPromptShow',
    }),
  },
  methods: {
    ...mapActions(['setPromptShow', 'setPromptValue']),
    async Save() {
      console.log(this.value)
      this.setPromptValue(this.value)
      this.setPromptShow(false)
      this.pData.onsave(this.value)
    },
  },
}
</script>
