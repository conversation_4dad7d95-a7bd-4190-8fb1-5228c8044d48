user  nginx;
worker_processes  1;
error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;
events {
  worker_connections  1024;
}
http {
  include       /etc/nginx/mime.types;
  default_type  application/octet-stream;
  log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
  access_log  /var/log/nginx/access.log  main;
  sendfile        on;
  keepalive_timeout  65;

  upstream websocket {
    server localhost:8289;
  }

  server {
    listen                  80 default_server;
    listen                  [::]:80 default_server;

    server_name             rampungbayar.dpubinmarcipka.jatengprov.go.id simperum.bagide.com;
    return 301              https://$host$request_uri;
  }

  server {
    listen                  443 ssl http2;
    server_name             rampungbayar.dpubinmarcipka.jatengprov.go.id;
    ssl_certificate	        /etc/letsencrypt/live/rampungbayar.dpubinmarcipka.jatengprov.go.id/fullchain.pem;
    ssl_certificate_key	    /etc/letsencrypt/live/rampungbayar.dpubinmarcipka.jatengprov.go.id/privkey.pem;

    location / {
      root   /app/client;
      index  index.html;
      try_files $uri $uri/ /index.html;
    }
		location /api {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://localhost:8001;
		}
		location /report {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://localhost:8001;
		}
		location /reports {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://localhost:8001;
		}
		location /Main/App/Extender {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://localhost:8001;
		}
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
      root   /usr/share/nginx/html;
    }
  }
}