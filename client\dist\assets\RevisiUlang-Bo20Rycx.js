import{n as o,S as m,k as l,_ as r,h,i as p,b as c}from"./index-DYIZrBBo.js";const u={components:{SidePane:m},data:()=>({datagrid:[],dbparams:{PermohonanID:0},lembarKerja:[],forms:{PermohonanID:0,ParameterID:0,Ma<PERSON><PERSON>:""},rebind:1,masalah:{show:!1}}),computed:{showSubmitButton(){let t=this.datagrid.filter(e=>!e.Hasil<PERSON>tatus),a=this.lembarKerja.filter(e=>!e.Alasan&&!e.ApprovedBy);return t.length==0&&a.length>0}},methods:{async ItemClick(t){this.Populate(t.PermohonanID)},async Populate(t){this.dbparams={PermohonanID:t};var a=await this.$api.call("UJI.SelPermohonan",{PermohonanID:t});a.data.length?this.forms=a.data[0]:this.forms={},this.PopulateLK(t)},Open(t){window.open(this.$api.url+t,"_blank")},OpenPDF(t){window.open(this.$api.url+t.replace(/\.\w{3,4}$/,"pdf"),"_blank")},async PopulateLK(t){this.lembarKerja=[];var a=await this.$api.call("UJI.SelLembarKerja",{PermohonanID:t});this.lembarKerja=a.data},async UpdMulaiPengujian(){if(confirm("Mulai Pengujian?")){var t=await this.$api.call("UJI_UpdMulaiPerngujian",{PermohonanID:this.forms.PermohonanID,MulaiStatus:1});t.success&&this.Populate(this.forms.PermohonanID)}},async UpdHasil(t,a,e){if(!(this.datagrid.filter(n=>!n.HasilStatus).length<=1&&!confirm("Pengujian Telah Selesai?"))){var i=await this.$api.call("UJI_UpdHasil",{PermohonanID:t,ParameterID:a,HasilStatus:e});i.success&&this.Populate(this.forms.PermohonanID)}},async ShowMasalah(t,a){this.masalah.show=!0,this.forms.ParameterID=t,this.forms.ParameterID=a},async InsMasalah(){await this.$api.call("UJI_InsMasalah",this.forms),this.rebind++,this.masalah.show=!1},async Resolve(t){await this.$api.call("UJI_SavResolveMasalah",{MasalahID:t}),this.rebind++},async Reject(t){let a=prompt("Alasan?");a&&(await this.$api.call("UJI_SavLembarKerjaRejection",{LembarKerjaID:t,Alasan:a})).success&&(this.rawUrl="",this.PopulateLK(this.forms.PermohonanID),this.rebind++)},async Approve(t){confirm("Setujui?")&&(await this.$api.call("UJI_SavLembarKerjaApproval",{LembarKerjaID:t,FilePath:this.rawUrlOri})).success&&(this.rawUrl="",this.PopulateLK(this.forms.PermohonanID),this.rebind++)},async fileUploaded(t,a){if(this.$api.notify("Upload Sukses"),(await this.$api.call("UJI_SavLembarKerja",{PermohonanID:this.forms.PermohonanID,XmlRawUrl:a})).success){await this.PopulateLK(this.forms.PermohonanID);for(let s of this.lembarKerja)this.$api.get("/reports/uji/convert-lk/"+s.LembarKerjaID)}}}};var d=function(){var a=this,e=a._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{attrs:{statusId:",7,8,9,"},on:{"item-click":a.ItemClick}}),e("div",{directives:[{name:"show",rawName:"v-show",value:a.forms.Nama,expression:"forms.Nama"}],staticStyle:{overflow:"auto",height:"calc(100vh - 66px)"}},[e("div",{staticStyle:{background:"#039ae4",color:"white",padding:"15px 15px 10px 15px",display:"flex"}},[e("div",{staticStyle:{display:"flex",width:"calc(100% - 200px)"}},[e("div",{staticStyle:{"max-width":"450px",overflow:"hidden","text-wrap":"nowrap","text-overflow":"ellipsis"}},[a._v(" "+a._s(a.forms.Nama)+" ")]),e("div",{staticStyle:{background:"white",color:"#039ae4","margin-left":"10px",padding:"5px 8px","border-radius":"5px","font-size":"small",position:"relative",top:"-3px"}},[a._v(" "+a._s(a.forms.NoPengujian)+" ")])]),e(l),a.forms.StatusID==5?e(r,{staticStyle:{color:"white !important",position:"relative",top:"-2px"},attrs:{small:"",disabled:""}},[a._v(" DALAM PROSES ")]):a._e(),a.forms.StatusID==6?e(r,{staticStyle:{color:"white !important",position:"relative",top:"-2px"},attrs:{small:"",disabled:""}},[a._v(" SELESAI ")]):a._e()],1),e(h,{staticClass:"table-cekprogress",attrs:{datagrid:a.datagrid,dbref:"UJI_SelCekProgress",doRebind:a.rebind,dbparams:a.dbparams,disabled:!0,columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Jml",value:"JmlContoh"},{name:"Keterangan",value:"Keterangan"},{name:"Waktu",value:"Waktu"}]},on:{"update:datagrid":function(s){a.datagrid=s}},scopedSlots:a._u([{key:"row-NamaParameter",fn:function({row:s}){return[e("div",{class:{"is-masalah":s.Ordr==2,"is-done":s.HasilStatus}},[e("div",[a._v(a._s(s.NamaParameter))]),e("div",[a._v(a._s(s.Ordr==1?s.Metode:""))])])]}},{key:"row-Keterangan",fn:function({row:s}){return[a._v(" "+a._s(s.Keterangan)+" ")]}},{key:"row-Waktu",fn:function({row:s}){return[a._v(" "+a._s(s.Waktu)+" Hari ")]}}])}),a._l(a.lembarKerja,function(s,i){return e("div",{key:i,staticStyle:{"font-size":"12px",background:"#f3f3f3",display:"flex","margin-bottom":"1px",padding:"8px"}},[e(r,{attrs:{small:"",text:"",color:"primary"},on:{click:function(n){return a.Open(s.LkUrl,s.LembarKerjaID)}}},[a._v(" "+a._s(s.Nama)+" ")]),e(l),s.ApprovedBy?e(r,{staticStyle:{margin:"4px"},attrs:{"x-small":"",text:"",outlined:""}},[a._v(" SUDAH DISETUJUI ")]):a._e(),s.Alasan?e(r,{directives:[{name:"tooltip",rawName:"v-tooltip",value:s.Alasan,expression:"lk.Alasan"}],staticStyle:{margin:"4px"},attrs:{"x-small":"",text:"",outlined:"",color:"error"}},[a._v(" DITOLAK ")]):a._e(),s.Alasan?a._e():e(r,{staticStyle:{margin:"4px"},attrs:{"x-small":"",color:"error"},on:{click:function(n){return a.Reject(s.LembarKerjaID)}}},[a._v(" TOLAK / REVISI ")])],1)})],2),e(p,{attrs:{title:"Tambah Keterangan Masalah",show:a.masalah.show},on:{"update:show":function(s){return a.$set(a.masalah,"show",s)},submit:a.InsMasalah}},[e(c,{staticStyle:{height:"100px"},attrs:{value:a.forms.Masalah,placeholder:"Keterangan Masalah",width:"350px"},on:{"update:value":function(s){return a.$set(a.forms,"Masalah",s)}}})],1)],1)},f=[],_=o(u,d,f,!1,null,null);const I=_.exports;export{I as default};
