<template>
  <div>
    <div style="display: flex">
      <div>
        <v-btn
          :text="statusId != '1,2'"
          :outlined="statusId != '1,2'"
          color="primary"
          elevation="0"
          tile
          @click="ChangeStatus('1,2')"
        >
          Per<PERSON><PERSON><PERSON>
        </v-btn>
      </div>
      <div>
        <v-btn
          :text="statusId != '3'"
          :outlined="statusId != '3'"
          color="primary"
          elevation="0"
          tile
          @click="ChangeStatus('3')"
        >
          Penyerahan Sample
        </v-btn>
      </div>
      <div>
        <v-btn
          :text="statusId != '3'"
          :outlined="statusId != '3'"
          color="primary"
          elevation="0"
          tile
          @click="ChangeStatus('3')"
        >
          Menunggu Pembayaran
        </v-btn>
      </div>
      <div>
        <v-btn
          :text="statusId != '4,5,6,7,8'"
          :outlined="statusId != '4,5,6,7,8'"
          color="primary"
          elevation="0"
          tile
          @click="ChangeStatus('4,5,6,7,8')"
        >
          Dalam <PERSON>ses
        </v-btn>
      </div>
      <div>
        <v-btn
          :text="statusId != '9'"
          :outlined="statusId != '9'"
          color="primary"
          elevation="0"
          tile
          @click="ChangeStatus('9')"
        >
          Sudah Selesai
        </v-btn>
      </div>
    </div>
    <div>
      <List
        dbref="UJI_SelPermohonanList"
        :items.sync="datalist"
        :dbparams="dbparams"
        :rebind="rebind"
        :selectOnLoad="true"
      >
        <template v-slot="{ row }">
          <div
            style="
              font-size: 13px;
              margin-bottom: 10px;
              padding: 5px 10px 0 10px;
              display: flex;
            "
          >
            <div style="padding: 8px 20px" v-show="statusId == '1,2'">
              <Checkbox :value.sync="row.checked" @click="ItemClick" />
            </div>
            <div>
              <div>
                {{ row.NamaPelanggan }}
              </div>
              <div>{{ row.NoPengujian }} | {{ row.JenisUji }}</div>
            </div>
            <div
              style="
                padding: 12px 20px;
                font-weight: bold;
                width: 200px;
                text-align: right;
              "
            >
              Rp. {{ row.TotalBayar | format }}
            </div>
            <div style="padding: 8px 20px">
              <v-btn
                text
                outlined
                small
                color="primary"
                v-if="statusId == '3'"
                @click="ShowBilling(row.PermohonanID)"
              >
                BILLING
              </v-btn>
              <v-btn text outlined small color="primary" v-else>DETAIL</v-btn>
            </div>
          </div>
        </template>
      </List>
      <v-btn
        style="margin-left: 5px"
        color="primary"
        v-show="this.billing.TotalBayar"
        @click="billing.show = true"
      >
        BAYAR: Rp {{ this.billing.TotalBayar | format }},-
      </v-btn>
    </div>
    <Bayar :forms.sync="billing" @refresh="$emit('refresh')"> </Bayar>
  </div>
</template>
<script>
import Bayar from './Bayar.vue'
export default {
  components: {
    Bayar,
  },
  data: () => ({
    billing: {
      show: false,
      PaymentType: 'cash',
      PermohonanID: '',
      TotalBayar: 0,
    },
    datalist: [],
    statusId: ',1,',
    rebind: 1,
  }),
  computed: {
    dbparams() {
      return { StatusID: this.statusId }
    },
  },
  watch: {
    'billing.show'(val) {
      if (!val) {
        this.rebind++
        this.billing.TotalBayar = 0
      }
    },
  },
  mounted() {
    this.statusId = '1,2'
  },
  methods: {
    ItemClick() {
      this.billing.TotalBayar = 0
      let permohonanId = []
      for (let item of this.datalist) {
        if (item.checked) {
          this.billing.TotalBayar += item.TotalBayar
          permohonanId.push(item.PermohonanID)
        }
      }
      this.billing.PermohonanID = permohonanId.join(',')
    },
    ChangeStatus(status) {
      this.statusId = status
    },
    async ShowBilling(permohonanId) {
      let ret = await this.$api.call('UJI_SelBilling', {
        PermohonanID: permohonanId,
      })
      if (ret.success) {
        this.billing = {
          show: true,
          step: 1,
          total: ret.data[0].TotalBayar,
          billingId: ret.data[0].BillingID,
          PaymentType: 'transfer',
        }
      }
    },
  },
}
</script>
