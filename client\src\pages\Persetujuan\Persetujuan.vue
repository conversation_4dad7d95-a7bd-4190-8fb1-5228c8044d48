<template>
  <div style="display: flex">
    <SidePane @item-click="ShowSertifikat" />
    <div v-if="rawUrl" class="right-pane">
      <iframe
        :src="rawUrl"
        style="width: 100%; height: 100%"
        frameborder="0"
      ></iframe>
      <div style="position: fixed; bottom: 20px; right: 40px; display: flex">
        <v-btn
          class="close-right-pane"
          @click="rawUrl = ''"
          style="margin-right: 8px"
          v-show="!loading"
        >
          BATAL
        </v-btn>
        <v-btn color="primary" @click="Sign" :disabled="loading">
          SETUJUI
        </v-btn>
      </div>
    </div>
  </div>
</template>
<script>
import SidePane from './SidePane.vue'
export default {
  components: {
    SidePane,
  },
  data: () => ({
    rawUrlOri: '',
    rawUrl: '',
    forms: {},
    passphrase: '',
    showPassphrase: false,
    loading: false,
  }),
  methods: {
    ShowSertifikat(val) {
      this.forms = val
      this.rawUrlOri = val.RawUrl
      this.rawUrl = this.$api.url + val.RawUrl.replace(/(xlsx|docx)$/, 'pdf')
    },
    async Sign() {
      if (confirm('Setujui?')) {
        let ret = this.$api.call('UJI_SavSertifikatApproval', {
          PermohonanID: this.forms.PermohonanID,
          FilePath: this.rawUrlOri,
        })
        if (ret.success) {
          this.rawUrl = ''
        }
      }
    },
  },
}
</script>
<style lang="scss"></style>
