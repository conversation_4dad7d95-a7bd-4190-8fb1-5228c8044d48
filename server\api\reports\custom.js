var Reporter = require('./generator')
module.exports = {
  rtlh_rekom: [
    {
      sp: 'PRM_RptRekomendasi',
      params: {_PRM_SelProposalID: 8},
      groupsheet: {
        key: '<PERSON><PERSON><PERSON>',
      },
      grouprow: {
        key: 'Kabupaten',
        header: [null, [{key: 'Kabupaten', colspan: 6}]],
      },
      header: [
        null,
        {
          text: Reporter.template`REKOMENDASI PENCAIRAN (${'Tahapan'})`,
          colspan: 7,
        },
        {
          text: Reporter.template`BANKEUPEMDES RTLH TAHUN ${'Tahun'}`,
          colspan: 7,
        },
        null,
        [
          {text: 'NO.', width: 6, border: Reporter.BORDER_THIN},
          {text: 'NO.', width: 6, border: Reporter.BORDER_THIN},
          {text: 'NIK', width: 17, border: Reporter.BORDER_THIN},
          {text: 'ID BDT', width: 17, border: Reporter.BORDER_THIN},
          {text: 'NAMA PENERIMA', width: 20, border: Reporter.BORDER_THIN},
          {text: 'ALAMAT PENERIMA', width: 30, border: Reporter.BORDER_THIN},
          {text: 'USULAN (Rp)', width: 16, border: Reporter.BORDER_THIN},
          {text: 'REALISASI (Rp)', width: 16, border: Reporter.BORDER_THIN},
        ],
        [
          {
            text: '(1)',
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
          },
          {
            text: '(2)',
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
          },
          {
            text: '(3)',
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
          },
          {
            text: '(4)',
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
          },
          {
            text: '(5)',
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
          },
          {
            text: '(6)',
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
          },
          {
            text: '(7)',
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
          },
          {
            text: '(8)',
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
          },
        ],
      ],
      body: [
        {text: Reporter.template`${'idx'}`},
        {text: Reporter.template`${'idxgrp'}`},
        {key: 'NIK'},
        {key: 'IDBDT'},
        {key: 'Nama'},
        {key: 'Alamat'},
        {text: ********},
        {text: ********},
      ],
    },
    {
      sp: 'PRM_RptRekomRekap',
      params: {_PRM_SelProposalID: 8},
      groupsheet: {
        key: 'Tahapan',
        text: Reporter.template`REKAP_${'Tahapan'}`,
      },
      grouprow: {
        key: 'Kabupaten',
        header: [
          null,
          {
            text: Reporter.template`REKOMENDASI PENCAIRAN (${'Tahapan'})`,
            colspan: 10,
            font: Reporter.FONT_BOLD,
          },
          {
            text: Reporter.template`BANKEUPEMDES RTLH TAHUN ${'Tahun'}`,
            colspan: 10,
            font: Reporter.FONT_BOLD,
          },
          null,
          [
            {
              text: 'NO.',
              width: 6,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
            {
              text: 'NO REKOM',
              width: 11.3,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
            {
              text: 'KABUPATEN',
              width: 12,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
            {
              text: 'KECAMATAN',
              width: 12,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
            {
              text: 'DESA',
              width: 12,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
            {
              text: 'KODE',
              width: 12,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
            {
              text: 'NO. REK. DESA',
              width: 17,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
            {
              text: 'NAMA REK. DESA',
              width: 17,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
            {
              text: 'CABANG PEMBUKA',
              width: 17,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
            {
              text: 'JML PENERIMA',
              width: 14,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
            {
              text: 'JML RUPIAH',
              width: 14,
              border: Reporter.BORDER_THIN,
              alignment: Reporter.ALIGN_CENTER,
              font: Reporter.FONT_BOLD,
            },
          ],
          [
            {
              text: Reporter.template`${'Kabupaten'}`,
              colspan: 10,
              font: Reporter.FONT_BOLD,
            },
          ],
        ],
        footer: [
          [
            {
              text: 'Total',
              colspan: 8,
              border: Reporter.BORDER_THIN,
              font: Reporter.FONT_BOLD,
            },
            {
              fn: 'SUM',
              border: Reporter.BORDER_THIN,
              font: Reporter.FONT_BOLD,
            },
            {
              fn: 'SUM',
              border: Reporter.BORDER_THIN,
              font: Reporter.FONT_BOLD,
            },
          ],
          null,
          [
            {colspan: 6},
            {text: 'KEPALA DINAS PERUMAHAN RAKYAT', colspan: 3},
          ],
          [{colspan: 6}, {text: 'DAN KAWASAN PERMUKIMAN', colspan: 3}],
          [{colspan: 6}, {text: 'PROVINSI JAWA TENGAH', colspan: 3}],
          null,
          null,
          [{colspan: 6}, {text: 'Ir. ARIEF DJATMIKO', colspan: 3}],
          [{colspan: 6}, {text: 'Pembina Tingkat I', colspan: 3}],
          [{colspan: 6}, {text: 'NIP. ******** 199603 1 004', colspan: 3}],
        ],
      },
      header: null,
      body: [
        {text: Reporter.template`${'idx'}`},
        {
          text: d => {
            return d.NoRekap + `000${d.idxgrp}`.substr(-3)
          },
        },
        {key: 'Kabupaten'},
        {key: 'Kecamatan'},
        {key: 'Kelurahan'},
        {key: 'KodeDagri'},
        {key: 'NoRekening'},
        {key: 'NamaRekening'},
        {key: 'NamaBank'},
        {key: 'Jumlah'},
        {
          text: d => {
            return d.Jumlah * ********
          },
        },
      ],
    },
    {
      sp: 'PRM_RptRekomRekapKab',
      params: {_PRM_SelProposalID: 8},
      groupsheet: {
        key: 'Tahapan',
        text: Reporter.template`JMLDESA_${'Tahapan'}`,
      },
      header: [
        null,
        {
          text: Reporter.template`REKAP KABUPATEN (${'Tahapan'})`,
          colspan: 4,
          font: Reporter.FONT_BOLD,
        },
        {
          text: Reporter.template`BANKEUPEMDES RTLH TAHUN ${'Tahun'}`,
          colspan: 4,
          font: Reporter.FONT_BOLD,
        },
        null,
        [
          {
            text: 'NO.',
            width: 6,
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
            font: Reporter.FONT_BOLD,
          },
          {
            text: 'KABUPATEN',
            width: 17,
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
            font: Reporter.FONT_BOLD,
          },
          {
            text: 'JML. DESA',
            width: 12,
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
            font: Reporter.FONT_BOLD,
          },
          {
            text: 'JML PENERIMA',
            width: 12,
            border: Reporter.BORDER_THIN,
            alignment: Reporter.ALIGN_CENTER,
            font: Reporter.FONT_BOLD,
          },
        ],
      ],
      body: [
        {text: Reporter.template`${'idx'}`},
        {key: 'Kabupaten'},
        {key: 'JmlDesa'},
        {key: 'JmlPenerima'},
      ],
    },
  ],
}
