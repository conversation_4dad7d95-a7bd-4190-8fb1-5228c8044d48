<template>
  <div class="form-coms ui-textarea">
    <div
      class="form-label"
      v-if="$attrs.label"
      :class="{ '--required': hasError }"
    >
      {{ $attrs.label }}
    </div>
    <textarea
      ref="textarea"
      v-bind="$attrs"
      :disabled="disabled"
      v-model="vmodel"
      :class="height == 'auto' ? 'auto-height' : ''"
      :style="{
        width: width,
        resize: 'none',
      }"
      @keyup="adjustHeight"
    ></textarea>
  </div>
</template>

<script>
export default {
  name: 'TextArea',
  data: () => ({
    val: '',
  }),
  props: {
    value: String,
    disabled: Boolean,
    width: {
      type: String,
      default: '180px',
    },
    height: {
      type: String,
      default: '55px',
    },
  },
  mounted() {
    this.val = this.value
    setTimeout(() => {
      this.adjustHeight()
    }, 100)
    if (this.height != 'auto') this.$refs.textarea.style.height = this.height
  },
  computed: {
    vmodel: {
      get() {
        return (this.val || '').replace(/<br \/>/g, '\n')
      },
      set(val) {
        this.val = val
        this.$emit('update:value', val.replace(/\\n/g, '<br />'))
      },
    },
    hasError() {
      return (
        this.$attrs.label &&
        this.$attrs.label.match(/\*$/) &&
        (this.vmodel === null ||
          this.vmodel === undefined ||
          this.vmodel === '')
      )
    },
  },
  watch: {
    value(val) {
      this.val = val
      setTimeout(() => {
        this.adjustHeight()
      }, 100)
    },
  },
  methods: {
    adjustHeight() {
      if (this.height != 'auto') return
      const textarea = this.$refs.textarea
      if (textarea) {
        textarea.style.height = 'auto'
        textarea.style.height = textarea.scrollHeight + 1 + 'px'
      }
    },
  },
}
</script>
<style lang="scss">
.ui-textarea {
  margin-bottom: 8px;
  font-size: 14px;

  .form-label {
    text-align: left;
    font-size: 14px;
    &.--required {
      color: red;
    }
  }
  textarea {
    padding: 6px 12px;
    border-radius: 6px;
    background: rgba(200, 200, 200, 0.2);
    border-bottom: 1px solid silver;
    // min-height: calc(100% - 10px);

    &:hover,
    &:focus {
      border: 0;
      background: rgba(200, 200, 200, 0.4);
      border-bottom: 1px solid gray;
    }
  }
}
.rounded-forms {
  .ui-textarea {
    textarea {
      border-radius: 8px;
    }
  }
}
</style>
