<template>
  <div style="display: flex">
    <SidePane
      @item-click="ItemClick"
      statusId=",2,3,4,"
      :rebind="rebindSidebar"
      :addButton="true"
    />
    <div
      v-show="forms.PermohonanID"
      class="form-inline"
      style="
        padding: 20px 20px;
        width: calc(100vw - 500px);
        overflow: auto;
        height: calc(100vh - 66px);
      "
    >
      <div v-show="forms.BayarStatus == 2" class="dvWarn">
        {{ forms.CatatanKhusus }}
      </div>
      <div
        id="dvRightBox"
        style="
          float: right;
          font-family: raleway;
          background: white;
          margin-top: -20px;
        "
      >
        <div style="padding: 10px 20px; text-align: center">
          <div style="font-size: xx-large">{{ forms.NoPengujian }}</div>
          <div>{{ forms.ShareCode }}</div>
        </div>
        <div
          v-show="forms.StatusID >= 4 && forms.BayarStatus == 1"
          style="
            padding: 10px 20px;
            border-top: 1px solid #e3e3e3;
            background: #4caf50;
            color: white;
            text-align: center;
          "
        >
          <span>Sudah Dibayarkan</span>
        </div>
        <div
          v-show="forms.StatusID == 9"
          style="
            padding: 10px 20px;
            border-top: 1px solid #e3e3e3;
            background: #8bc34a;
            color: white;
            text-align: center;
          "
        >
          <span>Sudah Diserahkan</span>
        </div>
      </div>
      <Input
        type="text"
        label="Nama Pelanggan"
        :value.sync="forms.Nama"
        width="300px"
      />
      <TextArea label="Alamat" :value.sync="forms.Alamat" width="300px" />
      <Input
        type="text"
        label="No. Ponsel"
        :value.sync="forms.Phone"
        width="300px"
      />
      <Select
        :items="jenis"
        label="Nama/Jenis Contoh"
        :value.sync="forms.JenisID"
        width="300px"
      />
      <DatePicker
        label="Tanggal Masuk"
        :value.sync="forms.TglMasuk"
        style="padding-top: 4px"
      />
      <TextArea
        label="Kegiatan/Paket Pekerjaan"
        :value.sync="forms.NamaKegiatan"
        width="300px"
      />
      <Label label="Sumber/SatKer">
        <Select :items="satker" :value.sync="forms.SumberDana" />
        <Input
          type="text"
          placeholder="Satker / Kab / Kota"
          :value.sync="forms.SatKer"
          style="margin-left: 5px"
          v-show="forms.SumberDana < '5'"
        />
      </Label>
      <Label label="Surat Permohonan/Tgl.">
        <Input
          type="text"
          :value.sync="forms.SuratNo"
          placeholder="No. Surat"
        />
        <DatePicker :value.sync="forms.SuratTgl" style="margin-left: 5px" />
      </Label>
      <Label label="Surat Permohonan">
        <Uploader :value.sync="forms.SuratUrl" :key="rebindUpload">
          <template v-slot="{ opener, fileName }">
            <v-btn
              small
              text
              outlined
              v-show="fileName"
              style="margin-right: 8px"
              @click="Download(forms.SuratUrl)"
              >{{ fileName || forms.SuratUrl }}</v-btn
            >
            <v-btn small @click="opener">
              <v-icon>mdi-upload</v-icon>
            </v-btn>
          </template>
        </Uploader>
      </Label>
      <Grid
        :datagrid.sync="datagrid"
        dbref="UJI_SelPermohonanDet"
        :dbparams="dbparams"
        :disabled="true"
        :columns="[
          {
            name: 'Parameter Uji',
            value: 'NamaParameter',
          },
          {
            name: 'Nama Contoh',
            value: 'NamaContoh',
          },
          {
            name: 'Jml',
            value: 'JmlContoh',
          },
          {
            name: 'Metode',
            value: 'Metode',
          },
          {
            name: 'Harga',
            value: 'Harga',
            class: 'align-right',
          },
          {
            name: '',
            value: 'Delete',
          },
        ]"
      >
        <template v-slot:row-NamaContoh="{ row }">
          <Input
            :value.sync="row.NamaContoh"
            placeholder="(asal/ukuran contoh)"
          />
        </template>
        <template v-slot:row-JmlContoh="{ row }">
          <Input
            type="number"
            :value.sync="row.JmlContoh"
            placeholder="Jml"
            width="60px"
          />
        </template>
        <template v-slot:row-Harga="{ row }">
          {{ (row.Harga * row.JmlContoh) | format }}
        </template>
        <template v-slot:row-Delete="{ idx }">
          <v-icon color="error" @click="DelParameter(idx)"
            >mdi-trash-can</v-icon
          >
        </template>
        <template v-slot:footer>
          <tr>
            <td colspan="4">
              <v-btn text small @click="paramUji.show = true">
                <v-icon left>mdi-plus-circle</v-icon>
                Tambah Parameter Uji
              </v-btn>
            </td>
            <td
              class="align-right"
              style="font-weight: bold; padding: 8px 12px"
            >
              {{
                datagrid.reduce((res, d) => {
                  return res + d.Harga * d.JmlContoh
                }, 0) | format
              }}
            </td>
            <td></td>
          </tr>
        </template>
      </Grid>
      <br />
      <br />
      <div style="display: flex">
        <v-btn color="primary" @click="OpenReport($event)">
          <v-icon>mdi-printer</v-icon>
        </v-btn>
        <v-btn
          color="primary"
          style="margin-left: 5px"
          @click="Save"
          v-if="forms.StatusID == 1"
        >
          SIMPAN
        </v-btn>
        <v-spacer />
        <v-btn
          color="success"
          style="margin-left: 5px"
          @click="pay.show = true"
          v-if="forms.StatusID == 1"
        >
          BAYAR
        </v-btn>
      </div>
    </div>
    <ReportPopup
      v-show="showReport"
      :reportUrl="reportUrl"
      v-click-outside="CloseReport"
    />
    <ParameterUji
      :forms="paramUji"
      :jenisId="forms.JenisID"
      @submit="AddParameter"
    />
  </div>
</template>
<script>
import SidePane from '../../Loket/SidePane.vue'
export default {
  components: {
    SidePane,
  },
  data: () => ({
    datagrid: [],
    rebindSidebar: 0,
    rebindUpload: 0,
    dbparams: { PermohonanID: 0 },
    forms: {},
    pay: { show: false },
    paramUji: {
      show: false,
    },
    showReport: false,
    jenis: [
      { val: 'A', txt: 'Mutu Air & Lingkungan' },
      { val: 'B', txt: 'Bahan Bangunan' },
      { val: 'Ba', txt: 'Aspal' },
      { val: 'T', txt: 'Tanah (Geoteknik)' },
    ],
    satker: [
      { val: '1', txt: 'APBN' },
      { val: '2', txt: 'APBD I - BMCK' },
      { val: '3', txt: 'APBD I - NON BMCK' },
      { val: '4', txt: 'APBD II' },
      { val: '5', txt: 'Swasta' },
      { val: '6', txt: 'Perorangan' },
      { val: '7', txt: 'Lainnya' },
    ],
  }),
  methods: {
    async ItemClick(val) {
      this.Populate(val.PermohonanID)
    },
    async Download(val) {
      this.$api.download(this.$api.url + val, true)
    },
    async Populate(id) {
      this.rebindUpload++
      this.dbparams = { PermohonanID: id }
      var ret = await this.$api.call('UJI.SelPermohonan', {
        PermohonanID: id,
      })
      if (ret.data.length) {
        this.forms = ret.data[0]
      } else {
        this.forms = {}
      }
    },
    async Save() {
      var res = await this.$api.call('UJI.SavPermohonan', {
        ...this.forms,
        XmlPermohonanDet: this.datagrid,
      })
      if (res.success) this.rebindSidebar++
    },
    CloseReport() {},
    AddParameter() {},
  },
}
</script>
