module.exports = {
  build(item, ob) {
    let oob = ob || item.orderBy || {};
    var orderBy = "";
    for (var x in oob) {
      orderBy += x.replace(/[^a-z09_]/gi, "");
      if (oob[x] == "DESC") orderBy += " DESC";
      orderBy += ",";
    }
    if (orderBy) orderBy = " ORDER BY " + orderBy;
    let query = item.query + orderBy.replace(/,$/, "");
    console.log(query);
    return query;
  },
  warehouse: {
    query: `SELECT * FROM INV.Warehouse`
  },
  itemgroup: {
    query: `
        SELECT 
          g.*,
          p.GroupName ParentName
        FROM INV.ItemGroup g
          LEFT JOIN INV.ItemGroup p
          ON g.ParentID = p.ItemGroupID
        `,
    orderBy: {
      ItemGroupID: "ASC"
    }
  },
  item: {
    query: `
        SELECT
          i.*,
          d.<PERSON>,
          d<PERSON>,
          d<PERSON>,
          d.<PERSON>ell
        FROM inv_item i
          JOIN inv_itemdet d
          ON i.ItemID = d.ItemID
          AND d.UOMOrder = 1
          LEFT JOIN inv_itemgroup g
          ON i.GroupID = g.GroupID
        `,
    orderBy: {
      ItemName: "ASC"
    }
  }
};
