<template>
  <div></div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  created() {
    this.setMenu([])
    this.setUser(null)

    const basics = localStorage.getItem('basics')
    localStorage.clear()
    sessionStorage.clear()
    if (basics) localStorage.setItem('basics', basics)
    this.$router.push('/public')
  },
  methods: {
    ...mapActions(['setMenu', 'setUser']),
  },
}
</script>
