<template>
  <div
    style="
      height: calc(100vh - 64px);
      background: white;
      overflow-y: auto;
      overflow-x: hidden;
    "
  >
    <div style="padding: 10px">
      <v-row style="min-height: 780px">
        <v-col cols="12" md="4" sm="12">
          <div style="display: flex">
            <Select
              :items="[
                { val: '0', txt: 'Sumber per Bulan' },
                { val: '1', txt: 'Sumber per Tahun' },
                { val: '2', txt: 'Kategori per Tahun' },
              ]"
              v-model="chartVal"
              @change="SelectChanged"
            />
            <Select
              v-if="chartVal == '0' || chartVal == '1'"
              width="100px"
              :items="yearOptions"
              v-model="chartYear"
            />
          </div>
          <SumberBulanan v-if="chartVal == '0'" :tahun="chartYear" />
          <SumberTahunan v-if="chartVal == '1'" :tahun="chartYear" />
        </v-col>
        <v-col cols="12" md="4" sm="12">
          <div style="font-weight: bold; padding: 3px 10px 10px 10px">
            DAFTAR PENGUJIAN TERLAMBAT
          </div>
          <List
            dbref="UJI_SelNeedAttention"
            :dbparams="{ nocache: true }"
            class="list-attentiion"
            :selectOnLoad="true"
          >
            <template v-slot="{ row }">
              <div
                style="font-size: 13px; margin-bottom: 10px; padding: 0 10px"
              >
                <div style="color: gray; float: right">
                  {{ row.TglMasuk | format('DD MMM') }}
                </div>
                <div
                  :style="{ color: getRowColor(row) }"
                  style="
                    font-weight: bold;
                    font-family: Raleway;
                    text-transform: uppercase;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: calc(100% - 80px);
                    height: 20px;
                    white-space: nowrap;
                  "
                >
                  {{ row.NamaPelanggan }}
                </div>
                <div style="color: gray; float: right">
                  {{
                    row.StatusID >= 8 && row.BayarStatus != 1
                      ? 'BELUM BAYAR'
                      : row.StatusName
                  }}
                </div>
                <div style="color: gray; display: flex">
                  <span
                    style="
                      font-size: 10px;
                      padding: 3px 5px 0 5px;
                      background: #ddd;
                      border-radius: 5px;
                      color: #333;
                      margin-right: 5px;
                    "
                  >
                    {{ row.NoPengujian }}
                  </span>
                  <div
                    style="
                      overflow: hidden;
                      text-overflow: ellipsis;
                      width: 160px;
                      height: 20px;
                      white-space: nowrap;
                    "
                  >
                    {{ row.JenisUji }}
                  </div>
                </div>
              </div>
            </template>
            <template v-slot:empty>
              <div style="font-size: 14px; text-align: center; padding: 20px">
                Tidak ada pengujian yang terlambat.
              </div>
            </template>
          </List>
        </v-col>
        <v-col cols="12" md="4" sm="12"></v-col>
      </v-row>
    </div>
    <GrandView />
  </div>
</template>
<script>
import SumberBulanan from './SumberBulanan.vue'
import SumberTahunan from './SumberTahunan.vue'
import GrandView from './GrandView.vue'
export default {
  components: {
    SumberBulanan,
    SumberTahunan,
    GrandView,
  },
  data: () => ({
    chartYear: new Date().getFullYear(),
    progress: {},
    datacollection: {},
    lastData: {},
    totalPAD: 0,
    chartVal: 0,
  }),
  computed: {
    yearOptions() {
      const years = []
      for (let i = 0; i < 5; i++) {
        years.push({
          val: new Date().getFullYear() - i,
          txt: new Date().getFullYear() - i,
        })
      }
      return years
    },
  },
  methods: {
    SelectChanged(val) {
      this.chartVal = val
    },
    getRowColor(row) {
      if (row.StatusID >= 8 && row.BayarStatus != 1) {
        return 'red'
      } else if (row.StatusID >= 8 && row.BayarStatus == 1) {
        return 'green'
      } else if (row.StatusID < 8) {
        return 'blue'
      }
    },
  },
}
</script>
<style lang="scss">
.list-attentiion {
  max-height: 100%;
}
</style>
