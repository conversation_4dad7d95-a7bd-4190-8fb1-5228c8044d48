const express = require('express')
const router = express.Router()
const Reporter = require('./generator')
const path = require('path')
const fs = require('fs')
const JSZip = require('jszip')
const carbone = require('carbone')
let db = require('../../common/db')
const moment = require('moment')
const Excel = require('exceljs')
const shell = require('shelljs')
const custom = require('./custom')
const HtmlGenerator = require('./HtmlGenerator')
const report = require('./report')
moment.locale('id')

// CARBONE specific setup
carbone.addFormatters({
  // this formatter can be used in a template with {d.myBoolean:yesOrNo()}
  format: function (val, fmt) {
    if (val instanceof Date) {
      return moment(val).format(fmt.replace(/_/g, ' '))
    } else if (!isNaN(val)) {
      return parseFloat(val)
        .toFixed(2)
        .replace(/\d(?=(\d{3})+\.)/g, '$&.')
        .replace(/\.00$/, ' ')
    } else {
      return val
    }
  },
  boolstr: function (val, fmt) {
    const noyes = fmt.split('|')
    if (val === undefined || val === null || val === '') return ''
    else if (val === 0 || val === false) return noyes[0]
    else return noyes[1]
  }
})



router.post('/GetParamsVue', async function (req, res) {
  const dbp = await db.pool.query(
    `SELECT * FROM information_schema.PARAMETERS
    WHERE SPECIFIC_SCHEMA = '${db.dbname}'
    AND SPECIFIC_NAME = '${req.body.sp}' AND PARAMETER_MODE = 'IN'`
  )

  const sb = []
  for (const idx in dbp) {
    const paramName = dbp[idx].PARAMETER_NAME // .substr(1);
    if (paramName.substr(-3) !== 'Ref') {
      const o = {}
      o.id = paramName
      if (paramName.substr(-2) === 'ID') {
        o.text = paramName.substr(8, paramName.length - 10)
      } else { o.text = paramName.substr(1) }

      if (paramName.substr(-2) === 'ID') {
        // sb +=
        //   '"url":"api/call/' +
        //   paramName.substr(1, paramName.length - 3) +
        //   '",';
        o.dbref = paramName.substr(1, paramName.length - 3)
        // db += '"Type":"Select",';
        o.type = 'Select'
      } else if (
        dbp[idx].DATA_TYPE === 'datetime' ||
        dbp[idx].DATA_TYPE === 'varchar'
      ) {
        // db += '"Type":"Input",';
        o.type = 'Input'
      } else if (
        dbp[idx].DATA_TYPE === 'datetime' ||
        dbp[idx].DATA_TYPE === 'date'
      ) {
        // db += '"Type":"Date",';
        o.type = 'Date'
      } else if (
        dbp[idx].DATA_TYPE === 'boolean' ||
        dbp[idx].DATA_TYPE === 'tinyint'
      ) {
        // db += '"Type":"Checkbox",';
        o.type = 'Checkbox'
      } else {
        db += '"Type":"' + dbp[idx].DATA_TYPE + '"'
      }
      // db += "},";
      sb.push(o)
    }
  }
  // sb += "{";
  // sb += '"Id":"btnReport",';
  // sb += '"Name":"",';
  // sb += '"Type":"VBtn"';
  // sb += "}";
  // sb += "]";

  res.send({
    success: true,
    data: sb,
    message: '',
    type: 'array'
  })
})

router.post('/GetParams', async function (req, res) {
  const dbp = await db.pool.query(
    `SELECT * FROM information_schema.PARAMETERS
    WHERE SPECIFIC_SCHEMA = '${db.dbname}'
    AND SPECIFIC_NAME = '${req.query.sp}' AND PARAMETER_MODE = 'IN'`
  )

  let sb = '['
  for (const idx in dbp) {
    const paramName = dbp[idx].PARAMETER_NAME // .substr(1);
    if (paramName.substr(-3) !== 'Ref') {
      sb += '{'
      sb += '"Id":"' + paramName + '",'
      if (paramName.substr(-2) === 'ID') { sb += '"Name":"' + paramName.substr(8, paramName.length - 10) + '",' } else sb += '"Name":"' + paramName.substr(1) + '",'
      if (paramName.substr(-2) === 'ID') {
        sb +=
          '"url":"api/call/' +
          paramName.substr(1, paramName.length - 3) +
          '",'
        db += '"Type":"select",'
      } else if (
        dbp[idx].DATA_TYPE === 'datetime' ||
        dbp[idx].DATA_TYPE === 'varchar'
      ) {
        db += '"Type":"text",'
      } else if (
        dbp[idx].DATA_TYPE === 'datetime' ||
        dbp[idx].DATA_TYPE === 'date'
      ) {
        db += '"Type":"date",'
      } else if (
        dbp[idx].DATA_TYPE === 'boolean' ||
        dbp[idx].DATA_TYPE === 'tinyint'
      ) {
        db += '"Type":"checkbox",'
      } else {
        db += '"Type":"' + dbp[idx].DATA_TYPE + '"'
      }
      db += '},'
    }
  }
  sb += '{'
  sb += '"Id":"btnReport",'
  sb += '"Name":"",'
  sb += '"Type":"button"'
  sb += '}'
  sb += ']'

  res.send({
    Success: true,
    Data: sb,
    Message: '',
    Type: 'array'
  })
})

router.post('/Generic/:type', async function (req, res) {
  req.setTimeout(600000)
  // let rid = req.query.rid.split("/");
  await report.Generic(req, res, req.params.type)
})

router.get('/get/:file', async function (req, res) {
  res.sendFile(path.resolve('tmp/' + req.params.file))
})

router.get('/get/templates/:file', async function (req, res) {
  res.sendFile(path.resolve('tmp/templates/' + req.params.file))
})

router.post('/template/:file', async function (req, res) {
  let {file} = req.params
  let data = await db.exec(req.body.sp, req.body, {returnAll:true})
  if (req.body.renderEngine === 'text') {
    data = data[0]
    const template = fs.readFileSync('tmp/templates/' + req.params.file, 'utf-8')
    let result = ''
    for (const d of data) {
      let txt = template
      for (const c in d) {
        txt = txt.replace(new RegExp(`{{${c}}}`), d[c] || '')
      }
      result += txt
    }
    fs.writeFileSync('tmp/' + file, result)
    res.send({
      success: true,
      data: `/get/${file}`,
      message: 'Report Generated',
      type: 'url'
    })
  } else {
    if (req.body.transformResult) {
      let rs = data
      data = {
        ...rs[0][0]
      }
      for (let k in req.body.transformResult) {
        data[k] = rs[req.body.transformResult[k]]
      }
      let needParse = []
      for (let key in data) {
        if (key.match(/^_json_/)) {
          needParse.push(key)
          // console.log(d[0][key])
        }
      }
      if (needParse.length) {
        for (let key of needParse) {
          let newKey = key.replace(/^_json_/, '')
          data[newKey] = JSON.parse(data[key])
          delete data[key]
        }
      }
    } else {
      data = data[0]
    }
    carbone.render('tmp/templates/' + file, data, async function (
      err,
      result
    ) {
      if (err) {
        return console.error(err)
      }
      if(req.body.imgcols) {
        let p = new Promise((resolve, reject) => {
          const zip = new JSZip();
          zip.loadAsync(result).then((z) => {
            for(let d of data) {
              for(let k of req.body.imgcols) {
                let img = fs.readFileSync(path.resolve(d[k].replace(/^\//,'')))
                if(file.match(/\.ppt/))
                  z.file("ppt/media"+d[k], img)
              }
            }
            z.generateAsync({type:"nodebuffer"}).then((blob) => {
              resolve(blob);                         
            })
          })
        })
        result = await p
      }
      // write the result
      let m = moment().format('HHmmss')
      fs.writeFileSync('tmp/' + m + file, result)
      res.send({
        success: true,
        data: `/get/${m + file}`,
        message: 'Report Generated',
        type: 'url'
      })
    })
  }
})

router.post('/generate/:type', async function (req, res) {
  try {
    if (req.query.sp || req.body.sp) {
      await report.Generic(req, res, req.params.type)
    } else {
      res.send({
        success: true,
        data: null,
        message: 'Cant Generate Report',
        type: 'error'
      })
    }
  } catch (ex) {
    res.send({
      success: false,
      data: ex.message,
      message: 'Error while generating report',
      type: 'error'
    })
  }
})

router.post('/custom/:rpt', async function (req, res) {
  if (req.params.rpt) {
    const filename = await Reporter.Render(custom[req.params.rpt], req.body)
    res.send({
      success: true,
      data: `/get/${filename}.xlsx`,
      message: 'Report Generated',
      type: 'url'
    })
  } else {
    res.send({success: false})
  }
})

module.exports = router
