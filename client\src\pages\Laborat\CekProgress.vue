<template>
  <div style="display: flex">
    <SidePane @item-click="ItemClick" statusId=",4,5," />
    <div v-show="forms.Nama" style="overflow: auto; height: calc(100vh - 66px)">
      <div
        style="
          background: #039ae4;
          color: white;
          padding: 15px 15px 10px 15px;
          display: flex;
        "
      >
        <div style="display: flex; width: calc(100% - 200px)">
          <div
            style="
              max-width: 450px;
              overflow: hidden;
              text-wrap: nowrap;
              text-overflow: ellipsis;
            "
          >
            {{ forms.Nama }}
          </div>
          <div
            style="
              background: white;
              color: #039ae4;
              margin-left: 10px;
              padding: 5px 8px;
              border-radius: 5px;
              font-size: small;
              position: relative;
              top: -3px;
            "
          >
            {{ forms.NoPengujian }}
          </div>
        </div>

        <v-spacer />
        <v-btn
          v-if="forms.StatusID == 4"
          small
          color="success"
          @click="UpdMulaiPengujian"
        >
          MULAI PENGUJIAN
        </v-btn>
        <v-btn
          v-if="forms.StatusID == 5"
          small
          disabled
          style="color: white !important; position: relative; top: -2px"
        >
          DALAM PROSES
        </v-btn>
        <v-btn
          v-if="forms.StatusID == 6"
          small
          disabled
          style="color: white !important; position: relative; top: -2px"
        >
          SELESAI
        </v-btn>
      </div>
      <Grid
        class="table-cekprogress"
        :datagrid.sync="datagrid"
        dbref="UJI_SelCekProgress"
        :doRebind="rebind"
        :dbparams="dbparams"
        :disabled="true"
        :columns="[
          {
            name: 'Parameter Uji',
            value: 'NamaParameter',
            width: '30%',
          },
          {
            name: 'Jml',
            value: 'JmlContoh',
            width: '10%',
          },
          {
            name: 'Keterangan',
            value: 'Keterangan',
            width: '30%',
          },
          {
            name: 'Waktu',
            value: 'Waktu',
            width: '10%',
          },
          {
            name: 'Masalah',
            value: 'Masalah',
            width: '10%',
          },
          {
            name: 'Selesai',
            value: 'Selesai',
            width: '10%',
          },
        ]"
      >
        <template v-slot:row-NamaParameter="{ row }">
          <div
            :class="{
              'is-masalah': row.Ordr == 2,
              'is-done': row.HasilStatus,
            }"
          >
            <div>{{ row.NamaParameter }}</div>
            <div>{{ row.Ordr == 1 ? row.Metode : '' }}</div>
          </div>
        </template>
        <template v-slot:row-Keterangan="{ row }">
          {{ row.Keterangan }}
        </template>
        <template v-slot:row-Waktu="{ row }"> {{ row.Waktu }} Hari </template>
        <template v-slot:row-Masalah="{ row }">
          <v-btn
            x-small
            text
            color="primary"
            @click="ShowMasalah(row.PermohonanID, row.ParameterID)"
            v-show="!row.HasilStatus && row.Ordr == 1 && forms.StatusID == 5"
          >
            <v-icon left>mdi-plus</v-icon>
            Masalah
          </v-btn>
          <v-btn
            x-small
            color="success"
            :outlined="Boolean(row.HasilStatus)"
            :disabled="Boolean(row.HasilStatus)"
            @click="Resolve(row.Metode)"
            v-show="row.Ordr == 2"
          >
            Selesai
          </v-btn>
        </template>
        <template v-slot:row-Selesai="{ row }">
          <v-btn
            x-small
            color="success"
            :outlined="Boolean(row.HasilStatus)"
            :disabled="Boolean(row.HasilStatus)"
            @click="UpdHasil(row.PermohonanID, row.ParameterID, 1)"
            v-show="row.Ordr == 1 && forms.StatusID == 5"
          >
            Selesai
          </v-btn>
        </template>
      </Grid>
      <div
        v-for="(lk, idx) in lembarKerja"
        :key="idx"
        style="
          font-size: 12px;
          background: #f3f3f3;
          display: flex;
          margin-bottom: 1px;
          padding: 8px;
        "
      >
        <v-btn
          small
          text
          color="primary"
          @click="Open(lk.LkUrl, lk.LembarKerjaID)"
        >
          {{ lk.Nama }}
        </v-btn>

        <!-- <v-btn x-small text outlined color="primary" style="margin: 4px" @click="OpenPDF(lk.LkUrl, lk.LembarKerjaID)">
          PDF
        </v-btn> -->
        <v-spacer />
        <v-btn x-small text outlined style="margin: 4px" v-if="lk.ApprovedBy">
          SUDAH DISETUJUI
        </v-btn>
        <v-btn
          x-small
          text
          outlined
          color="error"
          style="margin: 4px"
          v-if="lk.Alasan"
          v-tooltip="lk.Alasan"
        >
          DITOLAK
        </v-btn>
        <!-- <v-btn x-small color="primary" style="margin: 4px"
          v-if="!lk.ApprovedBy && !lk.Alasan"
          @click="Approve(lk.LembarKerjaID)">
          SETUJUI
        </v-btn>
         -->
        <!-- <v-btn x-small color="error" style="margin: 4px"
          v-if="!lk.Alasan"
          @click="Reject(lk.LembarKerjaID)">
          TOLAK
        </v-btn> -->
      </div>
    </div>
    <Modal
      title="Tambah Keterangan Masalah"
      :show.sync="masalah.show"
      @submit="InsMasalah"
    >
      <TextArea
        :value.sync="forms.Masalah"
        placeholder="Keterangan Masalah"
        width="350px"
        style="height: 100px"
      />
    </Modal>
  </div>
</template>
<script>
import SidePane from '../Loket/SidePane.vue'
export default {
  components: {
    SidePane,
  },
  data: () => ({
    datagrid: [],
    dbparams: { PermohonanID: 0 },
    lembarKerja: [],
    forms: {
      PermohonanID: 0,
      ParameterID: 0,
      Masalah: '',
    },
    rebind: 1,
    masalah: {
      show: false,
    },
  }),
  computed: {
    showSubmitButton() {
      let unfinished = this.datagrid.filter((d) => !d.HasilStatus)
      let newlk = this.lembarKerja.filter((d) => !d.Alasan && !d.ApprovedBy)
      return unfinished.length == 0 && newlk.length > 0
    },
  },
  methods: {
    async ItemClick(val) {
      this.Populate(val.PermohonanID)
    },
    async Populate(id) {
      this.dbparams = { PermohonanID: id }
      var ret = await this.$api.call('UJI.SelPermohonan', {
        PermohonanID: id,
      })
      if (ret.data.length) {
        this.forms = ret.data[0]
      } else {
        this.forms = {}
      }
      // if (this.forms.StatusID >= 5) {
      this.PopulateLK(id)
      // } else {
      //   this.lembarKerja = []
      // }
    },
    Open(url) {
      window.open(this.$api.url + url, '_blank')
      // window.open(this.$api.url + '/reports/uji/lembarkerja/' + id, '_blank')
    },
    OpenPDF(url) {
      window.open(this.$api.url + url.replace(/\.\w{3,4}$/, 'pdf'), '_blank')
    },
    async PopulateLK(id) {
      this.lembarKerja = []
      var ret = await this.$api.call('UJI.SelLembarKerja', {
        PermohonanID: id,
      })
      this.lembarKerja = ret.data
    },
    async UpdMulaiPengujian() {
      if (!confirm('Mulai Pengujian?')) return

      var res = await this.$api.call('UJI_UpdMulaiPerngujian', {
        PermohonanID: this.forms.PermohonanID,
        MulaiStatus: 1,
      })

      if (res.success) {
        this.Populate(this.forms.PermohonanID)
      }
    },
    async UpdHasil(id, param_id, val) {
      let unfinished = this.datagrid.filter((d) => !d.HasilStatus)
      if (unfinished.length <= 1)
        if (!confirm('Pengujian Telah Selesai?')) return

      var res = await this.$api.call('UJI_UpdHasil', {
        PermohonanID: id,
        ParameterID: param_id,
        HasilStatus: val,
      })

      if (res.success) {
        this.Populate(this.forms.PermohonanID)
      }
    },
    async ShowMasalah(id, param_id) {
      this.masalah.show = true
      this.forms.ParameterID = id
      this.forms.ParameterID = param_id
    },
    async InsMasalah() {
      await this.$api.call('UJI_InsMasalah', this.forms)
      this.rebind++
      this.masalah.show = false
    },
    async Resolve(id) {
      await this.$api.call('UJI_SavResolveMasalah', { MasalahID: id })
      this.rebind++
    },
    async Reject(id) {
      let alasan = prompt('Alasan?')
      if (alasan) {
        let ret = await this.$api.call('UJI_SavLembarKerjaRejection', {
          LembarKerjaID: id,
          Alasan: alasan,
        })
        if (ret.success) {
          this.rawUrl = ''
          this.PopulateLK(this.forms.PermohonanID)
          this.rebind++
        }
      }
    },
    async Approve(id) {
      if (confirm('Setujui?')) {
        let ret = await this.$api.call('UJI_SavLembarKerjaApproval', {
          LembarKerjaID: id,
          FilePath: this.rawUrlOri,
        })
        if (ret.success) {
          this.rawUrl = ''
          this.PopulateLK(this.forms.PermohonanID)
          this.rebind++
        }
      }
    },
    async fileUploaded(file, infos) {
      this.$api.notify('Upload Sukses')

      let rex = await this.$api.call('UJI_SavLembarKerja', {
        PermohonanID: this.forms.PermohonanID,
        XmlRawUrl: infos,
      })
      if (rex.success) {
        await this.PopulateLK(this.forms.PermohonanID)
        for (let lk of this.lembarKerja) {
          this.$api.get('/reports/uji/convert-lk/' + lk.LembarKerjaID)
        }
      }
    },
  },
}
</script>
<style lang="scss">
.table-cekprogress {
  table {
    min-width: 750px;
    width: calc(100vw - 378px);
    .is-masalah {
      padding-left: 10px;
      position: absolute;
      margin-top: -9px;
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
      &.is-done {
        color: gray;
      }
    }
  }
}
</style>
