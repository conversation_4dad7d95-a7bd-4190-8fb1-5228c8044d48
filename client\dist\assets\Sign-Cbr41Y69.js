import{n,a as r,l,_ as i,g as o,w as p,i as d}from"./index-DYIZrBBo.js";const c={data:()=>({keyword:""}),props:{statusId:String,rebind:Number,addButton:<PERSON>olean},computed:{dbparams(){return{IsApproved:1,Keyword:this.keyword||""}}},methods:{ItemClick(t){this.$emit("item-click",t)},DaftarBaru(){this.$emit("item-click",{PengujianID:0})}}};var h=function(){var a=this,e=a._self._c;return e("div",{staticClass:"sidepane"},[e("div",{staticStyle:{padding:"10px",display:"flex"}},[e(r,{staticClass:"searchbar",attrs:{type:"text",value:a.keyword,placeholder:"Cari ..",width:"270px",rightIcon:"mdi-magnify"},on:{"update:value":function(s){a.keyword=s}}})],1),e("div",{staticStyle:{height:"calc(100% - 47px)"}},[e(l,{attrs:{dbref:"UJI_SelTandaTanganList",dbparams:a.dbparams,height:a.addButton?"calc(100% - 60px)":"100%",rebind:a.rebind,selectOnLoad:!0},on:{itemClick:a.ItemClick},scopedSlots:a._u([{key:"default",fn:function({row:s}){return[e("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"0 10px"}},[e("div",{staticStyle:{color:"gray",float:"right"}},[a._v(" "+a._s(a._f("format")(s.TglMasuk))+" ")]),e("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[a._v(" "+a._s(s.NamaPelanggan)+" ")]),e("div",{staticStyle:{color:"gray",float:"right"}},[a._v(" "+a._s(s.StatusID>=8&&s.BayarStatus!=1?"BELUM BAYAR":s.SignedUrl?"SUDAH DITANDATANGAN":s.StatusName)+" ")]),e("div",{staticStyle:{color:"gray",display:"flex"}},[e("span",{staticStyle:{"font-size":"10px",padding:"3px 5px 0 5px",background:"#ddd","border-radius":"5px",color:"#333","margin-right":"5px"}},[a._v(" "+a._s(s.NoPengujian)+" ")]),e("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis",width:"160px",height:"20px","white-space":"nowrap"}},[a._v(" "+a._s(s.NamaLK||"-")+" ")])])])]}}])}),a.addButton?e("div",{staticStyle:{padding:"10px","border-top":"1px solid #ddd"}},[e(i,{staticStyle:{width:"calc(100% - 10px)"},attrs:{outlined:"",color:"primary"},on:{click:a.DaftarBaru}},[e(o,{attrs:{left:""}},[a._v("mdi-plus")]),a._v(" DAFTAR BARU ")],1)],1):a._e()],1)])},u=[],f=n(c,h,u,!1,null,null);const m=f.exports,_={components:{SidePane:m},data:()=>({rawUrl:"",signedUrl:"",resetFile:0,forms:{},passphrase:"",showPassphrase:!1,loading:!1}),methods:{ShowSertifikat(t){this.forms=t,this.rawUrl=this.$api.url+t.RawUrl.replace(/(xlsx|docx)$/,"pdf"),this.signedUrl=t.SignedUrl,t.SignedUrl&&(this.rawUrl=this.$api.url+t.SignedUrl.replace(/(xlsx|docx)$/,"pdf"))},async MenuManual(t){if(t==="Download"){let a=await this.$api.post("/reports/uji/manual-download",{PermohonanID:this.forms.PermohonanID,FilePath:this.forms.RawUrl});window.open(this.$api.url+"/reports/get/"+a.RawUrl,"_blank")}else t==="Upload"&&this.$refs.uploader.click()},async uploadFile(t){if(!confirm("Anda yakin TTD manual?"))return;var a=new FormData;a.append("file",t.target.files[0]);let e=await this.$api.upload(a);if(e.success){let s=e.data;e=await this.$api.post("/reports/uji/manual-sign",{PermohonanID:this.forms.PermohonanID,FilePath:this.forms.RawUrl,SignedUrl:s}),e.success?(this.$api.notify(e.message,"success"),this.rawUrl=""):(this.$api.notify(e.message,"error"),this.passphrase="")}this.resetFile++},async Sign(){this.showPassphrase=!1,this.loading=!0;let t=await this.$api.post("/reports/uji/sign",{PermohonanID:this.forms.PermohonanID,Passphrase:this.passphrase,FilePath:this.rawUrl.replace(this.$api.url,"")});this.loading=!1,t.success?(this.$api.notify(t.message,"success"),this.rawUrl=""):(this.$api.notify(t.message,"error"),this.passphrase="")}}};var g=function(){var a=this,e=a._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{on:{"item-click":a.ShowSertifikat}}),a.rawUrl?e("div",{staticClass:"right-pane"},[e("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:a.rawUrl,frameborder:"0"}}),e("div",{staticStyle:{position:"fixed",bottom:"20px",right:"40px",display:"flex"}},[e(p,{attrs:{menu:["Download","Upload"]},on:{"item-click":function(s){return a.MenuManual(s)}},scopedSlots:a._u([{key:"default",fn:function({on:s}){return[e(i,[e(o,a._g({},s),[a._v(" mdi-dots-vertical ")])],1)]}}],null,!1,2745980243)}),e(i,{directives:[{name:"show",rawName:"v-show",value:!a.loading,expression:"!loading"}],staticClass:"close-right-pa ne",staticStyle:{"margin-right":"8px"},on:{click:function(s){a.rawUrl=""}}},[a._v(" BATAL ")]),e(i,{directives:[{name:"show",rawName:"v-show",value:!a.signedUrl,expression:"!signedUrl"}],attrs:{color:"primary",disabled:a.loading},on:{click:function(s){a.showPassphrase=!0}}},[a._v(" TANDA TANGANI ")]),e(r,{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],key:a.resetFile,ref:"uploader",attrs:{type:"file"},on:{change:a.uploadFile}})],1)]):a._e(),e(d,{attrs:{title:"Passphrase:",show:a.showPassphrase},on:{"update:show":function(s){a.showPassphrase=s},submit:a.Sign}},[e(r,{attrs:{type:"password",value:a.passphrase},on:{"update:value":function(s){a.passphrase=s}}})],1)],1)},w=[],v=n(_,g,w,!1,null,null);const x=v.exports;export{x as default};
