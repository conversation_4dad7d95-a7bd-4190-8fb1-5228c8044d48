<template>
  <Page title="Data Banner">
    <div
      style="
        margin-left: 10px;
        display: flex;
        flex-wrap: wrap;
        width: calc(100vw - 350px);
      "
    >
      <div v-for="(item, idx) in items" :key="idx" style="margin: 5px">
        <div
          style="
            width: 300px;
            height: 160px;
            background-size: cover;
            position: relative;
          "
          :style="{
            backgroundImage: `url(${item.background})`,
          }"
        >
          <div style="position: absolute; top: 60px; left: 5px">
            <div
              style="
                color: white;
                background: rgba(0, 0, 0, 0.5);
                text-transform: uppercase;
                font-weight: bold;
                padding: 0 5px;
              "
            >
              {{ item.title }}
            </div>
            <v-btn color="primary" x-small v-if="item.buttonText">
              {{ item.buttonText }}
            </v-btn>
          </div>
        </div>
        <div style="background: #f3f3f3; padding: 5px 10px">
          <v-icon>mdi-eye</v-icon>
          <v-icon style="margin-left: 10px" color="error" @click="Delete(item)">
            mdi-trash-can
          </v-icon>
          <v-icon style="float: right" @click="Edit(item)"> mdi-pencil </v-icon>
        </div>
      </div>
    </div>
    <div style="padding: 15px">
      <v-btn color="primary" @click="Edit(0)">
        <v-icon left>mdi-plus</v-icon>
        TAMBAH BANNER
      </v-btn>
    </div>
    <Modal title="Tambah Banner" :show.sync="showBanner" @submit="Save">
      <div>
        <Uploader
          :value.sync="banner.background"
          width="300px"
          height="160px"
        ></Uploader>
        <Input
          type="text"
          :value.sync="banner.title"
          placeholder="Title"
          style="width: 300px"
        />
      </div>
    </Modal>
  </Page>
</template>
<script>
export default {
  data: () => ({
    items: [],
    showBanner: false,
    banner: {
      bannerId: 0,
    },
  }),
  mounted() {
    this.Populate()
  },
  methods: {
    async Populate() {
      let ret = await this.$api.call('WEB.SelBanner', { nocache: true })
      this.items = ret.data
    },
    Edit(id) {
      if (id == 0) {
        this.banner = {
          bannerId: 0,
        }
      } else {
        this.banner = id
      }
      this.showBanner = true
    },
    async Delete(item) {
      if (!confirm('Yakin Menghapus?')) return
      let ret = await this.$api.call('WEB.DelBanner', item)
      if (ret.success) {
        this.showBanner = false
        this.Populate()
      }
    },
    async Save() {
      let ret = await this.$api.call('WEB.SavBanner', this.banner)
      if (ret.success) {
        this.showBanner = false
        this.Populate()
      }
    },
  },
}
</script>
<style lang="scss">
.modal-tambah-banner {
  .--input {
    width: 100% !important;
  }
}
</style>
