import{n as p,c as u,k as d,_ as h,g as s,q as m,r as v,s as _,t as w,u as f,v as l}from"./index-DYIZrBBo.js";import{A as y}from"./AlatDet-ecQII-bj.js";const g={components:{AlatDet:y},data:()=>({tahun:new Date().getFullYear(),items:[],showDet:!1,popover:{x:0,y:0,show:!1,item:null,month:null},menus:[{title:"Rencan<PERSON> Pemeliharaan",cmd:"MP",icon:"mdi-circle",iconColor:"#ddd"},{title:"Real<PERSON>si Pemeliharaan",cmd:"MR",icon:"mdi-circle",iconColor:"primary"},{title:"Rencan<PERSON> Kalibrasi",cmd:"KP",icon:"mdi-square",iconColor:"#FFE0B2"},{title:"<PERSON><PERSON><PERSON>si",cmd:"KR",icon:"mdi-square",iconColor:"#EF6C00"},{title:"Clear",cmd:"X",icon:"mdi-close",iconColor:"danger"}],months:["Jan","Feb","Mar","Apr","Mei","Jun","Jul","Agu","Sep","Okt","Nov","Des"]}),computed:{yearOptions(){const i=[];for(let t=-1;t<3;t++)i.push({val:new Date().getFullYear()-t,txt:new Date().getFullYear()-t});return i}},watch:{tahun(){this.populate()},showDet(i){i||this.populate()}},mounted(){this.populate()},methods:{async populate(){let i=document.querySelector("#tbl-jadwal td.active");i&&i.classList.remove("active");let t=await this.$api.call("PML_SelJadwal",{Tahun:this.tahun});this.items=t.data},async PopulateRencana(){await this.$api.call("PML_SavJadwalPlan",{Tahun:this.tahun}),this.populate()},openMenu(i,t,e){this.popover.item=t,this.popover.AlatId=t.AlatId,this.popover.Tanggal=l(e+" 01 "+this.tahun).format("YYYY-MM-DD"),i.preventDefault();let a=document.querySelector("#tbl-jadwal td.active");a&&a.classList.remove("active"),i.target.classList.add("active"),this.popover.show=!1,this.popover.x=i.clientX,this.popover.y=i.clientY,this.$nextTick(()=>{this.popover.show=!0})},async menuClick(i){const t=this.menus.find(e=>e.title==i);if(i.match(/Realisasi/))this.popover.Aktifitas=i.match(/kalibrasi/i)?"kalibrasi":"pemeliharaan",this.showDet=!0;else if(t){let e=l(this.popover.Tanggal).format("MMM");this.popover.item[e]+=t.cmd,await this.$api.call("PML_SavJadwal",{...this.popover,Tipe:t.cmd}),this.populate()}},async Print(){let i=await this.$api.post("/reports/template/KalibrasiPemeliharaan.docx",{sp:"PML_RptJadwal"});i.success&&this.$api.download("/report/"+i.data)}}};var x=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{height:"100vh"}},[e("div",{staticStyle:{height:"calc(100vh - 80px)",overflow:"auto"}},[e("div",{staticStyle:{display:"flex",width:"1150px"}},[e(u,{attrs:{width:"100px",items:t.yearOptions,value:t.tahun},on:{"update:value":function(a){t.tahun=a}}}),e(d),e(h,{staticStyle:{"margin-top":"3px"},attrs:{small:"",outlined:"",color:"primary"},on:{click:t.PopulateRencana}},[t._v(" ISI RENCANA ")]),e(s,{staticStyle:{"margin-left":"8px"},on:{click:t.Print}},[t._v("mdi-printer")])],1),e("table",{staticClass:"tbl-jadwal",attrs:{id:"tbl-jadwal"}},[e("thead",[e("tr",[e("th",{staticStyle:{width:"350px"}},[t._v("Nama Alat")]),t._l(t.months,function(a){return e("th",{key:a},[t._v(t._s(a))])})],2)]),e("tbody",t._l(t.items,function(a,n){return e("tr",{key:n},[e("td",{staticStyle:{width:"350px"}},[e("div",{staticStyle:{"font-size":"14px","font-weight":"bold"}},[t._v(" "+t._s(a.NamaAlat)+" "+t._s(a.Merk?"(".concat(a.Merk,")"):"")+" ")]),e("div",{staticStyle:{"font-size":"small",color:"gray"}},[t._v(" "+t._s(a.NomorAlat||"-")+" ")])]),t._l(t.months,function(o,r){return e("td",{key:o,on:{contextmenu:function(c){return t.openMenu(c,a,r+1)}}},[e("div",[e(s,{directives:[{name:"show",rawName:"v-show",value:a[o].includes("MP"),expression:"item[m].includes('MP')"}],attrs:{color:"#ddd"}},[t._v("mdi-circle")]),e(s,{directives:[{name:"show",rawName:"v-show",value:a[o].includes("MR"),expression:"item[m].includes('MR')"}],attrs:{color:"primary"}},[t._v(" mdi-circle ")])],1),e("div",[e(s,{directives:[{name:"show",rawName:"v-show",value:a[o].includes("KP"),expression:"item[m].includes('KP')"}],attrs:{color:"#FFE0B2"}},[t._v("mdi-square")]),e(s,{directives:[{name:"show",rawName:"v-show",value:a[o].includes("KR"),expression:"item[m].includes('KR')"}],attrs:{color:"#EF6C00"}},[t._v(" mdi-square ")])],1)])})],2)}),0)]),e(m,{attrs:{"position-x":t.popover.x,"position-y":t.popover.y,absolute:"","offset-y":""},model:{value:t.popover.show,callback:function(a){t.$set(t.popover,"show",a)},expression:"popover.show"}},[e(v,{attrs:{dense:""}},t._l(t.menus,function(a,n){return e(_,{key:n,on:{click:function(o){return t.menuClick(a.title)}}},[e(w,[e(s,{attrs:{color:a.iconColor}},[t._v(t._s(a.icon))])],1),e(f,[t._v(t._s(a.title))])],1)}),1)],1),e("alat-det",{attrs:{show:t.showDet,alatId:t.popover.AlatId,aktifitas:t.popover.Aktifitas,tanggal:t.popover.Tanggal},on:{"update:show":function(a){t.showDet=a}}})],1)])},k=[],M=p(g,x,k,!1,null,null);const S=M.exports;export{S as default};
