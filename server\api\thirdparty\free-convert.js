const axios = require("axios");
var http = require('https');
var fs = require('fs');

const fc = axios.create({
  baseURL: "https://api.freeconvert.com/v1",
  withCredentials: true,
  timeout: 9000000,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
    Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************.NKhw8gtjwvY_qWVvTwR_Ijj5vckWBi4kUDpxB4gXHPg`,
  }
})

const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
}

const download = function(url, dest) {
  return new Promise((resolve, reject) => {
    let file = fs.createWriteStream(dest);
    http.get(url, function(response) {
      response.pipe(file);
      file.on('finish', function() {
        file.close(resolve);  // close() is async, call cb after close completes.
      });
    });
  })
}

const exportFile = async (srcUrl, destPath) => {
  let paths = srcUrl.split('/')
  paths = paths[paths.length - 1]
  let [filename, ftype] = paths.split('.')
  console.log(filename, ftype)

  let c = await fc.post('/process/jobs', {
    tag: "conversion",
    tasks: {
      import: {
        operation: "import/url",
        url: srcUrl,
        // filename: "some.jpg", // optional
      },
      convert: {
        operation: "convert",
        input: "import",
        output_format: "pdf",
        options: {
          // png_compression_quality: 70,
        },
      },
    },
  })

  console.log(c.data.links.self)

  await sleep(20)
  let url = ""
  do {
    let d = await fc.get(c.data.links.self)
    if (d.data.tasks[1].status == "completed") {
      url = d.data.tasks[1].result.url
    }
    console.log(d.data.tasks[1].status)
    await sleep(5)
  } while(!url)
  console.log(url)
  
  await download(url, destPath);
  
  // process.exit()
}

// exportFile("https://silakon.dpubinmarcipka.jatengprov.go.id/reports/get/test.xlsx", "pdf")
module.exports = {
  exportFile
}