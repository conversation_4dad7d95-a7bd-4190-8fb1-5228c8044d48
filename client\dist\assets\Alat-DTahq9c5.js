import{x as l,M as u,y as h,T as m,I as g,z as p,A as v,B as _,C as f,D as r,n as S,o as y,r as b,s as w,E as z,u as k,F as I,_ as n,g as A}from"./index-DYIZrBBo.js";import{A as $}from"./AlatDet-ecQII-bj.js";import{_ as C}from"./VListItemAction-Cb7Lha4G.js";const o=l(u).extend({name:"v-responsive",props:{aspectRatio:[String,Number],contentClass:String},computed:{computedAspectRatio(){return Number(this.aspectRatio)},aspectStyle(){return this.computedAspectRatio?{paddingBottom:1/this.computedAspectRatio*100+"%"}:void 0},__cachedSizer(){return this.aspectStyle?this.$createElement("div",{style:this.aspectStyle,staticClass:"v-responsive__sizer"}):[]}},methods:{genContent(){return this.$createElement("div",{staticClass:"v-responsive__content",class:this.contentClass},h(this))}},render(s){return s("div",{staticClass:"v-responsive",style:this.measurableStyles,on:this.$listeners},[this.__cachedSizer,this.genContent()])}}),d=typeof window<"u"&&"IntersectionObserver"in window,x=l(o,m).extend({name:"v-img",directives:{intersect:g},props:{alt:String,contain:Boolean,eager:Boolean,gradient:String,lazySrc:String,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},position:{type:String,default:"center center"},sizes:String,src:{type:[String,Object],default:""},srcset:String,transition:{type:[Boolean,String],default:"fade-transition"}},data(){return{currentSrc:"",image:null,isLoading:!0,calculatedAspectRatio:void 0,naturalWidth:void 0,hasError:!1}},computed:{computedAspectRatio(){return Number(this.normalisedSrc.aspect||this.calculatedAspectRatio)},normalisedSrc(){return this.src&&typeof this.src=="object"?{src:this.src.src,srcset:this.srcset||this.src.srcset,lazySrc:this.lazySrc||this.src.lazySrc,aspect:Number(this.aspectRatio||this.src.aspect)}:{src:this.src,srcset:this.srcset,lazySrc:this.lazySrc,aspect:Number(this.aspectRatio||0)}},__cachedImage(){if(!(this.normalisedSrc.src||this.normalisedSrc.lazySrc||this.gradient))return[];const s=[],t=this.isLoading?this.normalisedSrc.lazySrc:this.currentSrc;this.gradient&&s.push("linear-gradient(".concat(this.gradient,")")),t&&s.push('url("'.concat(t,'")'));const e=this.$createElement("div",{staticClass:"v-image__image",class:{"v-image__image--preload":this.isLoading,"v-image__image--contain":this.contain,"v-image__image--cover":!this.contain},style:{backgroundImage:s.join(", "),backgroundPosition:this.position},key:+this.isLoading});return this.transition?this.$createElement("transition",{attrs:{name:this.transition,mode:"in-out"}},[e]):e}},watch:{src(){this.isLoading?this.loadImage():this.init(void 0,void 0,!0)},"$vuetify.breakpoint.width":"getSrc"},mounted(){this.init()},methods:{init(s,t,e){if(!(d&&!e&&!this.eager)){if(this.normalisedSrc.lazySrc){const i=new Image;i.src=this.normalisedSrc.lazySrc,this.pollForSize(i,null)}this.normalisedSrc.src&&this.loadImage()}},onLoad(){this.getSrc(),this.isLoading=!1,this.$emit("load",this.src),this.image&&(this.normalisedSrc.src.endsWith(".svg")||this.normalisedSrc.src.startsWith("data:image/svg+xml"))&&(this.image.naturalHeight&&this.image.naturalWidth?(this.naturalWidth=this.image.naturalWidth,this.calculatedAspectRatio=this.image.naturalWidth/this.image.naturalHeight):this.calculatedAspectRatio=1)},onError(){this.hasError=!0,this.$emit("error",this.src)},getSrc(){this.image&&(this.currentSrc=this.image.currentSrc||this.image.src)},loadImage(){const s=new Image;this.image=s,s.onload=()=>{s.decode?s.decode().catch(t=>{v("Failed to decode image, trying to render anyway\n\n"+"src: ".concat(this.normalisedSrc.src)+(t.message?"\nOriginal error: ".concat(t.message):""),this)}).then(this.onLoad):this.onLoad()},s.onerror=this.onError,this.hasError=!1,this.sizes&&(s.sizes=this.sizes),this.normalisedSrc.srcset&&(s.srcset=this.normalisedSrc.srcset),s.src=this.normalisedSrc.src,this.$emit("loadstart",this.normalisedSrc.src),this.aspectRatio||this.pollForSize(s),this.getSrc()},pollForSize(s,t=100){const e=()=>{const{naturalHeight:i,naturalWidth:a}=s;i||a?(this.naturalWidth=a,this.calculatedAspectRatio=a/i):!s.complete&&this.isLoading&&!this.hasError&&t!=null&&setTimeout(e,t)};e()},genContent(){const s=o.options.methods.genContent.call(this);return this.naturalWidth&&this._b(s.data,"div",{style:{width:"".concat(this.naturalWidth,"px")}}),s},__genPlaceholder(){const s=h(this,"placeholder");if(s){const t=this.isLoading?[this.$createElement("div",{staticClass:"v-image__placeholder"},s)]:[];return this.transition?this.$createElement("transition",{props:{appear:!0,name:this.transition}},t):t[0]}}},render(s){const t=o.options.render.call(this,s),e=p(t.data,{staticClass:"v-image",attrs:{"aria-label":this.alt,role:this.alt?"img":void 0},class:this.themeClasses,directives:d?[{name:"intersect",modifiers:{once:!0},value:{handler:this.init,options:this.options}}]:void 0});return t.children=[this.__cachedSizer,this.__cachedImage,this.__genPlaceholder(),this.genContent()],s(t.tag,e,t.children)}}),c=l(f,u,_).extend({name:"v-avatar",props:{left:Boolean,right:Boolean,size:{type:[Number,String],default:48}},computed:{classes(){return{"v-avatar--left":this.left,"v-avatar--right":this.right,...this.roundedClasses}},styles(){return{height:r(this.size),minWidth:r(this.size),width:r(this.size),...this.measurableStyles}}},render(s){const t={staticClass:"v-avatar",class:this.classes,style:this.styles,on:this.$listeners};return s("div",this.setBackgroundColor(this.color,t),h(this))}}),R=c.extend({name:"v-list-item-avatar",props:{horizontal:Boolean,size:{type:[Number,String],default:40}},computed:{classes(){return{"v-list-item__avatar--horizontal":this.horizontal,...c.options.computed.classes.call(this),"v-avatar--tile":this.tile||this.horizontal}}},render(s){const t=c.options.render.call(this,s);return t.data=t.data||{},t.data.staticClass+=" v-list-item__avatar",t}}),E={components:{AlatDet:$},data:()=>({alatId:null,forms:{},showDet:!1,imageDet:"",activities:[]}),computed:{...y({user:"getUser"})},created(){this.alatId=this.$route.params.id,this.populate()},methods:{async populate(){var e;let s=await this.$api.call("WEB.SelAlat",{AlatId:this.alatId});this.forms=((e=s.data)==null?void 0:e[0])||{};let t=await this.$api.call("WEB.SelAlatActivity",{AlatId:this.alatId});this.activities=t.data||[]}}};var W=function(){var t=this,e=t._self._c;return e("div",[e("br"),e("div",{staticStyle:{"background-color":"silver",width:"80vw",height:"80vw","border-radius":"10px",margin:"auto","background-position":"center","background-size":"cover"},style:{backgroundImage:"url(https://silakon.dpubinmarcipka.jatengprov.go.id"+t.forms.Foto+")"}}),e("br"),e("div",{staticStyle:{width:"100vw",padding:"15px"}},[e("div",{staticStyle:{"font-size":"x-large","font-family":"raleway"}},[t._v(" "+t._s(t.forms.NamaAlat)+" ")]),e("div",{staticStyle:{color:"gray"}},[t._v(" "+t._s(t.forms.Merk)+" / "+t._s(t.forms.Tipe)+" ("+t._s(t.forms.Tahun)+") ")]),e("br"),e(b,{attrs:{subheader:"","two-line":""}},t._l(t.activities,function(i){return e(w,{key:i.AlatId},[e(R,[e(x,{attrs:{src:"https://silakon.dpubinmarcipka.jatengprov.go.id"+i.Bukti}})],1),e(z,[e(k,[t._v(t._s(t._f("format")(i.Tanggal))+" - "+t._s(i.Aktifitas))]),e(I,[t._v(t._s(i.Keterangan))])],1),e(C,[e(n,{attrs:{icon:""},on:{click:function(a){t.imageDet=i.Bukti}}},[e(A,{attrs:{color:"grey lighten-1"}},[t._v("mdi-information")])],1)],1)],1)}),1),e("br"),t.user?e(n,{staticStyle:{width:"100%"},attrs:{color:"primary"},on:{click:function(i){t.showDet=!0}}},[t._v("TAMBAH DATA")]):t._e()],1),e("div",{directives:[{name:"show",rawName:"v-show",value:t.imageDet!="",expression:"imageDet != ''"}],staticStyle:{width:"100vw",height:"100vw",position:"absolute",bottom:"0",background:"white","text-align":"center","padding-top":"10vw","border-radius":"20px 20px 0 0","box-shadow":"0 0 10px rgba(0, 0, 0, 0.3)"},on:{click:function(i){t.imageDet=""}}},[e("div",{staticStyle:{"background-color":"silver",width:"80vw",height:"80vw","border-radius":"10px",margin:"auto","background-position":"center","background-size":"cover"},style:{backgroundImage:"url(https://silakon.dpubinmarcipka.jatengprov.go.id"+t.imageDet+")"}}),e(n,{staticStyle:{margin:"auto"},attrs:{color:"primary",text:""}},[t._v("TUTUP")])],1),e("alat-det",{attrs:{show:t.showDet,alatId:t.alatId},on:{"update:show":function(i){t.showDet=i}}})],1)},B=[],D=S(E,W,B,!1,null,null);const j=D.exports;export{j as default};
