import{n as i,o as n,i as o,c as r,d as l,b as u,f}from"./index-DYIZrBBo.js";const h={data:()=>({xshow:!1,forms:{}}),props:{show:<PERSON><PERSON><PERSON>,alatId:[String,Number],aktifitas:String,tanggal:[String,Date]},computed:{...n({user:"getUser"})},watch:{show(s){this.xshow=s,s&&(this.aktifitas&&(this.forms.Aktifitas=this.aktifitas),this.tanggal&&(this.forms.Tanggal=this.tanggal))},xshow(s){this.$emit("update:show",s)}},methods:{async Save(){(await this.$api.call("PML.SavAlatActivity",{AlatId:this.alatId,...this.forms})).success&&this.$emit("update:show",!1)}}};var m=function(){var t=this,e=t._self._c;return e(o,{attrs:{title:"Data Detail Alat",show:t.xshow,width:"450px"},on:{"update:show":function(a){t.xshow=a},submit:t.Save}},[e("div",{staticClass:"rounded-forms",staticStyle:{"min-width":"300px"}},[e(r,{staticStyle:{width:"100%"},attrs:{width:"100%",label:"Jenis Aktifitas",items:[{val:"kalibrasi",txt:"Kalibrasi"},{val:"pemeliharaan",txt:"Pemeliharaan"},{val:"kerusakan",txt:"Kerusakan"}],value:t.forms.Aktifitas},on:{"update:value":function(a){return t.$set(t.forms,"Aktifitas",a)}}}),e(l,{staticStyle:{width:"100%"},attrs:{width:"100%",label:"Tanggal",value:t.forms.Tanggal},on:{"update:value":function(a){return t.$set(t.forms,"Tanggal",a)}}}),e(u,{staticStyle:{width:"100%"},attrs:{width:"100%",label:"Keterangan",value:t.forms.Keterangan},on:{"update:value":function(a){return t.$set(t.forms,"Keterangan",a)}}}),t._v(" "),e(f,{attrs:{label:"Bukti",value:t.forms.Bukti},on:{"update:value":function(a){return t.$set(t.forms,"Bukti",a)}}})],1)])},_=[],c=i(h,m,_,!1,null,null);const d=c.exports;export{d as A};
