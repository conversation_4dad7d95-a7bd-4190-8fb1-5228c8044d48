// const {degrees, PDFDocument, rgb, StandardFonts, PDFPageLeaf, PDFDict, PDFName} = require('pdf-lib');
// const fs = require('fs')

// function hex2a(hex) {
//   var str = '';
//   for (var i = 0; i < hex.length; i += 2) {
//     var v = parseInt(hex.substr(i, 2), 16);
//     if (v) str += String.fromCharCode(v);
//   }
//   return str;
// } 

// const parseOutlines = (pdfDoc) => {
//   let outlines = {}
//   let oRef = pdfDoc.catalog.get(PDFName.of('Outlines'))
  
//   let outline = pdfDoc.context.enumerateIndirectObjects()[oRef.objectNumber-1][1]
//   let oCount = outline.dict.get(PDFName.of('Count')).numberValue

//   for(let i = oRef.objectNumber; i<oRef.objectNumber+oCount; i++) {
//     let o = pdfDoc.context.enumerateIndirectObjects()[i][1]
//     let oTitle = hex2a(o.dict.get(PDFName.of('Title')).value).substring(2)
//     //console.log(o.dict.get(PDFName.of('Dest')).array[0].objectNumber, oTitle)
//     outlines[o.dict.get(PDFName.of('Dest')).array[0].objectNumber] = oTitle
//   }
  
//   return outlines
// }

// const main = async () => {
//   const pdfDoc = await PDFDocument.load(fs.readFileSync('../tmp/26174152785-adb.pdf'))
//   // let outline = pdfDoc.catalog.get(PDFName.of('Outlines'))
//   // let outline = pdfDoc.catalog.get(PDFName.of('Outlines'))
//   // console.log(pdfDoc.catalog.get(PDFName.of('Metadata')))

//   // let outs = parseOutlines(pdfDoc)

//   let pages = pdfDoc.getPages()
//   console.log(pages[0].node.dict)
//   // for(let p of pages) {
//   //   // console.log(outs[p.ref.objectNumber])
//   //   console.log(p)
//   // }

//   // console.log(pdfDoc.catalog.keys())
//   // console.log(outline.objectNumber)
//   // let o = pdfDoc.context.enumerateIndirectObjects()[322][1]
//   // let oTitle = hex2a(o.dict.get(PDFName.of('Title')).value).substring(2)
//   // console.log(oTitle)
//   // console.log(o.dict.get(PDFName.of('Dest')).array[0])
  
//   process.exit()
// }
// main ()
// // console.log(hex2a('FEFF004700520041004400200041004700470020004B005300410052'))