!function(){function t(t,n){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,n){if(t){if("string"==typeof t)return e(t,n);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?e(t,n):void 0}}(t))||n&&t&&"number"==typeof t.length){r&&(t=r);var a=0,i=function(){};return{s:i,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){u=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw o}}}}function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",o=a.toStringTag||"@@toStringTag";function s(n,a,i,o){var s=a&&a.prototype instanceof l?a:l,c=Object.create(s.prototype);return r(c,"_invoke",function(n,r,a){var i,o,s,l=0,c=a||[],f=!1,d={p:0,n:0,v:t,a:m,f:m.bind(t,4),d:function(e,n){return i=e,o=0,s=t,d.n=n,u}};function m(n,r){for(o=n,s=r,e=0;!f&&l&&!a&&e<c.length;e++){var a,i=c[e],m=d.p,p=i[2];n>3?(a=p===r)&&(s=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=t):i[0]<=m&&((a=n<2&&m<i[1])?(o=0,d.v=r,d.n=i[1]):m<p&&(a=n<3||i[0]>r||r>p)&&(i[4]=n,i[5]=r,d.n=p,o=0))}if(a||n>1)return u;throw f=!0,r}return function(a,c,p){if(l>1)throw TypeError("Generator is already running");for(f&&1===c&&m(c,p),o=c,s=p;(e=o<2?t:s)||!f;){i||(o?o<3?(o>1&&(d.n=-1),m(o,s)):d.n=s:d.v=s);try{if(l=2,i){if(o||(a="next"),e=i[a]){if(!(e=e.call(i,s)))throw TypeError("iterator result is not an object");if(!e.done)return e;s=e.value,o<2&&(o=0)}else 1===o&&(e=i.return)&&e.call(i),o<2&&(s=TypeError("The iterator does not provide a '"+a+"' method"),o=1);i=t}else if((e=(f=d.n<0)?s:n.call(r,d))!==u)break}catch(e){i=t,o=1,s=e}finally{l=1}}return{value:e,done:f}}}(n,i,o),!0),c}var u={};function l(){}function c(){}function f(){}e=Object.getPrototypeOf;var d=[][i]?e(e([][i]())):(r(e={},i,function(){return this}),e),m=f.prototype=l.prototype=Object.create(d);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,r(t,o,"GeneratorFunction")),t.prototype=Object.create(m),t}return c.prototype=f,r(m,"constructor",f),r(f,"constructor",c),c.displayName="GeneratorFunction",r(f,o,"GeneratorFunction"),r(m),r(m,o,"Generator"),r(m,i,function(){return this}),r(m,"toString",function(){return"[object Generator]"}),(n=function(){return{w:s,m:p}})()}function r(t,e,n,a){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}r=function(t,e,n,a){function o(e,n){r(t,e,function(t){return this._invoke(e,n,t)})}e?i?i(t,e,{value:n,enumerable:!a,configurable:!a,writable:!a}):t[e]=n:(o("next",0),o("throw",1),o("return",2))},r(t,e,n,a)}function a(t,e,n,r,a,i,o){try{var s=t[i](o),u=s.value}catch(t){return void n(t)}s.done?e(u):Promise.resolve(u).then(r,a)}function i(t){return function(){var e=this,n=arguments;return new Promise(function(r,i){var o=t.apply(e,n);function s(t){a(o,r,i,s,u,"next",t)}function u(t){a(o,r,i,s,u,"throw",t)}s(void 0)})}}System.register(["./index-legacy-BUdDePUl.js"],function(e,r){"use strict";var a,o,s,u,l,c,f;return{setters:[function(t){a=t.n,o=t.i,s=t.Q,u=t.v,l=t._,c=t.l,f=t.j}],execute:function(){var r=a({data:function(){return{submitText:["BAYAR","OK"]}},props:{forms:Object},watch:{"forms.show":function(t){t||(this.forms.step=0)}},methods:{Submit:function(){var t=this;return i(n().m(function e(){var r;return n().w(function(e){for(;;)switch(e.n){case 0:if(0!=t.forms.step){e.n=4;break}if("transfer"!=t.forms.PaymentType){e.n=2;break}return e.n=1,t.$api.call("UJI_SavBillingID",t.forms);case 1:(r=e.v).success&&(t.forms.billingId=r.data[0].BillingID,t.forms.expiredDate=u().add(2,"hour").format("DD-MMM-YYYY HH:mm:ss"),t.forms.step=1,t.$emit("refresh")),e.n=3;break;case 2:t.forms.step=1;case 3:e.n=5;break;case 4:t.forms.show=!1;case 5:return e.a(2)}},e)}))()}}},function(){var t=this,e=t._self._c;return e(o,{attrs:{title:"Pembayaran",show:t.forms.show,submitText:t.submitText[t.forms.step]},on:{"update:show":function(e){return t.$set(t.forms,"show",e)},submit:t.Submit}},[e("div",{directives:[{name:"show",rawName:"v-show",value:0==t.forms.step,expression:"forms.step == 0"}],staticStyle:{padding:"5px"}},[t._v(" Pilih metode pembayaran: "),e("div",{staticStyle:{padding:"10px"}},[e(s,{attrs:{value:t.forms.PaymentType,data:"cash",text:"Tunai (datang ke tempat)"},on:{"update:value":function(e){return t.$set(t.forms,"PaymentType",e)}}}),e(s,{attrs:{value:t.forms.PaymentType,data:"transfer",text:"Transfer (Bank Jateng)"},on:{"update:value":function(e){return t.$set(t.forms,"PaymentType",e)}}})],1)]),e("div",{directives:[{name:"show",rawName:"v-show",value:1==t.forms.step,expression:"forms.step == 1"}],staticStyle:{padding:"5px"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:"cash"==t.forms.PaymentType,expression:"forms.PaymentType == 'cash'"}]},[t._v(" Silahkan datang ke kantor BP2 untuk melakukan pembayaran. "),e("br"),t._v(' Permohonan akan tetap dalam status "Belum Dibayar"'),e("br"),t._v(" hingga nanti dibayarkan di loket BP2 ")]),e("div",{directives:[{name:"show",rawName:"v-show",value:"transfer"==t.forms.PaymentType,expression:"forms.PaymentType == 'transfer'"}]},[t._v(" Silahkan melakukan transfer sejumlah: "),e("div",{staticStyle:{"font-size":"larger",background:"#f3f3f3",padding:"10px 15px","border-radius":"5px"}},[t._v(" Rp. "+t._s(t._f("format")(t.forms.total))+",- ")]),e("br"),e("div",[t._v("ke rekening Bank Jateng (Kode Bank: 113)")]),e("div",[t._v("dengan virtual account:")]),e("div",{staticStyle:{"font-size":"larger",background:"#f3f3f3",padding:"10px 15px","border-radius":"5px"}},[t._v(" "+t._s(t.forms.billingId)+" ")]),e("div",[t._v(" sebelum: "),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.forms.expiredDate))])]),e("div",[t._v(" (lihat "),e("a",{attrs:{href:"/web/TataCaraPembayaran.pdf",target:"_blank"}},[t._v("cara pembayaran")]),t._v(") ")])])])])},[],!1,null,null).exports;e("default",a({components:{Bayar:r},data:function(){return{billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0},datalist:[],statusId:",1,",rebind:1}},computed:{dbparams:function(){return{StatusID:this.statusId}}},watch:{"billing.show":function(t){t||(this.rebind++,this.billing.TotalBayar=0)}},mounted:function(){this.statusId="1,2"},methods:{ItemClick:function(){this.billing.TotalBayar=0;var e,n=[],r=t(this.datalist);try{for(r.s();!(e=r.n()).done;){var a=e.value;a.checked&&(this.billing.TotalBayar+=a.TotalBayar,n.push(a.PermohonanID))}}catch(i){r.e(i)}finally{r.f()}this.billing.PermohonanID=n.join(",")},ChangeStatus:function(t){this.statusId=t},ShowBilling:function(t){var e=this;return i(n().m(function r(){var a;return n().w(function(n){for(;;)switch(n.n){case 0:return n.n=1,e.$api.call("UJI_SelBilling",{PermohonanID:t});case 1:(a=n.v).success&&(e.billing={show:!0,step:1,total:a.data[0].TotalBayar,billingId:a.data[0].BillingID,PaymentType:"transfer"});case 2:return n.a(2)}},r)}))()}}},function(){var t=this,e=t._self._c;return e("div",[e("div",{staticStyle:{display:"flex"}},[e("div",[e(l,{attrs:{text:"1,2"!=t.statusId,outlined:"1,2"!=t.statusId,color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("1,2")}}},[t._v(" Permohonan Baru ")])],1),e("div",[e(l,{attrs:{text:"3"!=t.statusId,outlined:"3"!=t.statusId,color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("3")}}},[t._v(" Penyerahan Sample ")])],1),e("div",[e(l,{attrs:{text:"3"!=t.statusId,outlined:"3"!=t.statusId,color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("3")}}},[t._v(" Menunggu Pembayaran ")])],1),e("div",[e(l,{attrs:{text:"4,5,6,7,8"!=t.statusId,outlined:"4,5,6,7,8"!=t.statusId,color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("4,5,6,7,8")}}},[t._v(" Dalam Proses ")])],1),e("div",[e(l,{attrs:{text:"9"!=t.statusId,outlined:"9"!=t.statusId,color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("9")}}},[t._v(" Sudah Selesai ")])],1)]),e("div",[e(c,{attrs:{dbref:"UJI_SelPermohonanList",items:t.datalist,dbparams:t.dbparams,rebind:t.rebind,selectOnLoad:!0},on:{"update:items":function(e){t.datalist=e}},scopedSlots:t._u([{key:"default",fn:function(n){var r=n.row;return[e("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"5px 10px 0 10px",display:"flex"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:"1,2"==t.statusId,expression:"statusId == '1,2'"}],staticStyle:{padding:"8px 20px"}},[e(f,{attrs:{value:r.checked},on:{"update:value":function(e){return t.$set(r,"checked",e)},click:t.ItemClick}})],1),e("div",[e("div",[t._v(" "+t._s(r.NamaPelanggan)+" ")]),e("div",[t._v(t._s(r.NoPengujian)+" | "+t._s(r.JenisUji))])]),e("div",{staticStyle:{padding:"12px 20px","font-weight":"bold",width:"200px","text-align":"right"}},[t._v(" Rp. "+t._s(t._f("format")(r.TotalBayar))+" ")]),e("div",{staticStyle:{padding:"8px 20px"}},["3"==t.statusId?e(l,{attrs:{text:"",outlined:"",small:"",color:"primary"},on:{click:function(e){return t.ShowBilling(r.PermohonanID)}}},[t._v(" BILLING ")]):e(l,{attrs:{text:"",outlined:"",small:"",color:"primary"}},[t._v("DETAIL")])],1)])]}}])}),e(l,{directives:[{name:"show",rawName:"v-show",value:this.billing.TotalBayar,expression:"this.billing.TotalBayar"}],staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:function(e){t.billing.show=!0}}},[t._v(" BAYAR: Rp "+t._s(t._f("format")(this.billing.TotalBayar))+",- ")])],1),e("Bayar",{attrs:{forms:t.billing},on:{"update:forms":function(e){t.billing=e},refresh:function(e){return t.$emit("refresh")}}})],1)},[],!1,null,null).exports)}}})}();
