<template>
  <Modal
    title="Parameter Uji"
    :show.sync="xshow"
    :submit-text="datagrid.length ? 'SIMPAN' : ''"
    @submit="Save"
  >
    <Input
      type="text"
      placeholder="Nama Paket"
      :value.sync="forms.NamaPaket"
      :width="'100%'"
    />
    <div v-if="!jenisId"><PERSON><PERSON><PERSON>/<PERSON></div>
    <Grid
      v-else
      :datagrid.sync="datagrid"
      dbref="UJI.PaketParamDet"
      :dbparams="dbparams"
      :disabled="true"
      :doRebind="rebind"
      height="calc(100vh - 300px)"
      :columns="[
        {
          name: '<PERSON><PERSON>',
          value: '<PERSON>a',
          filter: {
            type: 'search',
          },
        },
        {
          name: 'Metode',
          value: 'Metode',
        },
        {
          name: 'Waktu',
          value: 'Waktu',
          class: 'align-right',
        },
        {
          name: '<PERSON><PERSON>',
          value: '<PERSON>rga',
          class: 'align-right',
        },
      ]"
    >
      <template v-slot:row-Nama="{ row }">
        <Checkbox
          :value.sync="row.Checked"
          style="position: relative; top: -2px"
          :text="row.Nama"
        />
      </template>
      <template v-slot:row-Waktu="{ row }"> {{ row.Waktu }} Hari </template>
      <template v-slot:row-Harga="{ row }"> {{ row.Harga | format }} </template>
    </Grid>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    rebind: 0,
    xshow: false,
    datagrid: [],
  }),
  props: {
    show: Boolean,
    forms: Object,
    jenisId: String,
  },
  computed: {
    dbparams() {
      return { PaketID: this.forms.PaketID, JenisID: this.jenisId }
    },
  },
  watch: {
    xshow(val) {
      this.$emit('update:show', val)
    },
    show(val) {
      this.xshow = val
    },
    // 'forms.show'(val) {
    //   if (!val) {
    //     this.rebind++
    //   }
    // },
  },
  methods: {
    async Save() {
      const ret = await this.$api.call('UJI_SavPaketParamDet', {
        ...this.forms,
        JenisID: this.jenisId,
        ParameterIDs: this.datagrid
          .filter((d) => d.Checked)
          .map((d) => d.ParameterID)
          .join(','),
      })
      if (ret.success) {
        this.$emit('submit')
        this.xshow = false
      }
    },
  },
}
</script>
<style lang="scss">
.align-right {
  text-align: right;
}
.modal-parameter-uji {
  .v-dialog--content {
    overflow-x: hidden;
  }
}
</style>
