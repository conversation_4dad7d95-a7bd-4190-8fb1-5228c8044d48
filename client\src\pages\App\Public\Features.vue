<template>
  <section id="features">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title text-center wow fadeInDown">
          <PERSON><PERSON>-<PERSON><PERSON>2
        </h2>
        <p class="text-center wow fadeInDown">
          <PERSON><PERSON>-<PERSON><PERSON><PERSON>an yang dapat dilakukan<br />
          <PERSON><PERSON> dan Peralatan Provinsi Jawa Tengah
        </p>
      </div>
      <div class="row" style="margin: 0">
        <div class="col-sm-6 wow fadeInLeft">
          <div
            class="media service-box wow fadeInRight"
            @mouseover="showdetail('ujitanah')"
          >
            <div class="pull-left">
              <i class="fa fa-globe"></i>
            </div>
            <div class="media-body">
              <h4 class="media-heading">Pen<PERSON><PERSON><PERSON></h4>
              <p>Jen<PERSON></p>
            </div>
          </div>

          <div
            class="media service-box wow fadeInRight"
            @mouseover="showdetail('ujiair')"
          >
            <div class="pull-left">
              <i class="fa fa-tint"></i>
            </div>
            <div class="media-body">
              <h4 class="media-heading">Pengujian Air</h4>
              <p>Jenis Pengujian Air</p>
            </div>
          </div>

          <div
            class="media service-box wow fadeInRight"
            @mouseover="showdetail('ujibeton')"
          >
            <div class="pull-left">
              <i class="fa fa-building"></i>
            </div>
            <div class="media-body">
              <h4 class="media-heading">Pengujian Bahan Bangunan</h4>
              <p>Jenis Pengujuan Bahan Bangunan</p>
            </div>
          </div>

          <div
            class="media service-box wow fadeInRight"
            @mouseover="showdetail('ujiaspal')"
          >
            <div class="pull-left">
              <i class="fa fa-road"></i>
            </div>
            <div class="media-body">
              <h4 class="media-heading">Pengujian Aspal</h4>
              <p>Jenis Pengujian Aspal</p>
            </div>
          </div>
        </div>
        <div class="col-sm-6">
          <div
            id="detailuji"
            style="
              height: 357px;
              margin-top: 60px;
              overflow: auto;
              padding-right: 10px;
            "
          >
            <ul id="ujiair" class="hide">
              <li v-for="(d, idx) in parameters.ujiair" :key="idx">
                <div>{{ d.Nama }}</div>
                <div>
                  <a
                    v-show="d.Metode"
                    :href="
                      'https://www.google.com/search?q=' +
                      (d.Metode || '').replace(/\s/, '+')
                    "
                    target="_blank"
                    >({{ d.Metode }})</a
                  >
                  <span style="float: right">Rp. {{ d.Harga | format }}</span>
                </div>
                <div v-if="d.MinSample" style="color: grey">
                  Contoh uji: {{ d.MinSample }}
                </div>
              </li>
            </ul>
            <ul id="ujiaspal" class="hide">
              <li v-for="(d, idx) in parameters.ujiaspal" :key="idx">
                <div>{{ d.Nama }}</div>
                <div>
                  <a
                    v-show="d.Metode"
                    :href="
                      'https://www.google.com/search?q=' +
                      (d.Metode || '').replace(/\s/, '+')
                    "
                    target="_blank"
                    >({{ d.Metode }})</a
                  >
                  <span style="float: right">Rp. {{ d.Harga | format }}</span>
                </div>
                <div v-if="d.MinSample" style="color: grey">
                  Contoh uji: {{ d.MinSample }}
                </div>
              </li>
            </ul>
            <ul id="ujibeton" class="hide">
              <li v-for="(d, idx) in parameters.ujibeton" :key="idx">
                <div>{{ d.Nama }}</div>
                <div>
                  <a
                    v-show="d.Metode"
                    :href="
                      'https://www.google.com/search?q=' +
                      (d.Metode || '').replace(/\s/, '+')
                    "
                    target="_blank"
                    >({{ d.Metode }})</a
                  >
                  <span style="float: right">Rp. {{ d.Harga | format }}</span>
                </div>
                <div v-if="d.MinSample" style="color: grey">
                  Contoh uji: {{ d.MinSample }}
                </div>
              </li>
            </ul>
            <ul id="ujitanah" class="">
              <li v-for="(d, idx) in parameters.ujitanah" :key="idx">
                <div>{{ d.Nama }}</div>
                <div>
                  <a
                    v-show="d.Metode"
                    :href="
                      'https://www.google.com/search?q=' +
                      (d.Metode || '').replace(/\s/, '+')
                    "
                    target="_blank"
                    >({{ d.Metode }})</a
                  >
                  <span style="float: right">Rp. {{ d.Harga | format }}</span>
                </div>
                <div v-if="d.MinSample" style="color: grey">
                  Contoh uji: {{ d.MinSample }}
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<script>
export default {
  data: () => ({
    parameters: {},
  }),
  created() {
    this.populate()
  },
  methods: {
    showdetail(id) {
      if (window.$) {
        const $ = window.$
        $('#detailuji ul').addClass('hide')
        $('#' + id).removeClass('hide')
      }
    },
    async populate() {
      let p = {}
      let res = await this.$api.call('WEB_SelParameter')
      for (let d of res.data) {
        if (!p[d.JenisUji]) p[d.JenisUji] = []
        p[d.JenisUji].push(d)
      }

      this.parameters = p
    },
  },
}
</script>
<style lang="scss">
#detailuji {
  li {
    padding: 5px 0 5px 0;
  }
}
</style>
