import{n as d,R as p,P as h,S as f,_ as o,a as r,b as i,c as u,d as m,e as l,f as c,g as v,h as _,i as g}from"./index-DYIZrBBo.js";const P={components:{SidePane:f,ParameterUji:h,ReportPopup:p},data:()=>({datagrid:[],rebindSidebar:0,rebindUpload:0,dbparams:{PermohonanID:0},forms:{},deletion:{show:!1},paramUji:{show:!1},showReport:!1,tahapan:[],jenis:[{val:"A",txt:"Mutu Air & Lingkungan"},{val:"B",txt:"Bahan Bangunan"},{val:"Ba",txt:"Aspal"},{val:"T",txt:"Tanah (Geoteknik)"}],satker:[{val:"1",txt:"APBN"},{val:"2",txt:"APBD I - BMCK"},{val:"3",txt:"APBD I - NON BMCK"},{val:"4",txt:"APBD II"},{val:"5",txt:"Swasta"},{val:"6",txt:"Perorangan"},{val:"7",txt:"Lainnya"}]}),computed:{reportUrl(){return this.showReport?this.forms.StatusID>=3?"/reports/uji/permohonan-paid/"+this.forms.PermohonanID:"/reports/uji/permohonan/"+this.forms.PermohonanID:"/tunggu-sebentar"}},mounted(){this.PopulateTahapan()},methods:{async ItemClick(s){this.Populate(s.PermohonanID)},async Download(s){this.$api.download(this.$api.url+s,!0)},async Populate(s){this.rebindUpload++,this.dbparams={PermohonanID:s};var a=await this.$api.call("UJI.SelPermohonan",{PermohonanID:s});a.data.length?this.forms=a.data[0]:this.forms={PermohonanID:0}},async PopulateTahapan(){let s=await this.$api.call("UJI.SelTahapan");this.tahapan=s.data},async Save(){var s=await this.$api.call("UJI.SavPermohonan",{...this.forms,XmlPermohonanDet:this.datagrid});s.success&&this.rebindSidebar++},async CatatanKhusus(){await this.$api.call("UJI_UpdCatatanKhusus",{PermohonanID:this.forms.PermohonanID,CatatanKhsusus:"Belum Dibayarkan"}),this.Populate(this.forms.PermohonanID)},async UpdBayar(){(await this.$api.call("UJI_UpdBayar",{PermohonanID:this.forms.PermohonanID,BayarStatus:1})).success&&this.Populate(this.forms.PermohonanID)},async UpdPengambilan(){},async Delete(){var s=await this.$api.call("UJI_DelPermohonan",this.forms);s.success&&(this.deletion.show=!1,this.forms.PermohonanID=0,this.Populate(this.forms.PermohonanID),this.rebindSidebar++)},async ResendNotif(){let s=await this.$api.post("/reports/uji/resend",{PermohonanID:this.forms.PermohonanID});s.success?this.$api.notify(s.message,"success"):this.$api.notify(s.message,"error")},async DownloadKwitansi(){window.open(this.$api.url+"/reports/uji/kwitansi/"+this.forms.PermohonanID,"_blank")},AddParameter(s){this.datagrid.push(...s),this.paramUji.show=!1},DelParameter(s){this.datagrid.splice(s,1)},OpenReport(){setTimeout(()=>{this.showReport=!0},100)},CloseReport(){this.showReport=!1}}};var w=function(){var a=this,e=a._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{attrs:{statusId:",1,2,3,4,5,6,7,8,9,",rebind:a.rebindSidebar,addButton:!1},on:{"item-click":a.ItemClick}}),e("div",{directives:[{name:"show",rawName:"v-show",value:a.forms.PermohonanID||a.forms.PermohonanID===0,expression:"forms.PermohonanID || forms.PermohonanID === 0"}],staticClass:"right-pane",class:a.$api.isMobile()?"":"form-inline",staticStyle:{padding:"20px 20px",width:"",height:"calc(100vh - 66px)",overflow:"auto"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:a.forms.BayarStatus==2,expression:"forms.BayarStatus == 2"}],staticClass:"dvWarn"},[a._v(" "+a._s(a.forms.CatatanKhusus)+" ")]),e("div",{staticStyle:{float:"right","font-family":"raleway",background:"white","margin-top":"-20px"},attrs:{id:"dvRightBox"}},[e("div",{staticStyle:{padding:"10px 20px","text-align":"center"}},[e("div",{staticStyle:{"font-size":"xx-large"}},[a._v(a._s(a.forms.NoPengujian))]),e("div",[a._v(a._s(a.forms.ShareCode))])]),e(o,{staticStyle:{"border-top":"1px solid #e3e3e3","text-align":"center"},attrs:{text:"",small:"",color:"primary"}},[a._v(" "+a._s(a.forms.StatusName)+" ")])],1),e(r,{attrs:{type:"text",label:"Nama Pelanggan",value:a.forms.Nama,disabled:!0,width:"600px"},on:{"update:value":function(t){return a.$set(a.forms,"Nama",t)}}}),e(i,{attrs:{label:"Alamat",value:a.forms.Alamat,width:"600px",disabled:!0},on:{"update:value":function(t){return a.$set(a.forms,"Alamat",t)}}}),a._v(" "),e(r,{attrs:{type:"text",label:"No. Ponsel",value:a.forms.Phone,disabled:!0,width:"600px"},on:{"update:value":function(t){return a.$set(a.forms,"Phone",t)}}}),e(r,{attrs:{type:"text",label:"Email",value:a.forms.Email,disabled:!0,width:"600px"},on:{"update:value":function(t){return a.$set(a.forms,"Email",t)}}}),e(u,{attrs:{items:a.jenis,label:"Nama/Jenis Contoh",value:a.forms.JenisID,disabled:!0,width:"600px"},on:{"update:value":function(t){return a.$set(a.forms,"JenisID",t)}}}),e(m,{staticStyle:{"padding-top":"4px"},attrs:{label:"Tanggal Masuk",value:a.forms.TglMasuk,disabled:!0},on:{"update:value":function(t){return a.$set(a.forms,"TglMasuk",t)}}}),e(i,{attrs:{label:"Kegiatan/Paket Pekerjaan",value:a.forms.NamaKegiatan,disabled:!0,width:"600px"},on:{"update:value":function(t){return a.$set(a.forms,"NamaKegiatan",t)}}}),a._v(" "),e(l,{attrs:{label:"Sumber/SatKer"}},[e(u,{attrs:{items:a.satker,value:a.forms.SumberDana,disabled:!0,width:"300px"},on:{"update:value":function(t){return a.$set(a.forms,"SumberDana",t)}}}),e(r,{directives:[{name:"show",rawName:"v-show",value:a.forms.SumberDana<"5",expression:"forms.SumberDana < '5'"}],staticStyle:{"margin-left":"5px"},attrs:{type:"text",placeholder:"Satker / Kab / Kota",value:a.forms.SatKer,disabled:!0,width:"300px"},on:{"update:value":function(t){return a.$set(a.forms,"SatKer",t)}}})],1),e(l,{attrs:{label:"Surat Permohonan/Tgl."}},[e(r,{attrs:{type:"text",value:a.forms.SuratNo,placeholder:"No. Surat",width:"300px"},on:{"update:value":function(t){return a.$set(a.forms,"SuratNo",t)}}}),e(m,{staticStyle:{"margin-left":"5px"},attrs:{value:a.forms.SuratTgl,width:"300px"},on:{"update:value":function(t){return a.$set(a.forms,"SuratTgl",t)}}})],1),e(l,{attrs:{label:"Surat Permohonan"}},[e(c,{key:a.rebindUpload,attrs:{value:a.forms.SuratUrl},on:{"update:value":function(t){return a.$set(a.forms,"SuratUrl",t)}},scopedSlots:a._u([{key:"default",fn:function({opener:t,fileName:n}){return[e(o,{directives:[{name:"show",rawName:"v-show",value:n,expression:"fileName"}],staticStyle:{"margin-right":"8px"},attrs:{small:"",text:"",outlined:""},on:{click:function(S){return a.Download(a.forms.SuratUrl)}}},[a._v(a._s(n||a.forms.SuratUrl))]),e(o,{attrs:{small:""},on:{click:t}},[e(v,[a._v("mdi-upload")])],1)]}}])})],1),e(_,{attrs:{datagrid:a.datagrid,dbref:"UJI_SelPermohonanDet",dbparams:a.dbparams,disabled:!0,columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Nama Contoh",value:"NamaContoh"},{name:"Jml",value:"JmlContoh"},{name:"Metode",value:"Metode"},{name:"Harga",value:"Harga",class:"align-right"},{name:"",value:"Delete"}]},on:{"update:datagrid":function(t){a.datagrid=t}},scopedSlots:a._u([{key:"row-NamaContoh",fn:function({row:t}){return[e(r,{attrs:{value:t.NamaContoh,disabled:!0,placeholder:"(asal/ukuran contoh)"},on:{"update:value":function(n){return a.$set(t,"NamaContoh",n)}}})]}},{key:"row-JmlContoh",fn:function({row:t}){return[e(r,{attrs:{type:"number",value:t.JmlContoh,disabled:!0,placeholder:"Jml",width:"60px"},on:{"update:value":function(n){return a.$set(t,"JmlContoh",n)}}})]}},{key:"row-Harga",fn:function({row:t}){return[a._v(" "+a._s(a._f("format")(t.Harga*t.JmlContoh))+" ")]}}])})],1),e("ReportPopup",{directives:[{name:"show",rawName:"v-show",value:a.showReport,expression:"showReport"},{name:"click-outside",rawName:"v-click-outside",value:a.CloseReport,expression:"CloseReport"}],attrs:{reportUrl:a.reportUrl}}),e("ParameterUji",{attrs:{forms:a.paramUji,jenisId:a.forms.JenisID}}),e(g,{attrs:{title:"Pembatalan",show:a.deletion.show},on:{"update:show":function(t){return a.$set(a.deletion,"show",t)}}},[e(i,{attrs:{label:"Alasan Pembatalan",value:a.forms.Keterangan,width:"300px"},on:{"update:value":function(t){return a.$set(a.forms,"Keterangan",t)}}})],1)],1)},b=[],x=d(P,w,b,!1,null,"54e48c71");const D=x.exports;export{D as default};
