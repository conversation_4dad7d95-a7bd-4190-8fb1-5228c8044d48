<template>
  <header class="header">
    <div class="header__container">
      <div class="header__toggle" @click="OpenMenu" v-if="!isPageFocused">
        <i class="bx bx-menu" id="header-toggle" style="color: white"></i>
      </div>
      <div class="header__toggle" @click="setPageFocused(false)" v-else>
        <v-icon color="white">mdi-arrow-left</v-icon>
      </div>

      <a href="#" class="header__logo">
        {{ title }}
        <v-icon
          color="warning"
          v-tooltip="'Whatsapp is not working'"
          v-show="showAlert"
          >mdi-message-alert</v-icon
        >
      </a>
      <img src="/imgs/logo.png" alt="" class="header__img" />
    </div>
  </header>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
export default {
  data: () => ({
    title: 'SILAKON',
    showAlert: false,
  }),
  watch: {
    $route(to) {
      this.title = to.meta.title
    },
  },
  computed: {
    ...mapGetters(['isPageFocused']),
  },
  mounted() {
    this.CheckWhatsapp()
    setInterval(() => {
      this.CheckWhatsapp()
    }, 1000 * 60 * 5)
  },
  methods: {
    ...mapActions(['setPageFocused']),
    OpenMenu() {
      let $n = document.getElementById('navbar')
      $n.style.left = '0'
    },
    async CheckWhatsapp() {
      let res = await this.$api.get('/Main/App/Extender/Whatsapp')
      if (res.success) {
        if (res.qrcode == 'No QRCODE, WA is connected') {
          this.showAlert = false
        } else {
          this.showAlert = true
        }
      }
    },
  },
}
</script>
