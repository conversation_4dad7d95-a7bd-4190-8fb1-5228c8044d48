<template>
  <div style="display: flex">
    <SidePane @item-click="ItemClick" statusId=",5," :rebind="rebindSidebar" />
    <div v-show="forms.Nama" style="overflow: auto; height: calc(100vh - 66px)">
      <div
        style="
          background: #039ae4;
          color: white;
          padding: 15px 15px 10px 15px;
          display: flex;
        "
      >
        <div style="display: flex; width: calc(100% - 200px)">
          <div
            style="
              max-width: 450px;
              overflow: hidden;
              text-wrap: nowrap;
              text-overflow: ellipsis;
            "
          >
            {{ forms.Nama }}
          </div>
          <div
            style="
              background: white;
              color: #039ae4;
              margin-left: 10px;
              padding: 5px 8px;
              border-radius: 5px;
              font-size: small;
              position: relative;
              top: -3px;
            "
          >
            {{ forms.NoPengujian }}
          </div>
        </div>

        <v-spacer />
        <v-btn
          v-if="forms.StatusID == 6"
          small
          disabled
          style="color: white !important; position: relative; top: -2px"
        >
          SELESAI
        </v-btn>
      </div>
      <Grid
        class="table-cekprogress"
        :datagrid.sync="datagrid"
        dbref="UJI_SelCekProgress"
        :doRebind="rebind"
        :dbparams="dbparams"
        :disabled="true"
        :columns="[
          {
            name: 'Parameter Uji',
            value: 'NamaParameter',
          },
          {
            name: 'Jml',
            value: 'JmlContoh',
            width: '50px',
          },
          {
            name: 'Keterangan',
            value: 'Keterangan',
          },
          {
            name: 'Waktu',
            value: 'Waktu',
          },
        ]"
      >
        <template v-slot:row-NamaParameter="{ row }">
          <div
            :class="{
              'is-masalah': row.Ordr == 2,
              'is-done': row.HasilStatus,
            }"
          >
            <div>{{ row.NamaParameter }}</div>
            <div>{{ row.Ordr == 1 ? row.Metode : '' }}</div>
          </div>
        </template>
        <template v-slot:row-Keterangan="{ row }">
          {{ row.Keterangan }}
        </template>
        <template v-slot:row-Waktu="{ row }"> {{ row.Waktu }} Hari </template>
      </Grid>
      <div
        v-for="(lk, idx) in lembarKerja"
        :key="idx"
        style="
          font-size: 12px;
          background: #f3f3f3;
          display: flex;
          margin-bottom: 1px;
          padding: 8px;
        "
      >
        <v-btn
          x-small
          text
          color="error"
          v-tooltip="'Hapus Lembar Kerja'"
          style="margin-top: 4px"
          v-if="!lk.ApprovedBy"
          @click="DeleteLK(lk.LembarKerjaID)"
        >
          <v-icon> mdi-close </v-icon>
        </v-btn>
        <v-btn
          small
          text
          color="primary"
          @click="Open(lk.LkUrl, lk.LembarKerjaID)"
        >
          {{ lk.Nama }}
        </v-btn>
        <!-- <MenuButton
          :menu="['Tampilkan', 'Perbaiki']"
          @item-click="MenuPDF($event, lk)"
        >
          <template v-slot="{ on }">
            <v-btn x-small text outlined color="primary" style="margin: 4px" v-on="on" :disabled="loading">
              PDF
            </v-btn>
          </template>
        </MenuButton> -->
        <v-spacer />
        <v-btn
          x-small
          text
          outlined
          color="error"
          style="margin: 4px"
          v-if="lk.Alasan"
          v-tooltip="lk.Alasan"
        >
          DITOLAK
        </v-btn>
        <Uploader
          v-if="lk.Alasan"
          accept=".pdf"
          @change="fileUploaded($event, lk.LembarKerjaID)"
        >
          <template v-slot="{ opener }">
            <v-btn
              x-small
              text
              outlined
              color="primary"
              style="margin: 4px"
              @click="opener"
            >
              REVISI
            </v-btn>
          </template>
        </Uploader>
        <v-btn
          x-small
          text
          outlined
          style="margin: 4px"
          v-else-if="lk.ApprovedBy"
        >
          SUDAH DISETUJUI
        </v-btn>
      </div>
      <div style="display: flex">
        <Uploader
          @change="fileUploaded"
          :multiple="true"
          style="margin-top: 8px"
          accept=".pdf"
        >
          <template v-slot="{ opener }">
            <v-btn color="primary" @click="opener"> UPLOAD LEMBAR KERJA </v-btn>
          </template>
        </Uploader>
        <v-btn
          color="success"
          style="margin: 8px 0 0 8px"
          v-if="showSubmitButton"
          @click="SubmitLK"
        >
          SUBMIT
        </v-btn>
      </div>
    </div>
  </div>
</template>
<script>
import SidePane from '../../Loket/SidePane.vue'
export default {
  components: {
    SidePane,
  },
  data: () => ({
    datagrid: [],
    dbparams: { PermohonanID: 0 },
    lembarKerja: [],
    loading: false,
    forms: {
      PermohonanID: 0,
      ParameterID: 0,
      Masalah: '',
    },
    rebind: 1,
    rebindSidebar: 1,
    masalah: {
      show: false,
    },
  }),
  computed: {
    showSubmitButton() {
      // let unfinished = this.datagrid.filter(d => !d.HasilStatus)
      let newlk = this.lembarKerja.filter((d) => !d.Alasan && !d.ApprovedBy)
      return newlk.length > 0 && this.forms.LkStatus != 'submitted'
    },
  },
  methods: {
    SideFilter(item) {
      return !item.LkStatus
    },
    async ItemClick(val) {
      this.Populate(val.PermohonanID)
    },
    OpenPDF(url) {
      window.open(this.$api.url + url.replace(/\.\w{3,4}$/, 'pdf'), '_blank')
    },
    async Populate(id) {
      this.dbparams = { PermohonanID: id }
      var ret = await this.$api.call('UJI.SelPermohonan', {
        PermohonanID: id,
      })
      if (ret.data.length) {
        this.forms = ret.data[0]
      } else {
        this.forms = {}
      }
      // if (this.forms.StatusID >= 5) {
      this.PopulateLK(id)
      // }
    },
    async PopulateLK(id) {
      this.lembarKerja = []
      var ret = await this.$api.call('UJI.SelLembarKerja', {
        PermohonanID: id,
      })
      this.lembarKerja = ret.data
    },
    async DeleteLK(id) {
      if (!confirm('Anda yakin menghapus Lembar Kerja ini?')) return

      var ret = await this.$api.call('UJI.DelLembarKerja', {
        LembarKerjaID: id,
      })
      if (ret.success) this.PopulateLK(this.forms.PermohonanID)
    },
    async MenuPDF(cmd, lk) {
      if (cmd == 'Tampilkan') {
        window.open(
          this.$api.url + lk.LkUrl.replace(/\.\w{3,4}$/, '.pdf'),
          '_blank'
        )
      } else if (cmd == 'Perbaiki') {
        this.$api.notify('Silahkan tunggu ..')
        this.loading = true
        // window.open(this.$api.url + '/reports/uji/convert-lk/' + lk.LembarKerjaID, '_blank')
        await this.$api.get('/reports/uji/refine-lk/' + lk.LembarKerjaID)
        window.open(
          this.$api.url + lk.LkUrl.replace(/\.\w{3,4}$/, '.pdf'),
          '_blank'
        )
        this.loading = false
      }
    },
    Open(url, id) {
      // window.open(this.$api.url + url, '_blank')
      window.open(this.$api.url + '/reports/uji/lembarkerja/' + id, '_blank')
    },
    async SubmitLK() {
      // if(!confirm('Anda yakin semua LK sudah diupload?')) return

      await this.$api.call('UJI_UpdLkStatus', {
        PermohonanID: this.forms.PermohonanID,
        Status: 'submitted',
      })
      this.forms.LkStatus = 'submitted'
      this.rebindSidebar++
    },
    async fileUploaded(file, oldId) {
      this.$api.notify('Upload Sukses')

      let rex = await this.$api.call('UJI_SavLembarKerja', {
        PermohonanID: this.forms.PermohonanID,
        XmlRawUrl: file.infos,
        RevisedID: typeof oldId == 'object' ? null : oldId,
      })
      if (rex.success) {
        this.forms.LkStatus = null
        await this.PopulateLK(this.forms.PermohonanID)
        for (let lk of this.lembarKerja) {
          this.$api.get('/reports/uji/convert-lk/' + lk.LembarKerjaID)
        }
      }
    },
  },
}
</script>
<style lang="scss">
.table-cekprogress {
  table {
    min-width: 750px;
    width: 100%;
    .is-masalah {
      padding-left: 10px;
      position: absolute;
      margin-top: -9px;
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
      &.is-done {
        color: gray;
      }
    }
  }
}
</style>
