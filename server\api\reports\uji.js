const carbone = require("carbone");
var db = require("../../common/db");
var moment = require('moment')
const fs = require("fs");
const path = require("path");
var express = require('express')
var router = express.Router()
var Excel = require('exceljs')
var {degrees, PDFDocument, rgb, StandardFonts, PDFPageLeaf, PDFDict, PDFName} = require('pdf-lib');
const QRCode = require('qrcode')
const axios = require("axios");
const FormData = require('form-data');
const freeConvert = require('../thirdparty/free-convert')

router.get('/', async function(req, res) {
    
  res.send({
    success: true,
    data: `/get/kwitansi.pdf`,
    message: 'Report Generated',
    type: 'url',
  })
    
})

// eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************.NKhw8gtjwvY_qWVvTwR_Ijj5vckWBi4kUDpxB4gXHPg
  
router.get('/permohonan/:id', async function(req, res) {
  res.header("Cache-Control", "private, no-cache, no-store, must-revalidate");
  res.header("Expires", "-1");
  res.header("Pragma", "no-cache");
  // Data to inject
  var {id} = req.params;
  if(!id) {
    res.send({
      success: false,
      message: 'id tidak terdaftar'
    })
  }
  var d = await db.exec("UJI_SelPermohonan", {_PermohonanID: id});
  var data = d[0];
  if(!d.length) {
    res.send({
      success: false,
      message: 'permohonan tidak ditemukan'
    })
  }
  d = await db.exec("UJI_SelPermohonanDet", {_PermohonanID: id});
  data.detail = d;

  // console.log("Generating..");
  res.header("Content-Type", "application/pdf");
  carbone.render(
    "./tmp/templates/Pendaftaran.docx",
    data,
    {convertTo: "pdf"},
    function(err, result) {
      if (err) return console.log(err);
      // console.log("Converting..");
      // let addf = moment().format('DDHHmmssSSS') 
      // fs.writeFileSync(__dirname + `/../../tmp/pendaftaran_${addf}.pdf`, result);
      // res.sendFile(path.resolve(__dirname + `/../../tmp/pendaftaran_${addf}.pdf`));
      res.send(result)
      //process.exit();
    }
  );
});

router.get('/permohonanonline/:id', async function(req, res) {
  res.header("Cache-Control", "private, no-cache, no-store, must-revalidate");
  res.header("Expires", "-1");
  res.header("Pragma", "no-cache");
  res.header("Content-Type", "application/pdf");
  // Data to inject
  var {id} = req.params;
  var d = await db.exec("UJI_SelPermohonan", {_PermohonanID: id});
  var data = d[0];
  d = await db.exec("UJI_SelPermohonanDet", {_PermohonanID: id});
  data.detail = d;
  d = await db.exec("UJI_SelSyaratByPermohonan", {_PermohonanID: id});
  data.syarat = d;

  //console.log(data);

  // console.log("Generating..");
  carbone.render(
    "./tmp/templates/PendaftaranOnline.docx",
    data,
    {convertTo: "pdf"},
    function(err, result) {
      if (err) return console.log(err);
      // console.log("Converting..");
      //let addf = moment().format('DDHHmmssSSS') 
      //fs.writeFileSync(__dirname + `/../../tmp/pendaftaran-online_${addf}.pdf`, result);
      //res.sendFile(__dirname + `/../../tmp/pendaftaran-online_${addf}.pdf`);
      res.send(result)
      //process.exit();
    }
  );
});

router.get('/permohonan-paid/:id', async function(req, res) {
  res.header("Cache-Control", "private, no-cache, no-store, must-revalidate");
  res.header("Expires", "-1");
  res.header("Pragma", "no-cache");
  res.header("Content-Type", "application/pdf");
  // Data to inject
  var {id} = req.params;
  var d = await db.exec("UJI_SelPermohonan", {_PermohonanID: id});
  var data = d[0];
  if(!data) {
    res.send({success: false, data:[{ErrCode:404, ErrMsg:'data tidak ditemukan'}]});
    return;
  }
  d = await db.exec("UJI_SelPermohonanPaidDet", {_PermohonanID: id});
  data.detail = d;
  d = await db.exec("UJI_SelSyaratByPermohonan", {_PermohonanID: id});
  data.syarat = d;

  //console.log("Generating..");
  carbone.render(
    "./tmp/templates/PendaftaranFinal.docx",
    data,
    {convertTo: "pdf"},
    function(err, result) {
      if (err) return console.log(err);
      //console.log("Converting..");
      //let addf = moment().format('DDHHmmssSSS') 
      //fs.writeFileSync(__dirname + `/../../tmp/pendaftaran-online_${addf}.pdf`, result);
      //res.sendFile(__dirname + "/../../tmp/pendaftaran-online.pdf");
      //res.redirect(302, `/reports/get/pendaftaran-online_${addf}.pdf`);
      res.send(result)
      //process.exit();
    }
  );
});

router.get('/spu/:id', async function(req, res) {
  res.header("Cache-Control", "private, no-cache, no-store, must-revalidate");
  res.header("Expires", "-1");
  res.header("Pragma", "no-cache");
  res.header("Content-Type", "application/pdf");
  // Data to inject
  var {id} = req.params;
  var d = await db.exec("UJI_SelPermohonan", {_PermohonanID: id});
  var data = d[0];

  d = await db.exec("UJI_SelPermohonanDet", {_PermohonanID: id});
  data.detail = d;
  d = await db.exec("UJI_SelSpuPenguji", {_PermohonanID: id});
  data.penguji = d;

  //console.log(data);

  // console.log("Generating..");
  carbone.render(
    "./tmp/templates/SPU.docx",
    data,
    {convertTo: "pdf"},
    async (err, result) => {
      if (err) return console.log(err);
      // console.log("Converting.." + __dirname + "/../../tmp/perintahuji.pdf");
      // let addf = moment().format('DDHHmmssSSS') 
      // fs.writeFileSync(__dirname + `/../../tmp/perintahuji_${addf}.pdf`, result);
      // res.redirect(302, `/reports/get/perintahuji_${addf}.pdf`);
      // res.sendFile(__dirname + "/../../tmp/perintahuji.pdf");
      res.send(result)
      //process.exit();
    }
  );
});

router.get('/pengantar/:id', async function(req, res) {
  res.header("Cache-Control", "private, no-cache, no-store, must-revalidate");
  res.header("Expires", "-1");
  res.header("Pragma", "no-cache");
  res.header("Content-Type", "application/docx");
  res.header("Content-Disposition", "attachment;filename=Pengantar.docx")
  // Data to inject
  var {id} = req.params;
  var d = await db.exec("UJI_SelPermohonan", {_PermohonanID: id});
  var data = d[0];

  data.TglSekarang = moment(data.TglSekarang).format('DD MMMM YYYY')
  data.TglMasuk = moment(data.TglMasuk).format('DD MMMM YYYY')
  data.SignedDate = moment(data.SignedDate).format('DD MMMM YYYY')

  // console.log("Generating..");
  carbone.render(
    "./tmp/templates/Pengantar.docx",
    data,
    {},
    async (err, result) => {
      if (err) return console.log(err);
      
      res.send(result)
    }
  );
});

router.get('/kwitansi/:id', async function(req, res) {
  res.header("Cache-Control", "private, no-cache, no-store, must-revalidate");
  res.header("Expires", "-1");
  res.header("Pragma", "no-cache");
  res.header("Content-Type", "application/pdf");
  // Data to inject
  var {id} = req.params;
  var {keterangan} = req.query;
  var d = await db.exec("UJI_RptKwitansi", {_PermohonanID: id});
  var data = d[0];
  if (keterangan) {
    data.TujuanPembayaran = keterangan
  }

  let qrimg = new Promise((resolve, reject) => {
    QRCode.toFile(`./tmp/qrcode_kw${id}.png`, 
      `https://silakon.dpubinmarcipka.jatengprov.go.id/reports/uji/kwitansi/${id}`, 
      (err) => {
        if(err) reject(err)
        resolve()
      })  
  })

  // console.log("Generating..");
  carbone.render(
    "./tmp/templates/Kwitansi.docx",
    data,
    {convertTo: "pdf"},
    async (err, result) => {
      if (err) return console.log(err);

      const pdfDoc = await PDFDocument.load(result)
      const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica)

      // Get the first page of the document
      const pages = pdfDoc.getPages()
      
      let i = 1
      for(let firstPage of pages){
        // Draw a string of text diagonally across the first page
        firstPage.drawText('Scan barcode disamping ', {
          x: 90,
          y: 33,
          size: 10,
          font: helveticaFont,
        })
        firstPage.drawText('Scan barcode disamping ', {
          x: 540,
          y: 33,
          size: 10,
          font: helveticaFont,
        })

        firstPage.drawText('untuk mengetahui keaslian kwitansi ini', {
          x: 90,
          y: 22,
          size: 10,
          font: helveticaFont,
        })
        firstPage.drawText('untuk mengetahui keaslian kwitansi ini', {
          x: 540,
          y: 22,
          size: 10,
          font: helveticaFont,
        })

        // firstPage.drawText(`Hal. ${i} dari ${pages.length}`, {
        //   x: 515,
        //   y: 28,
        //   size: 10,
        //   color: rgb(0.3, 0.3, 0.3),
        //   font: helveticaFont,
        // })

        //qrcode
        await qrimg
        const pngImage = await pdfDoc.embedPng(fs.readFileSync(`./tmp/qrcode_kw${id}.png`))
        firstPage.drawImage(pngImage, {
          x: 30,
          y: 15,
          width: 50,
          height: 50,
        })
        
        firstPage.drawImage(pngImage, {
          x: 480,
          y: 15,
          width: 50,
          height: 50,
        })

        // Draw the SVG path as a black line
        // const svgPath = 'M 0,0 h 270'
        // firstPage.moveTo(90, 60)
        // firstPage.drawSvgPath(svgPath)
        // firstPage.moveTo(470, 60)
        // firstPage.drawSvgPath(svgPath)


        i++
      }

      result = await pdfDoc.save()

      let fn = `tmp/kwitansi_${id}.pdf`
      fs.writeFileSync(fn, result)
      res.sendFile(path.resolve(__dirname + `/../../${fn}`))
      // res.send(result)
    }
  );
});

function hex2a(hex) {
  var str = '';
  for (var i = 0; i < hex.length; i += 2) {
    var v = parseInt(hex.substr(i, 2), 16);
    if (v) str += String.fromCharCode(v);
  }
  return str;
} 

const parseOutlines = (pdfDoc) => {
  let outlines = {}
  let oRef = pdfDoc.catalog.get(PDFName.of('Outlines'))
  
  let outline = pdfDoc.context.enumerateIndirectObjects()[oRef.objectNumber-1][1]
  let oCount = outline.dict.get(PDFName.of('Count')).numberValue

  for(let i = oRef.objectNumber; i<oRef.objectNumber+oCount; i++) {
    let o = pdfDoc.context.enumerateIndirectObjects()[i][1]
    let oTitle = hex2a(o.dict.get(PDFName.of('Title')).value).substring(2)
    outlines[o.dict.get(PDFName.of('Dest')).array[0].objectNumber] = oTitle
  }
  
  return outlines
}

const exportPDFAdobe = async (file, output) => {
  const PDFServicesSdk = require('@adobe/pdfservices-node-sdk');
  console.log(file, output)
  // Initial setup, create credentials instance.
  const credentials =  PDFServicesSdk.Credentials
    .servicePrincipalCredentialsBuilder()
    .withClientId('dc1ff01544e04e88a41e4f20933189a2')
    .withClientSecret('p8e-fkD1O9d-eMhACP9MfTuA9Y9UZ362W0rv')
    .build();

  // Create an ExecutionContext using credentials and create a new operation instance.
  const executionContext = PDFServicesSdk.ExecutionContext.create(credentials),
    createPdfOperation = PDFServicesSdk.CreatePDF.Operation.createNew();

  // Set operation input from a source file.
  const input = PDFServicesSdk.FileRef.createFromLocalFile(file);
  createPdfOperation.setInput(input);

  //Generating a file name
  let outputFilePath = output;

  // Execute the operation and Save the result to the specified location.
  let result = await createPdfOperation.execute(executionContext)
    // .then(result => result.saveAsFile(outputFilePath))
    .catch(err => {
      if(err instanceof PDFServicesSdk.Error.ServiceApiError
          || err instanceof PDFServicesSdk.Error.ServiceUsageError) {
        console.log('Exception encountered while executing operation', err);
      } else {
        console.log('Exception encountered while executing operation', err);
      }
    });

  if (result)
    await result.saveAsFile(outputFilePath)
}

router.get('/convert-lk/:id', async function(req, res) {
  try {
    var {id} = req.params;
    let d = await db.exec("UJI_SelLembarKerja", {_LembarKerjaID: id, _IsHistory: 1});

    let lkUrl = d[0].LkUrl
    if (!lkUrl.match(/.xls\w$/)) {
      res.send({success: false, error: 'File bukan PDF'})
      return
    }
    //if (fs.existsSync(d[0].LkUrl.replace(/^\//,'').replace(/.xls\w$/,'.pdf'))) {
    //  res.send({success: true})
    //  return
    //}
    let filename = lkUrl.replace(/\/uploads\/\d{4}\//, '').replace(/.xls\w$/,'')

    let workbook = new Excel.Workbook();
    await workbook.xlsx.readFile(`.${lkUrl}`)
    let pageStatus = {}
  
    for(let s of workbook.worksheets) {
      if (s.name.match(/^#/)) pageStatus[s.name.trim()] = true
      else pageStatus[s.name.trim()] = s.state == 'hidden'
    }
    // await  exportPDFAdobe(`.${lkUrl}`, path.resolve(__dirname + `/../../tmp/${filename}-adb.pdf`))
    exportPDF(`.${lkUrl}`, './tmp')
    // await apdf

    const pdfOutline = await PDFDocument.load(fs.readFileSync(`./tmp/${filename}.pdf`))
    let outs = parseOutlines(pdfOutline)
    // console.log(pageStatus)
    // console.log(outs)

    // const pdfDoc = await PDFDocument.load(fs.readFileSync(`./tmp/${filename}-adb.pdf`))

    let pages = pdfOutline.getPages()
    let isHidden = false
    let toRemove = []
    let i = 0
    for(let p of pages) {
      if (outs[p.ref.objectNumber]) isHidden = pageStatus[outs[p.ref.objectNumber]]
      // console.log(i, outs[p.ref.objectNumber], isHidden)
      if (isHidden) toRemove.unshift(i)
      i++
    }
    for(let pi of toRemove) {
      // pdfDoc.removePage(pi)
      pdfOutline.removePage(pi)
    }

    // let result = await pdfDoc.save()
    let result = await pdfOutline.save()
    let fn = `tmp/${filename}-fix.pdf` // `tmp/${id}-${d[0].Nama.replace(/(.pdf|.xlsx|.docx)/, '')}.pdf`
    fs.writeFileSync(d[0].LkUrl.replace(/^\//,'').replace(/.xls\w/,'.pdf'), result)
    res.send({
      success: true
    })
  }
  catch (ex) {
    console.error(ex)
    res.send({
      success: false,
      message: 'Error while converting to pdf'
    })
  }
})

router.get('/refine-lk/:id', async function(req, res) {
  try {
    var {id} = req.params;
    let d = await db.exec("UJI_SelLembarKerja", {_LembarKerjaID: id});

    let lkUrl = d[0].LkUrl
    let pdfPath = d[0].LkUrl.replace(/^\//,'').replace(/.xls\w/,'.pdf')

    await freeConvert.exportFile('https://silakon.dpubinmarcipka.jatengprov.go.id' + lkUrl, pdfPath)
    
    res.send({
      success: true
    })
  } catch (ex) {
    console.error(ex)
    res.send({
      success: false,
      message: 'Error while converting to pdf'
    })
  }
})

router.get('/lembarkerja/:xid', async function(req, res) {  
  var {xid} = req.params;
  let id = xid.split('-')[0]
  let d = await db.exec("UJI_SelLembarKerja", {_LembarKerjaID: id, _IsHistory: 1});

  let signedFn = ''
  if (!d.length) {
    res.status(404).send({error: 'data not found'})
    return
  } else if (!d[0].Approved2By) {
    res.sendFile(path.resolve(__dirname + `/../../${d[0].LkUrl}`))
    return
  } else if (d[0].SignedUrl && d[0].SignedUrl !== 'reports/uji/lembarkerja/'+xid) {
    res.status(404).send({error: 'file not found'})
    return
  } else {
    signedFn = d[0].LkUrl.replace(/\.\w{3,4}$/, '-signed.pdf')
    if(fs.existsSync(signedFn.replace('/',''))) {
      res.sendFile(path.resolve(__dirname + `/../../${signedFn}`))
      return
    }
  }

  let qrimg = new Promise((resolve, reject) => {
    QRCode.toFile(`./tmp/qrcode_lk${id}.png`, 
      `https://silakon.dpubinmarcipka.jatengprov.go.id/${d[0].SignedUrl}`, 
      (err) => {
        if(err) reject(err)
        resolve()
      })  
  })
  
  const pdfDoc = await PDFDocument.load(fs.readFileSync(`./${d[0].LkUrl.replace(/^\//,'').replace(/\.\w{3,4}$/,'.pdf')}`))

  // Get the first page of the document
  const pages = pdfDoc.getPages()
      
  let i = 1
  for(let firstPage of pages){
    // Draw footer
    await drawFooter(pdfDoc, firstPage, i, pages, false)

    //qrcode
    await qrimg
    const pngImage = await pdfDoc.embedPng(fs.readFileSync(`./tmp/qrcode_lk${id}.png`))
    firstPage.drawImage(pngImage, {
      x: 45,
      y: 15,
      width: 50,
      height: 50,
    })
    
    i++
  }

  let result = await pdfDoc.save()
  // let fn = `tmp/${id}-${d[0].Nama.replace(/\.\w{3,4}$/, '')}.pdf`
  // let fn = d[0].LkUrl.replace(/\.\w{3,4}$/, '-signed.pdf')
  fs.writeFileSync(signedFn.replace('/',''), result)
  res.sendFile(path.resolve(__dirname + `/../../${signedFn}`))
  // res.send(result);
      
})

const drawFooter = async (pdfDoc, firstPage, i, pages, withLine = true) => {
  const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica)
  // Draw a string of text diagonally across the first page
  let psize = firstPage.getSize()
  firstPage.drawText('Sertifikat ini ditandatangani secara elektronik dengan menggunakan Sertifikat Elektronik', {
    x: 100,
    y: 45,
    size: 10,
    font: helveticaFont,
  })
  firstPage.drawText('yang diterbitkan oleh Balai Sertifikasi Elektronik (BSrE)', {
    x: 100,
    y: 33,
    size: 10,
    font: helveticaFont,
  })
  firstPage.drawText('Silakan scan barcode disamping untuk mengecek keaslian', {
    x: 100,
    y: 20,
    size: 10,
    color: rgb(0.8, 0, 0),
    font: helveticaFont,
  })    
  firstPage.drawText(`Hal. ${i} dari ${pages.length}`, {
    x: psize.width - (psize.width > 800 ? 100 : 80), // 515,
    y: 20,
    size: 10,
    color: rgb(0.3, 0.3, 0.3),
    font: helveticaFont,
  })
  
  if (withLine) {
    const svgPath = 'M 0,0 h 470'
    firstPage.moveTo(100, 60)

    // Draw the SVG path as a black line
    firstPage.drawSvgPath(svgPath)
  }

}

router.get('/sertifikat/:id', async function(req, res) {
  /*res.header("Cache-Control", "private, no-cache, no-store, must-revalidate");
  res.header("Expires", "-1");
  res.header("Pragma", "no-cache");
  res.header("Content-Type", "application/pdf");*/

  var {id} = req.params;
  id = id.split('-')
  let d = await db.exec("UJI_SelRecoverSertivikat", {SignedUrl: `/reports/uji/sertifikat/${id[0]}-${id[1]}`});
  // console.log(d)
  if (fs.existsSync(`sertifikat/${id[0]}/${id[1]}.pdf`)) {
    // let file = fs.readFileSync(`sertifikat/${id[0]}/${id[1]}.pdf`)
    // res.send(file)
    // res.send(`<iframe src="/sertifikat/${id[0]}/${id[1]}.pdf" style="height: calc(100vh - 60px);width: calc(100vw - 20px);"></iframe>
    //   <div style="width: calc(100vw - 20px);">${d[0].DitandatanganiOleh}</div>`);
    if (d.length) {
      res.send(`<iframe id="pdfviewer" frameborder="0" src="https://docs.google.com/gview?embedded=true&url=https://silakon.dpubinmarcipka.jatengprov.go.id/sertifikat/${id[0]}/${id[1]}.pdf" style="height: calc(100vh - 20px);width: calc(100vw - 20px);"></iframe>
      <div style="position: absolute;
        bottom: 0;
        padding: 20px;
        background: #333;
        color: white;">${d[0].DitandatanganiOleh}<br /><br />
        <a href="https://silakon.dpubinmarcipka.jatengprov.go.id/sertifikat/${id[0]}/${id[1]}.pdf" target="_blank" style="color:silver;">Download</a></div>`);
    } else {
      res.status(404).send('FILE NOT FOUND')
    }
  } else {
    if (d.length) {
      let addf = id[1] 
      let year = id[0]
      let qrimg = new Promise((resolve, reject) => {
        QRCode.toFile(`./tmp/qrcode_${addf}.png`, 
          `https://silakon.dpubinmarcipka.jatengprov.go.id/reports/uji/sertifikat/${year}-${addf}`, 
          (err) => {
            if(err) reject(err)
            resolve()
          })  
      })
    
      carbone_render(
        d[0].RawUrl.replace(/^\//,''),
        {
          SerialNumber: d[0].SerialNumber,
          TanggalTTD: moment(d[0].SignedDate).format('DD MMMM YYYY')
        },
        {convertTo: "pdf"},
        async (err, result) => {
          if (err) return console.log(err);
          
          const pdfDoc = await PDFDocument.load(result)
    
          // Get the first page of the document
          const pages = pdfDoc.getPages()
          
          let i = 1
          for(let firstPage of pages){
            
            await drawFooter(pdfDoc, firstPage, i, pages)

            //qrcode
            await qrimg
            const pngImage = await pdfDoc.embedPng(fs.readFileSync(`./tmp/qrcode_${addf}.png`))
            firstPage.drawImage(pngImage, {
              x: 45,
              y: 15,
              width: 50,
              height: 50,
            })

            i++
          }
    
          result = await pdfDoc.save()
          fs.writeFileSync(`sertifikat/${year}/${addf}.pdf`, result)
          res.sendFile(path.resolve(__dirname + `/../../sertifikat/${year}/${addf}.pdf`))
          // res.send(result);
        }
      );
    } else {
      res.status(404).send('FILE NOT FOUND')
    }
  }
});

router.get('/pdf', async function(req, res) {
  res.header("Cache-Control", "private, no-cache, no-store, must-revalidate");
  res.header("Expires", "-1");
  res.header("Pragma", "no-cache");
  res.header("Content-Type", "application/pdf");
  var {FilePath, Name} = req.query;

  // console.log("Generating..");
  carbone.render(
    "."+FilePath,
    {},
    {convertTo: "pdf"},
    async (err, result) => {
      if (err) return console.log(err);
      
      res.send(result)
      //process.exit();
    }
  );
});

router.get('/serialnumber', async function(req, res) {
  var {FilePath} = req.query;

  // read from a file
  const workbook = new Excel.Workbook();
  await workbook.xlsx.readFile(__dirname +'/../..'+ FilePath);

  let t  = []
  let row = 9
  do {
    row++
    t = workbook.worksheets[0].getRow(row).values
    t = t[t.length-1].match(/\d{3}\/BP2-\d{4}\/\d{3}/)
  } while(!t)

  res.send({
    success: true,
    data: t[0],
  })
});


const shell = require("shelljs");
const exportPDF = (file, outdir = '.', tipe = 'pdf:writer_pdf_Export') => {
  let libre = "/opt/libreoffice7.6/program/soffice" 
  if(!fs.existsSync(libre)) {
    libre = `"C:\\Program Files\\LibreOffice\\program\\soffice.exe"`
  }
  const out = shell.exec(
    `${libre} --nolockcheck --headless --convert-to ${tipe} "${file}" --outdir ${outdir}`,
    {
      silent: true
    }
  )
  if (out.code !== 0) {
    console.error(`ERR: ${out.stderr}`)
  }
}

const JSZip = require("jszip");
const {sendMail} = require("../../common/email");

const carbone_render = async (fileInput, data, opts, callback) => {
  try {
    let ext = fileInput.split('.').pop()

    const zip = new JSZip();
    let filedata = fs.readFileSync(fileInput)
    zip.loadAsync(filedata).then(async (z) => {
      for(let [filename, file] of Object.entries(zip.files)) {
      // TODO Your code goes here
        if (filename.match(/.xml$/)) {
          let str = await z.file(filename).async("string")
          for(let key in data) {
            str = str.replace(new RegExp(`{(<[^<]+>){0,}d.${key}(<[^<]+>){0,}}`, "g"), data[key])
          }
          z.file(filename, str)
        }
      }

      let outname = moment().format('DDHHmmssSSS') 
      let blob = await z.generateAsync({type:"nodebuffer"})
      if (opts.convertTo == "pdf") {
        fs.writeFileSync(`./tmp/${outname}.${ext}`, blob, "binary")
        exportPDF(`./tmp/${outname}.${ext}`, "./tmp")
        callback(null, fs.readFileSync(`./tmp/${outname}.pdf`))
      } else {
        callback(null, blob)
      }
    })
  } catch (err) {
    callback(err)
  }
}
const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
}

router.get('/get-sp/:id', async function(req, res) {
  let d = await db.exec("UJI_SelSuratPengantar", {PermohonanID: req.params.id});


  
  res.send({
    success: true,
    data: ''
  })
});

const renderSuratPengantar = async (opt) => {
  let qrimg = new Promise((resolve, reject) => {
    QRCode.toFile(`./tmp/qrcode-sp_${opt.addf}.png`,
      `https://silakon.dpubinmarcipka.jatengprov.go.id/reports/uji/sertifikat/${opt.year}-${opt.addf}sp`,
      (err) => {
        if (err) reject(err)
        resolve()
      })
  })

  let d = await db.exec("UJI_SelSuratPengantar", {PermohonanID: opt.PermohonanID});
  console.log(d)

  let fp = "tmp/templates/SuratPengantar.docx"
  carbone_render(
    fp,
    {
      ...d[0], 
      SerialNumber: opt.SerialNumber,
      NoSurat: opt.SerialNumber
    },
    {convertTo: "pdf"},
    async (err, result) => {
      if (err) return console.log(err);

      const pdfDoc = await PDFDocument.load(result)
      // const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica)

      // Get the first page of the document
      const pages = pdfDoc.getPages()

      let i = 1
      for (let firstPage of pages) {
        // Draw footer
        await drawFooter(pdfDoc, firstPage, i, pages)

        //qrcode
        await qrimg
        const pngImage = await pdfDoc.embedPng(fs.readFileSync(`./tmp/qrcode-sp_${opt.addf}.png`))
        firstPage.drawImage(pngImage, {
          x: 45,
          y: 15,
          width: 50,
          height: 50,
        })

        i++
      }

      result = await pdfDoc.save()
      fs.writeFileSync(`tmp/surat_pengantar${opt.addf}.pdf`, result)
      
      // SIGN
      if (opt.NIK && opt.Passphrase) {
        const formData = new FormData();
        formData.append('file', fs.createReadStream(`tmp/surat_pengantar${opt.addf}.pdf`));
        formData.append('nik', opt.NIK);
        formData.append('passphrase', opt.Passphrase);
        formData.append('tampilan', 'invisible');
        formData.append('jenis_response', 'BASE64');
        let is_err = null
        let signed = await axios({
          method: "post",
          url: "http://10.1.105.43/api/sign/pdf",
          data: formData,
          headers: {
            ...formData.getHeaders(),
            'Authorization': 'Basic c2lsYWtvbjojIyMqUyFsQGswbiojIyM=',
          },
        })
          .catch(function (response) {
          //handle error
            is_err = response
          });

      
        fs.writeFileSync(`sertifikat/${opt.year}/${opt.addf}sp.pdf`,
          signed.data.base64_signed_file, {encoding: 'base64'});      
      } else {
        fs.writeFileSync(`sertifikat/${opt.year}/${opt.addf}sp.pdf`, result)
      }     
    }
  );
}

router.get('/gensp', async function (req, res) {

  let data = await db.query(`select
      PermohonanID,
      SerialNumber,
      YEAR(SignedDate) year,
      RIGHT(SignedUrl,11) addf,
      SignedDate
    from uji_sertifikat
    where SignedDate > '2025-03-18'`);

  for(let d of data) {
    // console.log(d)
    await renderSuratPengantar(d)
  }
  res.send({success: true})

})

const registerRoute = (wa) => {
  router.post('/manual-download', async function (req, res) {
    let {FilePath, PermohonanID} = req.body;
    
    let addf = moment().format('DDHHmmssSSS')
    let year = moment().format('YYYY')
    
    let d = await db.exec("UJI_SelSerialNumber", {PermohonanID, RawUrl: FilePath});
    if (d.length && d[0].RefenceID && d[0].RefenceID.match(/-/)) {
      let refId = d[0].RefenceID.split('-')
      year = refId[0]
      addf = refId[1]
    }
    
    db.query(`UPDATE uji_sertifikat SET SerialNumber = '${d[0].SerialNumber}', ReferenceID = '${year}-${addf}' 
      WHERE PermohonanID = ${PermohonanID} AND RawUrl = '${FilePath}'
      AND ReferenceID IS NULL`)

    let qrimg = new Promise((resolve, reject) => {
      QRCode.toFile(`./tmp/qrcode_${addf}.png`,
        `https://silakon.dpubinmarcipka.jatengprov.go.id/reports/uji/sertifikat/${year}-${addf}`,
        (err) => {
          if (err) reject(err)
          resolve()
        })
    })

    let year_dir = moment().format('YYWW')
    if (!fs.existsSync('uploads/' + year_dir)) {
      fs.mkdirSync('uploads/' + year_dir)
      fs.mkdirSync('uploads/' + year_dir + '/ori/')
      fs.mkdirSync('uploads/' + year_dir + '/med/')
      fs.mkdirSync('uploads/' + year_dir + '/small/')
      fs.mkdirSync('uploads/' + year_dir + '/tiny/')
    }

    let fp = "." + FilePath.replace(/pdf/, 'xlsx')
    if (!fs.existsSync(fp)) {
      fp = "." + FilePath.replace(/pdf/, 'docx')
    }

    console.log("Generating..");
    carbone_render(
      fp,
      {
        SerialNumber: d[0].SerialNumber,
        TanggalTTD: moment().format('DD MMMM YYYY')
      },
      {convertTo: "pdf"},
      async (err, result) => {
        if (err) return console.log(err);

        const pdfDoc = await PDFDocument.load(result)
        const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica)

        // Get the first page of the document
        const pages = pdfDoc.getPages()

        let i = 1
        for (let firstPage of pages) {
          // Draw footer
          await drawFooter(pdfDoc, firstPage, i, pages)

          //qrcode
          await qrimg
          const pngImage = await pdfDoc.embedPng(fs.readFileSync(`./tmp/qrcode_${addf}.png`))
          firstPage.drawImage(pngImage, {
            x: 45,
            y: 15,
            width: 50,
            height: 50,
          })

          i++
        }

        result = await pdfDoc.save()
        fs.writeFileSync(`tmp/sertifikat_${year}-${addf}.pdf`, result)
        // console.log(path.resolve(__dirname + `/../../tmp/sertifikat_${year}-${addf}.pdf`))
        // res.sendFile(path.resolve(__dirname + `/../../tmp/sertifikat_${year}-${addf}.pdf`))
        res.send({
          success: true,
          RawUrl: `sertifikat_${year}-${addf}.pdf`
        })
      }
    );
  });
  router.post('/manual-sign', async function (req, res) {
    let {FilePath, SignedUrl, PermohonanID} = req.body;
    
    let year = 0
    let addf = 0
    let d = await db.exec("UJI_SelSerialNumber", {PermohonanID, RawUrl: FilePath});
    if (d.length && d[0].RefenceID && d[0].RefenceID.match(/-/)) {
      let refId = d[0].RefenceID.split('-')
      year = refId[0]
      addf = refId[1]
    } else {
      res.send({
        success: false,
        message: 'Draft belum didownload'
      })
      return
    }


    let resdb = null
    try {
      resdb = db.exec("UJI_SavSign", {
        PermohonanID: PermohonanID,
        SerialNumber: d[0].SerialNumber,
        NIK: '0',
        RawUrl: FilePath,
        SignedUrl: `/reports/uji/sertifikat/${year}-${addf}`
      });
      if (!fs.existsSync(`sertifikat/${year}`)) {
        fs.mkdirSync(`sertifikat/${year}`)
      }
      fs.copyFileSync(SignedUrl.replace(/^\//,''), `sertifikat/${year}/${addf}.pdf`)

      renderSuratPengantar({
        SerialNumber: d[0].SerialNumber,
        PermohonanID,
        year,
        addf
      })
    } catch (ex) {
      console.error('Sign Error: ' + ex.message)
      res.send({
        success: false,
        message: 'Gagal ditandatangani: ' + ex.message
      })
      return
    }

    resdb = await resdb
    let str = `Kami dari BP2 Prov. Jawa Tengah menginformasikan bahwa pengujian *${resdb[0].NamaKegiatan}* dengan Kode *${resdb[0].NoPengujian}* telah selesai.\n\n`
          + `Lembar Kerja dapat diunduh pada tautan/link berikut https://silakon.dpubinmarcipka.jatengprov.go.id/${resdb[0].LkSignedUrl}\n\n
`         + `Surat pengantar dapat diunduh pada tautan/link berikut https://silakon.dpubinmarcipka.jatengprov.go.id/reports/uji/sertifikat/${year}-${addf}sp\n\n`
          + `Sedangkan Sertifikat Hasil Uji (SHU) dapat diunduh melalui tautan/link https://silakon.dpubinmarcipka.jatengprov.go.id/reports/uji/sertifikat/${year}-${addf}.\n\n`
          + 'Mohon partisipasinya dalam mengisi survei kepuasan pelanggan pada link di bawah ini:\n'
          + `https://eskm.jatengprov.go.id/skm/340`
    if (resdb[0].Phone) {
      try {
        let destNumber = resdb[0].Phone.replace('+', '').replace(/^0/, '62') + '@c.us'
        // let m = await wa.sendMessage(resdb[0].Phone.replace('+','').replace(/^0/,'62')+'@c.us', str);
        await db.query(`INSERT INTO web_whatsapp values (NOW(), ${db.esc(str)}, '${destNumber}', NULL)`)
      } catch (ex) {
        console.error('WA Error:' + ex.message)
      }
    }

    if (resdb[0].Email) {
      try {
        let signedFn = resdb[0].LkUrl.replace(/\.\w{3,4}$/, '-signed.pdf')
        sendMail({
          from: "BP2 Prov Jateng<<EMAIL>>", // sender address
          to: resdb[0].Email, // receiver email
          subject: "Notifiksasi Pengujian "+resdb[0].NoPengujian, // Subject line
          text: str,
          html: str.replaceAll('\n', '<br />'),
          attachments: [{
            filename: `sertifikat-${resdb[0].NoPengujian}.pdf`,
            path: path.join(__dirname, `./sertifikat/${year}/${addf}.pdf`),
            contentType: 'application/pdf'
          },{
            filename: `lembar-kerja-${resdb[0].NoPengujian}.pdf`,
            path: path.join(__dirname + `/../../${signedFn}`),
            contentType: 'application/pdf'
          }]
        })
      } catch (ex) {
        console.error('Email Error:' + ex.message)
      }
    }

    db.exec('Arch_InsApiLogs', {
      PubKey: '0',
      RequestUrl: 'sign',
      RequestData: JSON.stringify({FilePath, PermohonanID, addf}),
      SpCall: 'success'
    })
    res.send({
      success: true,
      message: 'Dokumen telah ditandatangai'
    })
    
  });
  router.post('/sign', async function(req, res) {
    let {FilePath, Passphrase, PermohonanID} = req.body;
    let NIK = '3374026403790004' // '3374071311650003'

    
    let logs = db.exec('Arch_InsApiLogs', {
      PubKey: NIK,
      RequestUrl: 'sign',
      RequestData: JSON.stringify({FilePath, PermohonanID})
    })
  
    let addf = moment().format('DDHHmmssSSS') 
    let year = moment().format('YYYY') 
    let qrimg = new Promise((resolve, reject) => {
      QRCode.toFile(`./tmp/qrcode_${addf}.png`, 
        `https://silakon.dpubinmarcipka.jatengprov.go.id/reports/uji/sertifikat/${year}-${addf}`, 
        (err) => {
          if(err) reject(err)
          resolve()
        })  
    })

    let year_dir = moment().format('YYWW')
    if (!fs.existsSync('uploads/' + year_dir)) {
      fs.mkdirSync('uploads/' + year_dir)
      fs.mkdirSync('uploads/' + year_dir + '/ori/')
      fs.mkdirSync('uploads/' + year_dir + '/med/')
      fs.mkdirSync('uploads/' + year_dir + '/small/')
      fs.mkdirSync('uploads/' + year_dir + '/tiny/')
    }

  
    let d = await db.exec("UJI_SelSerialNumber", {});
  
    let fp = "."+FilePath.replace(/pdf/,'xlsx')
    if(!fs.existsSync(fp)) {
      fp = "."+FilePath.replace(/pdf/,'docx')
    }
  
    console.log("Generating..");
    carbone_render(
      fp,
      {
        SerialNumber: d[0].SerialNumber,
        TanggalTTD: moment().format('DD MMMM YYYY')
      },
      {convertTo: "pdf"},
      async (err, result) => {
        if (err) return console.log(err);
        
        const pdfDoc = await PDFDocument.load(result)
        const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica)
  
        // Get the first page of the document
        const pages = pdfDoc.getPages()
        
        let i = 1
        for(let firstPage of pages){
          // Draw footer
          await drawFooter(pdfDoc, firstPage, i, pages)
  
          //qrcode
          await qrimg
          const pngImage = await pdfDoc.embedPng(fs.readFileSync(`./tmp/qrcode_${addf}.png`))
          firstPage.drawImage(pngImage, {
            x: 45,
            y: 15,
            width: 50,
            height: 50,
          })
  
          i++
        }
  
        result = await pdfDoc.save()
        fs.writeFileSync(`tmp/sertifikat.pdf`, result)
        // SIGN
        const formData = new FormData();
        formData.append('file', fs.createReadStream(`tmp/sertifikat.pdf`));
        formData.append('nik', NIK);
        formData.append('passphrase', Passphrase);
        formData.append('tampilan', 'invisible');
        formData.append('jenis_response', 'BASE64');
        let is_err = null
        let signed = await axios({
          method: "post",
          url: "http://10.1.105.43/api/sign/pdf",
          data: formData,
          headers: {
            ...formData.getHeaders(),
            'Authorization':'Basic c2lsYWtvbjojIyMqUyFsQGswbiojIyM=',
          },
        })
          .catch(function (response) {
            //handle error
            is_err = response
          });
  
        if(is_err || !signed.data) {
          db.exec('Arch_InsApiLogs', {
            PubKey: NIK,
            RequestUrl: 'sign',
            RequestData: JSON.stringify({FilePath, PermohonanID, addf}),
            SpCall: JSON.stringify(is_err.response.data)
          })
          res.send({
            success: false,
            message: 'Gagal ditandatangani:'+is_err.response.data.error
          })
          return
        }
    
        let resdb = null
        try {
          resdb = db.exec("UJI_SavSign", {
            PermohonanID: PermohonanID,
            SerialNumber: d[0].SerialNumber,
            NIK: NIK,
            RawUrl: fp.replace(/^./,''),
            ReferenceID: signed.data.id_dokumen,
            SignedUrl: `/reports/uji/sertifikat/${year}-${addf}`
          });
          if (!fs.existsSync(`sertifikat/${year}`)) {
            fs.mkdirSync(`sertifikat/${year}`)
          }
          fs.writeFileSync(`sertifikat/${year}/${addf}.pdf`, 
            signed.data.base64_signed_file, {encoding: 'base64'});

          renderSuratPengantar({
            SerialNumber: d[0].SerialNumber,
            PermohonanID,
            NIK,
            Passphrase,
            year,
            addf
          })

        } catch(ex){
          console.error('Sign Error: '+ex.message)
          res.send({
            success: false,
            message: 'Gagal ditandatangani: '+ex.message
          })
          return
        }
  
        resdb = await resdb
        if(resdb && resdb.length>0 && resdb[0].Phone) {
          let str = `Kami dari BP2 Prov. Jawa Tengah menginformasikan bahwa pengujian *${resdb[0].NamaKegiatan}* dengan Kode *${resdb[0].NoPengujian}* telah selesai.\n\n`
          +`Lembar Kerja dapat diunduh pada tautan/link berikut https://silakon.dpubinmarcipka.jatengprov.go.id/${resdb[0].LkSignedUrl}\n`
          +`Surat Pengantar dapat diunduh pada tautan/link berikut https://silakon.dpubinmarcipka.jatengprov.go.id/reports/uji/sertifikat/${year}-${addf}sp\n\n`
          +`Sedangkan Sertifikat Hasil Uji (SHU) dapat diunduh melalui tautan/link https://silakon.dpubinmarcipka.jatengprov.go.id/reports/uji/sertifikat/${year}-${addf}.\n\n` 
          +'Mohon partisipasinya dalam mengisi survei kepuasan pelanggan pada link di bawah ini:\n'
          +`https://eskm.jatengprov.go.id/skm/340`
          try {
            let destNumber = resdb[0].Phone.replace('+', '').replace(/^0/, '62') + '@c.us'
            // let m = await wa.sendMessage(resdb[0].Phone.replace('+','').replace(/^0/,'62')+'@c.us', str);
            await db.query(`INSERT INTO web_whatsapp values (NOW(), ${db.esc(str)}, '${destNumber}', NULL)`)
          } catch(ex){
            console.error('WA Error:'+ex.message)
          }
        }        

        if (resdb[0].Email) {
          try {
            let signedFn = resdb[0].LkUrl.replace(/\.\w{3,4}$/, '-signed.pdf')
            sendMail({
              from: "BP2 Prov Jateng<<EMAIL>>", // sender address
              to: resdb[0].Email, // receiver email
              subject: "Notifiksasi Pengujian "+resdb[0].NoPengujian, // Subject line
              text: str,
              html: str.replaceAll('\n', '<br />'),
              attachments: [{
                filename: `sertifikat-${resdb[0].NoPengujian}.pdf`,
                path: path.join(__dirname, `./sertifikat/${year}/${addf}.pdf`),
                contentType: 'application/pdf'
              },{
                filename: `lembar-kerja-${resdb[0].NoPengujian}.pdf`,
                path: path.join(__dirname + `/../../${signedFn}`),
                contentType: 'application/pdf'
              }]
            })
          } catch (ex) {
            console.error('Email Error:' + ex.message)
          }
        }

        db.exec('Arch_InsApiLogs', {
          PubKey: NIK,
          RequestUrl: 'sign',
          RequestData: JSON.stringify({FilePath, PermohonanID, addf}),
          SpCall: 'success'
        })
        res.send({
          success: true,
          message: 'Dokumen telah ditandatangai'
        })
      }
    );
  });
  router.get('/resend-all', async function (req, res) {
    let resdb = await db.query('SELECT CreatedAt, Message, DestNumber, SendAt FROM web_whatsapp WHERE SendAt IS NULL LIMIT 20')
    for (let r of resdb) {
      const secs = ((Math.random() * 10) % 8) * 20000
      console.log('wait for ' + secs)
      await sleep(secs)


      await wa.sendMessage(r.DestNumber, r.Message);
      await db.query(`UPDATE web_whatsapp SET SendAt = NOW() WHERE DestNumber = '${r.DestNumber}' AND CreatedAt = '${new Date(r.CreatedAt).toISOString().slice(0, 19).replace('T', ' ')}'`)
    }
    res.send({success: true, data: 'OK'})
  }); 
  router.post('/resend', async function(req, res) { 
    let resdb = await db.exec('UJI_SelPermohonan', req.body)
    let sertifikat = resdb[0].SignedUrl.split(',').map(s => 'https://silakon.dpubinmarcipka.jatengprov.go.id'+s).join('\n')
    let lembarKerja = '-'
    if (resdb[0].LkSignedUrl)
      lembarKerja = resdb[0].LkSignedUrl.split(',').map(s => 'https://silakon.dpubinmarcipka.jatengprov.go.id/'+s).join('\n')
    
    let str = `Kami dari BP2 Prov.Jawa Tengah menginformasikan bahwa pengujian *${resdb[0].NamaKegiatan}* dengan Kode * ${resdb[0].NoPengujian} *, telah selesai.\n`
      + 'Lembar kerja dapat di unduh pada tautan/link berikut:\n'
      + lembarKerja + '\n\n'
      + 'Sedangkan Sertifikat Hasil Uji (SHU) dapat diunduh melalui tautan/link:\n'
      + sertifikat
    if (resdb[0].Phone) {
      try {
        // console.log(resdb[0].Phone.replace(/^0/,'62').replace('+',''))
        let destNumber = resdb[0].Phone.replace(/^0/, '62').replace('+', '') + '@c.us'
        // let m = await wa.sendMessage(destNumber, str);
        await db.query(`INSERT INTO web_whatsapp values(NOW(), ${db.esc(str)}, '${destNumber}', NULL)`)
      } catch (ex) {
        console.log('WA Error:' + ex.message)
        res.send({
          success: false,
          message: 'Notifikasi Gagal Terkirim'
        })
        return
      }
    }
    if (resdb[0].Email) {
      try {
        let signedFn = resdb[0].LkUrl.replace(/\.\w{3,4}$/, '-signed.pdf')
        sendMail({
          from: "BP2 Prov Jateng<<EMAIL>>", // sender address
          to: resdb[0].Email, // receiver email
          subject: "Notifiksasi Pengujian "+resdb[0].NoPengujian, // Subject line
          text: str,
          html: str.replaceAll('\n', '<br />'),
          // attachments: [{
          //   filename: `sertifikat-${resdb[0].NoPengujian}.pdf`,
          //   path: path.join(__dirname, `./sertifikat/${year}/${addf}.pdf`),
          //   contentType: 'application/pdf'
          // },{
          //   filename: `lembar-kerja-${resdb[0].NoPengujian}.pdf`,
          //   path: path.join(__dirname + `/../../${signedFn}`),
          //   contentType: 'application/pdf'
          // }]
        })
      } catch (ex) {
        console.error('Email Error:' + ex.message)
      }
    }
    

    res.send({
      success: true,
      message: 'Notifikasi Terkirim'
    })
  });
}

module.exports = (wa) => {
  registerRoute(wa)
  return router
}