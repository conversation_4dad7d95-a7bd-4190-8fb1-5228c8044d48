#!/usr/bin/env python3
"""Enhanced Bluetooth device discovery with proper error handling and debugging."""

import asyncio
import sys
from bleak import BleakClient, BleakScanner


async def scan_ble_with_debug():
    """Scan for BLE devices with detailed debugging."""
    print("\n=== BLE Device Discovery Debug ===")
    
    # Method 1: Standard discovery with callback for AdvertisementData
    print("\n1. Standard discovery (10 second timeout)...")
    
    devices_with_data = []
    
    def detection_callback(device, advertisement_data):
        devices_with_data.append((device, advertisement_data))
    
    scanner = BleakScanner(detection_callback=detection_callback)
    await scanner.start()
    await asyncio.sleep(10.0)
    await scanner.stop()
    
    print(f"Found {len(devices_with_data)} BLE devices:")
    
    for device, adv_data in devices_with_data:
        print(f"   {device.address} - {device.name or 'Unknown'}")
        print(f"   RSSI: {adv_data.rssi if adv_data else 'N/A'} dBm")
        
        # Method 2: Try to find device immediately after discovery
        print(f"   Attempting to find device by address...")
        try:
            d = await BleakScanner.find_device_by_address(
                device.address, 
                timeout=5.0  # Shorter timeout for individual device
            )
            if d:
                print(f"   ✓ Device {d.address} found successfully")
                print(f"   Device details: {d}")
            else:
                print(f"   ✗ Device {d.address} NOT found by address")
                
                # Method 3: Try discovery again with same device
                print(f"   Trying fresh discovery with filter...")
                fresh_devices = await BleakScanner.discover(timeout=5.0)
                found_device = None
                for fd in fresh_devices:
                    if fd.address.lower() == device.address.lower():
                        found_device = fd
                        break
                
                if found_device:
                    print(f"   ✓ Device found in fresh discovery: {found_device}")
                else:
                    print(f"   ✗ Device completely disappeared")
                    
        except Exception as e:
            print(f"   Error finding device: {e}")

async def test_specific_address(address):
    """Test finding a specific device address."""
    print(f"\n=== Testing specific address: {address} ===")
    
    # Method 1: Direct lookup
    print("1. Direct lookup...")
    device = await BleakScanner.find_device_by_address(address, timeout=30.0)
    if device:
        print(f"✓ Found: {device}")
        # Connect to the device
        await connect(device.address)
        print(f"Connected to {address}")
        return True
    
    print("✗ Not found via direct lookup")
    
    # Method 2: Discovery with filtering
    print("2. Discovery with filtering...")
    devices = await BleakScanner.discover(timeout=10.0)
    for d in devices:
        if d.address.lower() == address.lower():
            print(f"✓ Found in discovery: {d}")
            return True
    
    print("✗ Not found in discovery either")
    return False


async def connect(address):
    async with BleakClient(address, timeout=30.0) as client:
        print(client.services)
        print("Connected and retrieved services")


async def compare_scan_methods():
    """Compare different scanning methods."""
    print("\n=== Comparing Scan Methods ===")
    
    # Method 1: Modern discovery with callback
    print("1. Modern discovery with callback (10s)...")
    devices1 = []
    
    def callback1(device, advertisement_data):
        devices1.append((device, advertisement_data))
    
    scanner1 = BleakScanner(detection_callback=callback1)
    await scanner1.start()
    await asyncio.sleep(10.0)
    await scanner1.stop()
    
    print(f"   Found: {len(devices1)} devices")
    
    # Method 2: Find device by address for each discovered device
    print("2. Individual address lookup...")
    found_count = 0
    for device, adv_data in devices1:
        try:
            found_device = await BleakScanner.find_device_by_address(
                device.address,
                timeout=3.0
            )
            if found_device:
                found_count += 1
        except Exception as e:
            print(f"   Error looking up {device.address}: {e}")
    
    print(f"   Found {found_count}/{len(devices1)} devices via individual lookup")
    
    # Method 3: Fresh discovery
    print("3. Fresh discovery (10s)...")
    devices2 = []
    
    def callback2(device, advertisement_data):
        devices2.append((device, advertisement_data))
    
    scanner2 = BleakScanner(detection_callback=callback2)
    await scanner2.start()
    await asyncio.sleep(10.0)
    await scanner2.stop()
    
    print(f"   Found: {len(devices2)} devices")
    
    # Compare results
    print("4. Comparison:")
    print(f"   First discovery: {len(devices1)} devices")
    print(f"   Second discovery: {len(devices2)} devices")
    
    # Show which devices disappeared
    first_addresses = {device.address.lower() for device, _ in devices1}
    second_addresses = {device.address.lower() for device, _ in devices2}
    
    disappeared = first_addresses - second_addresses
    appeared = second_addresses - first_addresses
    
    if disappeared:
        print(f"   Disappeared: {disappeared}")
    if appeared:
        print(f"   Newly appeared: {appeared}")

async def main():
    """Main function with menu."""
    print("BLE Device Discovery Debugger")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        # Test specific address
        address = sys.argv[1]
        await test_specific_address(address)
    else:
        # Run full debug
        await scan_ble_with_debug()
        await compare_scan_methods()

if __name__ == "__main__":
    asyncio.run(main())