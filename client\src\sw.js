import { precacheAndRoute, cleanupOutdatedCaches } from 'workbox-precaching'
import { clientsClaim } from 'workbox-core'

// Precache all the assets generated by your build process
precacheAndRoute(self.__WB_MANIFEST)

// Clean up any previous workbox caches
cleanupOutdatedCaches()

// Take control of all clients as soon as the service worker is activated
// But only after receiving the SKIP_WAITING message
let shouldSkipWaiting = false

// Listen for messages from the client
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    console.log('SW: Received SKIP_WAITING message')
    shouldSkipWaiting = true
    self.skipWaiting()
  }
})

// Handle the install event
self.addEventListener('install', (event) => {
  console.log('SW: Installing new service worker')
  // Don't automatically skip waiting - wait for user confirmation
})

// Handle the activate event
self.addEventListener('activate', (event) => {
  console.log('SW: Activating new service worker')
  if (shouldSkipWaiting) {
    // Claim all clients immediately
    event.waitUntil(clientsClaim())
  }
})

// Handle push notifications (keeping existing functionality)
self.addEventListener('push', (event) => {
  if (!event.data) return

  try {
    const data = event.data.json()
    const options = {
      body: data.body,
      icon: '/img/notification-icon.png',
      badge: '/img/notification-badge.png',
      data: {
        url: data.url || '/',
      },
    }

    event.waitUntil(self.registration.showNotification(data.title, options))
  } catch (error) {
    console.error('SW: Error handling push notification:', error)
  }
})

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close()
  event.waitUntil(self.clients.openWindow(event.notification.data.url))
})
