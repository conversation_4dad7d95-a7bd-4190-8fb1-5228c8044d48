import Vue from 'vue'

const initialState = () => ({
  title: 'PROMPT',
  label: 'Masukkan:',
  value: '',
  type: 'text',
  show: false,
  onsave: null,
})
const state = initialState()

const getters = {
  getPromptData: (state) => ({ ...state }),
  getPromptShow: (state) => {
    return state.show
  },
}

const actions = {
  setPromptShow({ commit }, show) {
    commit(types.SET_SHOW, show)
  },
  setPromptValue({ commit }, val) {
    commit(types.SET_VALUE, val)
  },
  createPrompt({ commit }, opt) {
    commit(types.CREATE, opt)
  },
}

const types = {
  CREATE: 'prompt/CREATE',
  SET_SHOW: 'prompt/SET_SHOW',
  SET_VALUE: 'prompt/SET_VALUE',
}

const mutations = {
  [types.CREATE](state, opt) {
    Vue.set(state, 'title', opt.title || 'MASUKKAN DATA')
    Vue.set(state, 'label', opt.label || '')
    Vue.set(state, 'value', opt.value || '')
    Vue.set(state, 'type', opt.type || 'text')
    Vue.set(state, 'show', opt.show || true)
    Vue.set(
      state,
      'onsave',
      opt.onsave ||
        (() => {
          alert('Save function is not defined')
        })
    )
  },
  [types.SET_SHOW](state, show) {
    Vue.set(state, 'show', show)
  },
  [types.SET_VALUE](state, val) {
    Vue.set(state, 'value', val)
  },
}

export default {
  state,
  mutations,
  getters,
  actions,
  types,
}
