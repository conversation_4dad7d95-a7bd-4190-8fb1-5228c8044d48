import{n as l,a as r,l as o,_ as s,g as n,m as d,o as p,V as c,p as u,f as m,c as f,h,k as _}from"./index-DYIZrBBo.js";import{A as v}from"./AlatDet-ecQII-bj.js";const g={data:()=>({keyword:""}),props:{statusId:String,rebind:Number,addButton:Boolean,filters:Object},computed:{dbparams(){return{Keyword:this.keyword||""}}},methods:{ItemClick(i){this.$emit("item-click",i)},DaftarBaru(){this.$emit("item-click",{AlatId:"-"})}}};var w=function(){var t=this,e=t._self._c;return e("div",{staticClass:"sidepane"},[e("div",{staticStyle:{padding:"10px",display:"flex"}},[e(r,{staticClass:"searchbar",attrs:{type:"text",value:t.keyword,placeholder:"Cari ..",width:"100%",rightIcon:"mdi-magnify"},on:{"update:value":function(a){t.keyword=a}}})],1),e("div",{staticStyle:{height:"calc(100% - 47px)"}},[e(o,{attrs:{dbref:"PML.SelAlatList",dbparams:t.dbparams,height:t.addButton?"calc(100% - 60px)":"100%",rebind:t.rebind},on:{itemClick:t.ItemClick},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"5px 10px"}},[e("div",{staticStyle:{color:"gray",float:"right"}},[t._v(" "+t._s(a.Tahun)+" ")]),e("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(a.NamaAlat)+" ")]),e("div",{staticStyle:{color:"gray",display:"flex"}},[e("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis",width:"160px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(a.Merk)+" / "+t._s(a.Tipe)+" ")])])])]}}])}),e("div",{staticStyle:{padding:"10px","border-top":"1px solid #ddd"}},[e(s,{staticStyle:{width:"calc(100% - 10px)"},attrs:{outlined:"",color:"primary"},on:{click:t.DaftarBaru}},[e(n,{attrs:{left:""}},[t._v("mdi-plus")]),t._v(" TAMBAH ALAT ")],1)],1)],1)])},y=[],b=l(g,w,y,!1,null,null);const x=b.exports,k={components:{VueQrcode:c,SidePane:x,AlatDet:v},data:()=>({datagrid:[],dbparams:{AlatId:""},lembarKerja:[],loading:!1,showDet:!1,forms:{},rebind:1,rebindSidebar:1,masalah:{show:!1}}),watch:{showDet(i){i||this.rebind++}},computed:{...p({user:"getUser"}),showSubmitButton(){return this.lembarKerja.filter(t=>!t.Alasan&&!t.ApprovedBy).length>0},qrUrl(){return window.location.origin+"/daftar-alat/"+this.forms.AlatId},disabled(){return this.user.RolePositionID==12&&this.user.UserID!=this.forms.PIC}},methods:{...d(["setPageFocused"]),async ItemClick(i){this.setPageFocused(!0),this.Populate(this.$api.toUUID(i.AlatId))},async Populate(i){this.dbparams={AlatId:i};var t=await this.$api.call("PML.SelAlat",{AlatId:i});t.data.length?this.forms=t.data[0]:this.forms={}},async Save(){let i=await this.$api.call("PML.SavAlat",this.forms);i.success&&(this.Populate(i.data[0].AlatId),this.rebindSidebar++)},async Delete(){if(!confirm("Yakin menghapus alat ini?"))return;(await this.$api.call("PML.DelAlat",this.forms)).success&&(this.Populate(0),this.rebindSidebar++)},async DelActivity(i){confirm("Yakin menghapus baris ini?")&&await this.$api.call("PML.DelAlatActivity",{AlatActivityId:i})},PrintQR(){let i=document.getElementById("qralat").src,t=window.open("about:blank","_new");t.document.open(),t.document.write(["<html>","   <head>",'<link rel="preconnect" href="https://fonts.googleapis.com">','<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>','<link href="https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">',"   </head>",'   <body onload="window.print()" onafterprint="window.close()">','       <img src="'+i+'" width="200px"/><br />','       <div style="width:200px; text-align:center; font-family:raleway; margin-top:-8px;">',this.forms.NamaAlat,"</div>","   </body>","</html>"].join("")),t.document.close()}}};var A=function(){var t=this,e=t._self._c;return e(u,{attrs:{sidebar:!0}},[e("SidePane",{attrs:{addButton:!0,rebind:t.rebindSidebar},on:{"item-click":t.ItemClick}}),e("div",{class:t.$api.isMobile()?"":"form-inline",staticStyle:{"max-width":"1000px",padding:"0 20px","padding-bottom":"50px"},style:{"margin-left":t.$api.isMobile()?"10px":"0"}},[t.forms.AlatId?e("div",{staticStyle:{float:"right","font-family":"raleway",background:"white","margin-top":"-20px",width:"210px"},attrs:{id:"dvRightBox"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:!t.$api.isMobile(),expression:"!$api.isMobile()"}],staticStyle:{padding:"10px 20px","text-align":"center","margin-top":"10px"}},[e("VueQrcode",{style:{width:"150px",height:"150px"},attrs:{id:"qralat",value:t.qrUrl}})],1),e(s,{directives:[{name:"show",rawName:"v-show",value:!t.$api.isMobile(),expression:"!$api.isMobile()"}],staticStyle:{"border-top":"1px solid #e3e3e3","text-align":"center",width:"210px"},attrs:{text:"",small:"",color:"primary"},on:{click:t.PrintQR}},[t._v(" PRINT BARCODE ")])],1):t._e(),e("div",{style:{display:t.$api.isMobile()?"":"flex"}},[e("div",{staticClass:"alat-img",style:{width:t.$api.isMobile()?"100%":""}},[e(m,{attrs:{value:t.forms.Foto},on:{"update:value":function(a){return t.$set(t.forms,"Foto",a)}}})],1),e("div",{class:t.$api.isMobile()?"":"form-inline",staticStyle:{"max-width":"450px",width:"100%"},style:{"margin-left":t.$api.isMobile()?"0":"20px","margin-top":t.$api.isMobile()?"0":"20px"}},[e(r,{attrs:{type:"text",label:"Nama Alat",value:t.forms.NamaAlat,width:"300px"},on:{"update:value":function(a){return t.$set(t.forms,"NamaAlat",a)}}}),e(r,{attrs:{type:"text",label:"Nomor",value:t.forms.NomorAlat,width:"300px"},on:{"update:value":function(a){return t.$set(t.forms,"NomorAlat",a)}}}),e(r,{attrs:{type:"text",label:"Merk",value:t.forms.Merk,width:"300px"},on:{"update:value":function(a){return t.$set(t.forms,"Merk",a)}}}),e(r,{attrs:{type:"text",label:"Tipe",value:t.forms.Tipe,width:"300px"},on:{"update:value":function(a){return t.$set(t.forms,"Tipe",a)}}}),e(r,{attrs:{type:"number",label:"Tahun",value:t.forms.Tahun,width:"300px"},on:{"update:value":function(a){return t.$set(t.forms,"Tahun",a)}}}),e(f,{attrs:{dbref:"PML_SelTeknisi",dbparams:{nocache:!0},label:"PIC",value:t.forms.PIC,width:"300px"},on:{"update:value":function(a){return t.$set(t.forms,"PIC",a)}}}),e(r,{attrs:{type:"number",label:"Periode Pemeliharaan",value:t.forms.PeriodePemeliharaan,width:"300px",postfix:"tahun"},on:{"update:value":function(a){return t.$set(t.forms,"PeriodePemeliharaan",a)}}})],1)]),e("br"),t.forms.AlatId?e(h,{attrs:{datagrid:t.datagrid,dbref:"PML_SelAlatActivity",dbparams:t.dbparams,doRebind:t.rebind,disabled:!0,columns:[{name:"Tanggal",value:"Tanggal"},{name:"Aktifitas",value:"Aktifitas",width:"100px"},{name:"Keterangan",value:"Keterangan",width:"300px"},{name:"Bukti",value:"Bukti"},{name:"",value:"Delete"}]},on:{"update:datagrid":function(a){t.datagrid=a}},scopedSlots:t._u([{key:"row-Tanggal",fn:function({row:a}){return[t._v(" "+t._s(t._f("format")(a.Tanggal))+" ")]}},{key:"row-Delete",fn:function({row:a}){return[e(n,{attrs:{color:"error"},on:{click:function(P){return t.DelActivity(a.AlatActivityId)}}},[t._v(" mdi-trash-can ")])]}},{key:"footer",fn:function(){return[e("tr",[e("td",{attrs:{colspan:"4"}},[e(s,{attrs:{text:"",small:""},on:{click:function(a){t.showDet=!0}}},[e(n,{attrs:{left:""}},[t._v("mdi-plus-circle")]),t._v(" Tambah ")],1)],1)])]},proxy:!0},{key:"row-Bukti",fn:function({row:a}){return[e("a",{attrs:{href:t.$api.url+a.Bukti,target:"_blank"}},[e(n,{attrs:{color:"primary"}},[t._v("mdi-open-in-new")]),t._v(" Buka ")],1)]}}],null,!1,4126180271)}):t._e(),e("br"),e("br"),t.disabled?t._e():e("div",{staticStyle:{display:"flex","margin-bottom":"30px"}},[e(s,{staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:t.Save}},[t._v(" SIMPAN ")]),e(_),e(s,{staticStyle:{"margin-left":"5px"},attrs:{color:"error"},on:{click:t.Delete}},[t._v(" HAPUS ")])],1)],1),e("alat-det",{attrs:{show:t.showDet,alatId:t.forms.AlatId},on:{"update:show":function(a){t.showDet=a}}})],1)},S=[],$=l(k,A,S,!1,null,null);const D=$.exports;export{D as default};
