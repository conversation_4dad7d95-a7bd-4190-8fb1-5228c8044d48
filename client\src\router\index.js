/* eslint-disable no-undef */
import Vue from 'vue'
import VueRouter from 'vue-router'
//import store from "../store/modules/common";
import Public from '../pages/App/Public/FrontPage.vue'
import Login from '../pages/App/Login.vue'
import Home from '../pages/App/Home.vue'
import Dashboard from '../pages/Dashboard/Dashboard.vue'
import Logout from '../pages/App/Logout.vue'
import Pendaftaran from '../pages/Loket/Pendaftaran.vue'
import Koordinator from '../pages/Koordinator/CekSample.vue'
import CekProgress from '../pages/Laborat/CekProgress.vue'
import CekSertifikat from '../pages/Sertifikasi/CekSertifikat.vue'
import MasterData from '../pages/MasterData/MasterData.vue'
import Laporan from '../pages/Report.vue'
import AdminRoutes from './administrasi'
import UserRoutes from './user'

Vue.use(VueRouter)

const routes = [
  ...AdminRoutes,
  {
    path: '/',
    component: Public,
    meta: { title: 'SILAKON', noauth: true },
  },
  {
    path: '/Main',
    alias: '/Main/App/Home',
    component: Home,
    meta: { title: 'SILAKON' },
    children: [
      {
        path: '/',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: 'Dashboard' },
      },
      {
        path: 'Loket/Index/:id',
        name: 'Pendaftaran',
        component: Pendaftaran,
        meta: { title: 'LOKET - Pendaftaran' },
      },
      {
        path: 'Loket/Index',
        name: 'Pendaftaran2',
        component: Pendaftaran,
        meta: { title: 'LOKET - Pendaftaran' },
      },
      {
        path: 'Loket/Search',
        name: 'Pencarian',
        component: () => import('../pages/Loket/Pencarian.vue'),
        meta: { title: 'LOKET - Pencarian' },
      },
      {
        path: 'Koordinator/Penerimaan',
        name: 'PenerimaanSample',
        component: () => import('../pages/Koordinator/Penerimaan.vue'),
        meta: { title: 'KOORDINATOR - Penerimaan' },
      },
      {
        path: 'Koordinator/CekSample/:id',
        name: 'Koordinator',
        component: Koordinator,
        meta: { title: 'KOORDINATOR - Cek Sample' },
      },
      {
        path: 'Laborat/CekProgress/:id',
        name: 'CekProgress',
        component: CekProgress,
        meta: { title: 'LABORAT - Daftar Pengujian' },
      },
      {
        path: 'Laborat/Persetujuan',
        name: 'PersetujuanLab',
        component: () => import('../pages/Laborat/Persetujuan/Persetujuan.vue'),
        meta: { title: 'LABORAT - Persetujuan LK' },
      },
      {
        path: 'Laborat/Sign',
        name: 'SignLab',
        component: () => import('../pages/Laborat/Sign/Sign.vue'),
        meta: { title: 'LABORAT - Pengesahan LK' },
      },
      {
        path: 'Laborat/DaftarPengujian',
        name: 'CekProgress2',
        component: CekProgress,
        meta: { title: 'LABORAT - Daftar Pengujian' },
      },
      {
        path: 'Laborat/RevisiUlang',
        name: 'RevisiUlang',
        component: () => import('../pages/Laborat/RevisiUlang.vue'),
        meta: { title: 'LABORAT - Revisi Ulang' },
      },
      {
        path: 'Teknisi/LembarKerja',
        name: 'LembarKerja',
        component: () => import('../pages/Teknisi/LembarKerja/LembarKerja.vue'),
        meta: { title: 'TEKNISI - Lembar Kerja' },
      },
      {
        path: 'Teknisi/DaftarAlat',
        name: 'DaftarAlat',
        component: () => import('../pages/Teknisi/DaftarAlat/DaftarAlat.vue'),
        meta: { title: 'TEKNISI - Daftar Alat' },
      },
      {
        path: 'Teknisi/JadwalPemeliharaan',
        name: 'JadwalPemeliharaan',
        component: () => import('../pages/Teknisi/DaftarAlat/Jadwal.vue'),
        meta: { title: 'TEKNISI - Jadwal Pemeliharaan' },
      },
      {
        path: 'Sertifikasi/CekSertifikat/:id',
        name: 'CekSertifikat',
        component: CekSertifikat,
        meta: { title: 'Surat Pengantar & Sertifikat' },
      },
      {
        path: 'App/Report',
        name: 'Laporan',
        component: Laporan,
        meta: { title: 'Laporan' },
      },
      {
        path: 'App/MasterData',
        name: 'Data Master',
        component: MasterData,
        meta: { title: 'Data Master' },
      },
      {
        path: 'Sertifikasi/Persetujuan',
        name: 'Persetujuan',
        meta: { title: 'SERTIFIKASI - Persetujuan' },
        component: () => import('../pages/Persetujuan/Persetujuan.vue'),
      },
      {
        path: 'Sertifikasi/Sign',
        name: 'Penandatanganan',
        meta: { title: 'SERTIFIKASI - Penandatanganan' },
        component: () => import('../pages/Sign/Sign.vue'),
      },
      {
        path: 'ISO/Perencanaan',
        name: 'Penjaminan Mutu',
        meta: { title: 'ISO - Penjaminan Mutu' },
        component: () => import('../pages/ISO/Planning/Planning.vue'),
      },
      {
        path: 'ISO/Suhu',
        name: 'Pencatatan Suhu',
        meta: { title: 'ISO - Pencatatan Suhu' },
        component: () => import('../pages/ISO/Suhu/Suhu.vue'),
      },
    ],
  },
  {
    path: '/User/App',
    alias: '/User/App/Home',
    component: Home,
    meta: { title: 'SILAKON' },
    children: [...UserRoutes],
  },
  {
    path: '/daftar-alat/:id',
    name: 'Alat',
    component: () => import('../pages/Alat/Alat.vue'),
    meta: { title: 'SILAKON', noauth: true },
  },
  {
    path: '/public',
    name: 'Public',
    component: Public,
    meta: { title: 'SILAKON', noauth: true },
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: 'SILAKON', noauth: true },
  },
  {
    path: '/Main/App/Logout',
    name: 'Logout',
    component: Logout,
    meta: { noauth: true },
  },
]

const router = new VueRouter({
  mode: 'history',
  // base: process.env.BASE_URL,
  base: '/',
  routes,
})

router.beforeEach((to, from, next) => {
  if (to.meta.noauth) return next()
  else {
    if (localStorage.getItem('menu-access')) {
      let menu = JSON.parse(localStorage.getItem('menu-access'))
      if (to.path === '/') next()
      else if (to.path === '/User/App/Home') next()
      else {
        // let pages = store.state.user.pages
        // if (pages[to.path] && pages[to.path].access) {
        //   store.state.pageId = pages[to.path].id
        //   return next()
        // } else {
        //   return next({ path: '/notfound' })
        // }
        if (menu[to.path]) {
          sessionStorage.setItem('coms-access', JSON.stringify(menu[to.path]))
          return next()
        } else {
          let found = false
          for (let m in menu) {
            if (to.path.match(m)) {
              found = true
              break
            }
          }
          if (found) {
            return next()
          } else {
            return next({ path: '/Main/App/Home' })
          }
        }
      }
    } else {
      return next({ path: '/login' })
    }
  }
})
const DEFAULT_TITLE = 'SILAKON'
router.afterEach((to) => {
  // Use next tick to handle router history correctly
  // see: https://github.com/vuejs/vue-router/issues/914#issuecomment-384477609
  Vue.nextTick(() => {
    document.title = to.meta.title || DEFAULT_TITLE
  })
})

export default router
