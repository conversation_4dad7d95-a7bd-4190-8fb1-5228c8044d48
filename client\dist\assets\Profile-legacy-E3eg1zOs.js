!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,e,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function u(t,o,a,i){var u=o&&o.prototype instanceof c?o:c,f=Object.create(u.prototype);return r(f,"_invoke",function(t,r,o){var a,i,u,c=0,f=o||[],l=!1,d={p:0,n:0,v:n,a:p,f:p.bind(n,4),d:function(t,r){return a=t,i=0,u=n,d.n=r,s}};function p(t,r){for(i=t,u=r,e=0;!l&&c&&!o&&e<f.length;e++){var o,a=f[e],p=d.p,v=a[2];t>3?(o=v===r)&&(u=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=n):a[0]<=p&&((o=t<2&&p<a[1])?(i=0,d.v=r,d.n=a[1]):p<v&&(o=t<3||a[0]>r||r>v)&&(a[4]=t,a[5]=r,d.n=v,i=0))}if(o||t>1)return s;throw l=!0,r}return function(o,f,v){if(c>1)throw TypeError("Generator is already running");for(l&&1===f&&p(f,v),i=f,u=v;(e=i<2?n:u)||!l;){a||(i?i<3?(i>1&&(d.n=-1),p(i,u)):d.n=u:d.v=u);try{if(c=2,a){if(i||(o="next"),e=a[o]){if(!(e=e.call(a,u)))throw TypeError("iterator result is not an object");if(!e.done)return e;u=e.value,i<2&&(i=0)}else 1===i&&(e=a.return)&&e.call(a),i<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=n}else if((e=(l=d.n<0)?u:t.call(r,d))!==s)break}catch(e){a=n,i=1,u=e}finally{c=1}}return{value:e,done:l}}}(t,a,i),!0),f}var s={};function c(){}function f(){}function l(){}e=Object.getPrototypeOf;var d=[][a]?e(e([][a]())):(r(e={},a,function(){return this}),e),p=l.prototype=c.prototype=Object.create(d);function v(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,l):(t.__proto__=l,r(t,i,"GeneratorFunction")),t.prototype=Object.create(p),t}return f.prototype=l,r(p,"constructor",l),r(l,"constructor",f),f.displayName="GeneratorFunction",r(l,i,"GeneratorFunction"),r(p),r(p,i,"Generator"),r(p,a,function(){return this}),r(p,"toString",function(){return"[object Generator]"}),(t=function(){return{w:u,m:v}})()}function r(t,n,e,o){var a=Object.defineProperty;try{a({},"",{})}catch(t){a=0}r=function(t,n,e,o){function i(n,e){r(t,n,function(t){return this._invoke(n,e,t)})}n?a?a(t,n,{value:e,enumerable:!o,configurable:!o,writable:!o}):t[n]=e:(i("next",0),i("throw",1),i("return",2))},r(t,n,e,o)}function n(t,r,n,e,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void n(t)}u.done?r(s):Promise.resolve(s).then(e,o)}System.register(["./index-legacy-BUdDePUl.js"],function(r,e){"use strict";var o,a,i,u,s;return{setters:[function(t){o=t.n,a=t.p,i=t.a,u=t._,s=t.G}],execute:function(){var e={data:function(){return{forms:{}}},methods:{md5:function(t){return s.createHash("md5").update(t).digest("hex")},Save:function(){var r,e=this;return(r=t().m(function r(){return t().w(function(t){for(;;)switch(t.n){case 0:return t.n=1,e.$api.call("Arch_SavPassword",e.forms);case 1:return t.a(2)}},r)}),function(){var t=this,e=arguments;return new Promise(function(o,a){var i=r.apply(t,e);function u(t){n(i,o,a,u,s,"next",t)}function s(t){n(i,o,a,u,s,"throw",t)}u(void 0)})})()}}};r("default",o(e,function(){var t=this,r=t._self._c;return r(a,{attrs:{title:"Ganti Password"}},[r("div",{staticClass:"form-inline padding",staticStyle:{background:"white"}},[r(i,{attrs:{label:"Password Lama",type:"password",value:t.forms.OldPassword},on:{"update:value":function(r){return t.$set(t.forms,"OldPassword",r)}}}),r("br"),r(i,{attrs:{label:"Password Baru",type:"password",value:t.forms.NewPassword},on:{"update:value":function(r){return t.$set(t.forms,"NewPassword",r)}}}),r(i,{attrs:{label:"Ulangi Password",type:"password",value:t.forms.RptPassword},on:{"update:value":function(r){return t.$set(t.forms,"RptPassword",r)}}}),r(u,{attrs:{color:"primary"},on:{click:t.Save}},[t._v("SIMPAN")])],1)])},[],!1,null,null).exports)}}})}();
