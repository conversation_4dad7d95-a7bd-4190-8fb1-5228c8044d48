#!/usr/bin/env python3
# Windows-compatible version of the temperature sensor script using bleak instead of bluepy

import asyncio
import argparse
import os
import re
from dataclasses import dataclass
from collections import deque
import threading
import time
import signal
import traceback
import math
import logging
from bleak import BleakClient, BleakScanner
import struct

@dataclass
class Measurement:
    temperature: float
    humidity: int
    voltage: float
    calibratedHumidity: int = 0
    battery: int = 0
    timestamp: int = 0

    def __eq__(self, other):
        if self.temperature == other.temperature and self.humidity == other.humidity and self.calibratedHumidity == other.calibratedHumidity and self.battery == other.battery and self.voltage == other.voltage:
            return True
        else:
            return False

measurements = deque()
previousMeasurement = Measurement(0, 0, 0, 0, 0, 0)
identicalCounter = 0
connected = False
unconnectedTime = None
connectionLostCounter = 0
client = None
stop_event = threading.Event()

# Characteristic UUIDs
TEMP_HUMID_CHAR_UUID = "00000038-0000-1000-8000-00805f9b34fb"  # Temperature and humidity characteristic
BATTERY_CHAR_UUID = "00000046-0000-1000-8000-00805f9b34fb"     # Battery characteristic

def signal_handler(sig, frame):
    print("Exiting...")
    stop_event.set()
    os._exit(0)

def thread_SendingData():
    global previousMeasurement
    global measurements
    global identicalCounter
    path = os.path.dirname(os.path.abspath(__file__))
    
    while not stop_event.is_set():
        try:
            if not measurements:
                time.sleep(1)
                continue
                
            mea = measurements.popleft()
            if (mea == previousMeasurement and identicalCounter < args.skipidentical):
                print("Measurements are identical don't send data\n")
                identicalCounter += 1
                continue
                
            identicalCounter = 0
            fmt = "sensorname,temperature,humidity,voltage"
            params = args.name + " " + str(mea.temperature) + " " + str(mea.humidity) + " " + str(mea.voltage)
            
            if (args.TwoPointCalibration or args.offset):
                fmt += ",humidityCalibrated"
                params += " " + str(mea.calibratedHumidity)
                
            if (args.battery):
                fmt += ",batteryLevel"
                params += " " + str(mea.battery)
                
            params += " " + str(mea.timestamp)
            fmt += ",timestamp"
            
            cmd = path + "/" + args.callback + " " + fmt + " " + params
            print(cmd)
            ret = os.system(cmd)
            
            if (ret != 0):
                measurements.appendleft(mea)
                print("Data couldn't be sent to Callback, retrying...")
                time.sleep(5)
            else:
                previousMeasurement = Measurement(
                    mea.temperature,
                    mea.humidity,
                    mea.voltage,
                    mea.calibratedHumidity,
                    mea.battery,
                    0
                )
                time.sleep(60)

        except IndexError:
            time.sleep(1)
        except Exception as e:
            print(e)
            print(traceback.format_exc())

mode = "round"

def notification_handler(sender, data):
    global measurements
    try:
        measurement = Measurement(0, 0, 0, 0, 0, 0)
        measurement.timestamp = int(time.time())
        
        if args.influxdb == 1:
            measurement.timestamp = int((time.time() // 10) * 10)
            
        # Parse the data from the notification
        temp = struct.unpack('<h', data[0:2])[0] / 100
        
        if args.round:
            if args.debounce:
                global mode
                temp *= 10
                intpart = math.floor(temp)
                fracpart = round(temp - intpart, 1)
                
                if fracpart >= 0.7:
                    mode = "ceil"
                elif fracpart <= 0.2:
                    mode = "trunc"
                    
                if mode == "trunc":
                    temp = math.trunc(temp)
                elif mode == "ceil":
                    temp = math.ceil(temp)
                else:
                    temp = round(temp, 0)
                temp /= 10.
            else:
                temp = round(temp, 1)
                
        humidity = data[2]
        voltage = struct.unpack('<H', data[3:5])[0] / 1000.
        
        measurement.temperature = temp
        measurement.humidity = humidity
        measurement.voltage = voltage
        
        if args.battery:
            batteryLevel = min(int(round((voltage - 2.1), 2) * 100), 100)
            measurement.battery = batteryLevel

        if args.offset:
            humidityCalibrated = humidity + args.offset
            print("Calibrated humidity: " + str(humidityCalibrated))
            measurement.calibratedHumidity = humidityCalibrated

        if args.TwoPointCalibration:
            offset1 = args.offset1
            offset2 = args.offset2
            p1y = args.calpoint1
            p2y = args.calpoint2
            p1x = p1y - offset1
            p2x = p2y - offset2
            m = (p1y - p2y) * 1.0 / (p1x - p2x)
            b = p2y - m * p2x
            humidityCalibrated = m * humidity + b
            
            if (humidityCalibrated > 100):
                humidityCalibrated = 100
            elif (humidityCalibrated < 0):
                humidityCalibrated = 0
                
            humidityCalibrated = int(round(humidityCalibrated, 0))
            print("Calibrated humidity: " + str(humidityCalibrated))
            measurement.calibratedHumidity = humidityCalibrated

        measurements.append(measurement)
        print(f"Temperature: {temp}°C, Humidity: {humidity}%, Voltage: {voltage}V")

    except Exception as e:
        print("Error processing notification:")
        print(e)
        print(traceback.format_exc())

async def connect_to_device(address):
    global connected
    global unconnectedTime
    global client
    
    print(f"Trying to connect to {address}")
    
    try:
        # # Find the device by address
        # device = await BleakScanner.find_device_by_address(address)
        # if not device:
        #     print(f"Device with address {address} not found")
        #     return False
            
        # Connect to the device
        client = BleakClient(address)
        await client.connect()
        print(f"Connected to {address}")
        
        # Enable notifications
        await client.start_notify(TEMP_HUMID_CHAR_UUID, notification_handler)
        
        # Enable data reporting
        await client.write_gatt_char(TEMP_HUMID_CHAR_UUID, bytearray([0x01, 0x00]), response=True)
        await client.write_gatt_char(BATTERY_CHAR_UUID, bytearray([0xf4, 0x01, 0x00]), response=True)
        
        connected = True
        unconnectedTime = None
        return True
        
    except Exception as e:
        print(f"Connection failed: {e}")
        connected = False
        unconnectedTime = int(time.time())
        return False

async def connect(address):
    print(f"Trying to connect to {address}")
    async with BleakClient(address, timeout=30.0) as client:
        print("Connected and retrieving services")
        # Enable notifications
        await client.start_notify(TEMP_HUMID_CHAR_UUID, notification_handler)
        
        # Enable data reporting
        await client.write_gatt_char(TEMP_HUMID_CHAR_UUID, bytearray([0x01, 0x00]), response=True)
        await client.write_gatt_char(BATTERY_CHAR_UUID, bytearray([0xf4, 0x01, 0x00]), response=True)

async def main_loop():
    global connected
    global connectionLostCounter
    global unconnectedTime
    global client
    
    cnt = 0
    
    while not stop_event.is_set():
        try:
            if not connected:
                success = await connect_to_device(args.device)
                if not success:
                    connectionLostCounter += 1
                    if args.unreachable_count != 0 and connectionLostCounter >= args.unreachable_count:
                        print("Maximum number of unsuccessful connections reached, exiting")
                        stop_event.set()
                        return
                    await asyncio.sleep(5)
                    continue
            
            # Keep the connection alive
            await asyncio.sleep(1)
            
            # Check if client is still connected
            if client and client.is_connected:
                cnt += 1
                if args.count is not None and cnt >= args.count:
                    print(f"{args.count} measurements collected. Exiting in a moment.")
                    await client.disconnect()
                    stop_event.set()
                    return
            else:
                print("Connection lost")
                connected = False
                unconnectedTime = int(time.time())
                connectionLostCounter += 1
                
                if args.unreachable_count != 0 and connectionLostCounter >= args.unreachable_count:
                    print("Maximum number of unsuccessful connections reached, exiting")
                    stop_event.set()
                    return
                    
                await asyncio.sleep(5)
                
        except Exception as e:
            print(f"Error in main loop: {e}")
            print(traceback.format_exc())
            connected = False
            unconnectedTime = int(time.time())
            connectionLostCounter += 1
            
            if args.unreachable_count != 0 and connectionLostCounter >= args.unreachable_count:
                print("Maximum number of unsuccessful connections reached, exiting")
                stop_event.set()
                return
                
            await asyncio.sleep(5)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(allow_abbrev=False)
    parser.add_argument("--device", "-d", help="Set the device MAC-Address in format AA:BB:CC:DD:EE:FF", metavar='AA:BB:CC:DD:EE:FF')
    parser.add_argument("--battery", "-b", help="Get estimated battery level", metavar='', type=int, nargs='?', const=1)
    parser.add_argument("--count", "-c", help="Read/Receive N measurements and then exit script", metavar='N', type=int)
    parser.add_argument("--unreachable-count", "-urc", help="Exit after N unsuccessful connection tries", metavar='N', type=int, default=0)

    rounding = parser.add_argument_group("Rounding and debouncing")
    rounding.add_argument("--round", "-r", help="Round temperature to one decimal place", action='store_true')
    rounding.add_argument("--debounce", "-deb", help="Enable this option to get more stable temperature values, requires -r option", action='store_true')

    offsetgroup = parser.add_argument_group("Offset calibration mode")
    offsetgroup.add_argument("--offset", "-o", help="Enter an offset to the reported humidity value", type=int)

    complexCalibrationGroup = parser.add_argument_group("2 Point Calibration")
    complexCalibrationGroup.add_argument("--TwoPointCalibration", "-2p", help="Use complex calibration mode. All arguments below are required", action='store_true')
    complexCalibrationGroup.add_argument("--calpoint1", "-p1", help="Enter the first calibration point", type=int)
    complexCalibrationGroup.add_argument("--offset1", "-o1", help="Enter the offset for the first calibration point", type=int)
    complexCalibrationGroup.add_argument("--calpoint2", "-p2", help="Enter the second calibration point", type=int)
    complexCalibrationGroup.add_argument("--offset2", "-o2", help="Enter the offset for the second calibration point", type=int)

    callbackgroup = parser.add_argument_group("Callback related functions")
    callbackgroup.add_argument("--callback", "-call", help="Pass the path to a program/script that will be called on each new measurement")
    callbackgroup.add_argument("--name", "-n", help="Give this sensor a name reported to the callback script")
    callbackgroup.add_argument("--skipidentical", "-skip", help="N consecutive identical measurements won't be reported to callbackfunction", metavar='N', type=int, default=0)
    callbackgroup.add_argument("--influxdb", "-infl", help="Optimize for writing data to influxdb,1 timestamp optimization, 2 integer optimization", metavar='N', type=int, default=0)

    args = parser.parse_args()
    
    if args.device:
        if re.match("[0-9a-fA-F]{2}([:]?)[0-9a-fA-F]{2}(\\1[0-9a-fA-F]{2}){4}$", args.device):
            address = args.device
        else:
            print("Please specify device MAC-Address in format AA:BB:CC:DD:EE:FF")
            os._exit(1)
    else:
        parser.print_help()
        os._exit(1)

    if args.TwoPointCalibration:
        if not (args.calpoint1 and args.offset1 and args.calpoint2 and args.offset2):
            print("In 2 Point calibration you have to enter 4 points")
            os._exit(1)
        elif args.offset:
            print("Offset calibration and 2 Point calibration can't be used together")
            os._exit(1)
            
    if not args.name:
        args.name = args.device

    # Set up signal handler
    signal.signal(signal.SIGINT, signal_handler)

    # Start data sending thread if callback is provided
    if args.callback:
        dataThread = threading.Thread(target=thread_SendingData)
        dataThread.daemon = True
        dataThread.start()

    # Configure logging
    logging.basicConfig(level=logging.ERROR)

    # Run the main async loop
    try:
        asyncio.run(main_loop())
    except KeyboardInterrupt:
        print("Keyboard interrupt received, exiting...")
    finally:
        stop_event.set()
        print("Script terminated")