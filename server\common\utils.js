const axios = require('axios')

module.exports = {
  async verifyCaptcha(token) {
    console.log(token)
    var res = await axios.post(
      `https://www.google.com/recaptcha/api/siteverify?secret=6LfoHqoUAAAAANktvh4ppo-OH2d7VnVqlwg8dCXC&response=${token}`,
      {},
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
        },
      }
    )

    return res.data
  },
  async getExifData(img_path) {
    return new Promise((resolve, reject) => {
      var ExifImage = require('exif').ExifImage
      new ExifImage({image: img_path}, function(error, exifData) {
        if (error) {
          console.log('Error: ' + error.message)
          resolve(null)
        } else {
          if (exifData && exifData.gps && exifData.gps.GPSLatitude) {
            let lat = exifData.gps.GPSLatitude
            exifData.gps.lat = module.exports.convertGPS_DMS2DD(
              lat[0],
              lat[1],
              lat[2],
              exifData.gps.GPSLatitudeRef
            )
            let lon = exifData.gps.GPSLongitude
            exifData.gps.lon = module.exports.convertGPS_DMS2DD(
              lon[0],
              lon[1],
              lon[2],
              exifData.gps.GPSLongitudeRef
            )
          }
          resolve(exifData) // Do something with your data!
        }
      })
    })
  },
  convertGPS_DMS2DD(days, minutes, seconds, direction) {
    direction.toUpperCase()
    var dd = days + minutes / 60 + seconds / (60 * 60)
    //alert(dd);
    if (direction == 'S' || direction == 'W') {
      dd = dd * -1
    } // Don't do anything for N or E
    return dd
  },
}
