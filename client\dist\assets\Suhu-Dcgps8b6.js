import{n as o,a as s,l as r,_ as n,g as l,m as d,o as p,V as c,p as m,f as u,e as h,k as f}from"./index-DYIZrBBo.js";import{A as _}from"./AlatDet-ecQII-bj.js";const g={data:()=>({keyword:""}),props:{statusId:String,rebind:Number,addButton:Boolean,filters:Object},computed:{dbparams(){return{Keyword:this.keyword||""}}},methods:{ItemClick(a){this.$emit("item-click",a)},DaftarBaru(){this.$emit("item-click",{AlatId:"-"})}}};var w=function(){var t=this,e=t._self._c;return e("div",{staticClass:"sidepane"},[e("div",{staticStyle:{padding:"10px",display:"flex"}},[e(s,{staticClass:"searchbar",attrs:{type:"text",value:t.keyword,placeholder:"Cari ..",width:"100%",rightIcon:"mdi-magnify"},on:{"update:value":function(i){t.keyword=i}}})],1),e("div",{staticStyle:{height:"calc(100% - 47px)"}},[e(r,{attrs:{dbref:"ISO.SelRuang",dbparams:t.dbparams,height:t.addButton?"calc(100% - 60px)":"100%",rebind:t.rebind},on:{itemClick:t.ItemClick},scopedSlots:t._u([{key:"default",fn:function({row:i}){return[e("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"5px 10px"}},[e("div",{staticStyle:{color:"gray",float:"right"}},[t._v(" "+t._s(i.TglPencatatan)+" ")]),e("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(i.NamaRuang)+" ")]),e("div",{staticStyle:{color:"gray",display:"flex"}},[e("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis",width:"100%",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(i.Hasil)+" ")])])])]}}])}),e("div",{staticStyle:{padding:"10px","border-top":"1px solid #ddd"}},[e(n,{staticStyle:{width:"calc(100% - 10px)"},attrs:{outlined:"",color:"primary"},on:{click:t.DaftarBaru}},[e(l,{attrs:{left:""}},[t._v("mdi-plus")]),t._v(" TAMBAH ALAT ")],1)],1)],1)])},v=[],x=o(g,w,v,!1,null,null);const y=x.exports,b={components:{VueQrcode:c,SidePane:y,AlatDet:_},data:()=>({datagrid:[],dbparams:{AlatId:""},lembarKerja:[],loading:!1,showDet:!1,forms:{},rebind:1,rebindSidebar:1,masalah:{show:!1}}),watch:{showDet(a){a||this.rebind++}},computed:{...p({user:"getUser"}),showSubmitButton(){return this.lembarKerja.filter(t=>!t.Alasan&&!t.ApprovedBy).length>0},qrUrl(){return window.location.origin+"/daftar-alat/"+this.forms.AlatId},disabled(){return this.user.RolePositionID==12&&this.user.UserID!=this.forms.PIC}},methods:{...d(["setPageFocused"]),async ItemClick(a){this.setPageFocused(!0),this.Populate(a.RuangId)},async Populate(a){this.dbparams={AlatId:a};var t=await this.$api.call("ISO.SelRuangHarian",{RuangId:a});t.data.length?this.forms=t.data[0]:this.forms={}},async Save(){let a=await this.$api.call("ISO.SavRuangHarian",this.forms);a.success&&(this.Populate(a.data[0].RuangId),this.rebindSidebar++)},PrintQR(){let a=document.getElementById("qralat").src,t=window.open("about:blank","_new");t.document.open(),t.document.write(["<html>","   <head>",'<link rel="preconnect" href="https://fonts.googleapis.com">','<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>','<link href="https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">',"   </head>",'   <body onload="window.print()" onafterprint="window.close()">','       <img src="'+a+'" width="200px"/><br />','       <div style="width:200px; text-align:center; font-family:raleway; margin-top:-8px;">',this.forms.NamaAlat,"</div>","   </body>","</html>"].join("")),t.document.close()}}};var S=function(){var t=this,e=t._self._c;return e(m,{attrs:{sidebar:"true"}},[e("SidePane",{attrs:{addButton:!0,rebind:t.rebindSidebar},on:{"item-click":t.ItemClick}}),e("div",{class:t.$api.isMobile()?"":"form-inline",staticStyle:{"max-width":"1000px",padding:"0 20px","padding-bottom":"50px"},style:{"margin-left":t.$api.isMobile()?"10px":"0"}},[t.forms.AlatId?e("div",{staticStyle:{float:"right","font-family":"raleway",background:"white","margin-top":"-20px",width:"210px"},attrs:{id:"dvRightBox"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:!t.$api.isMobile(),expression:"!$api.isMobile()"}],staticStyle:{padding:"10px 20px","text-align":"center"}},[e("VueQrcode",{style:{width:"150px",height:"150px"},attrs:{id:"qralat",value:t.qrUrl}})],1),e(n,{directives:[{name:"show",rawName:"v-show",value:!t.$api.isMobile(),expression:"!$api.isMobile()"}],staticStyle:{"border-top":"1px solid #e3e3e3","text-align":"center",width:"210px"},attrs:{text:"",small:"",color:"primary"},on:{click:t.PrintQR}},[t._v(" PRINT BARCODE ")])],1):t._e(),e("div",{style:{display:t.$api.isMobile()?"":"flex"}},[e("div",{staticClass:"alat-img",style:{width:t.$api.isMobile()?"100%":""}},[e(u,{attrs:{value:t.forms.Foto},on:{"update:value":function(i){return t.$set(t.forms,"Foto",i)}}})],1),e("div",{class:t.$api.isMobile()?"":"form-inline",staticStyle:{"max-width":"450px",width:"100%"},style:{"margin-left":t.$api.isMobile()?"0":"20px","margin-top":t.$api.isMobile()?"0":"20px"}},[e(s,{attrs:{type:"text",label:"Nama Ruangan",value:t.forms.NamaRuang,width:"300px"},on:{"update:value":function(i){return t.$set(t.forms,"NamaRuang",i)}}}),e(s,{attrs:{type:"text",label:"Suhu",value:t.forms.Suhu,width:"300px"},on:{"update:value":function(i){return t.$set(t.forms,"Suhu",i)}}}),e(s,{attrs:{type:"text",label:"Kelembapan",value:t.forms.Kelembapan,width:"300px"},on:{"update:value":function(i){return t.$set(t.forms,"Kelembapan",i)}}}),e(h,{attrs:{label:"Tgl Pencatatan"}},[e("div",{staticStyle:{"font-size":"14px",color:"gray",padding:"6px"}},[t._v(" "+t._s(t._f("format")(t.forms.CreatedAt,"DD MMMM YYYY HH:mm"))+" ")])])],1)]),e("br"),e("br"),t.disabled?t._e():e("div",{staticStyle:{display:"flex","margin-bottom":"30px"}},[e(n,{staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:t.Save}},[t._v(" SIMPAN ")]),e(f)],1)]),e("alat-det",{attrs:{show:t.showDet,alatId:t.forms.AlatId},on:{"update:show":function(i){t.showDet=i}}})],1)},I=[],k=o(b,S,I,!1,null,null);const A=k.exports;export{A as default};
