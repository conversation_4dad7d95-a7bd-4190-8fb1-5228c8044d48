import{n as p,S as c,a as o,b as u,c as m,d,e as i,f,_ as s,g as l,h as v,k as h}from"./index-DYIZrBBo.js";const _={components:{SidePane:c},data:()=>({datagrid:[],rebindSidebar:0,rebindUpload:0,dbparams:{PermohonanID:0},forms:{},pay:{show:!1},paramUji:{show:!1},showReport:!1,jenis:[{val:"A",txt:"Mutu Air & Lingkungan"},{val:"B",txt:"Bahan Bangunan"},{val:"Ba",txt:"Aspal"},{val:"T",txt:"Tan<PERSON> (Geoteknik)"}],satker:[{val:"1",txt:"APBN"},{val:"2",txt:"APBD I - BMCK"},{val:"3",txt:"APBD I - NON BMCK"},{val:"4",txt:"APBD II"},{val:"5",txt:"Swasta"},{val:"6",txt:"Perorangan"},{val:"7",txt:"Lainnya"}]}),methods:{async ItemClick(n){this.Populate(n.PermohonanID)},async Download(n){this.$api.download(this.$api.url+n,!0)},async Populate(n){this.rebindUpload++,this.dbparams={PermohonanID:n};var a=await this.$api.call("UJI.SelPermohonan",{PermohonanID:n});a.data.length?this.forms=a.data[0]:this.forms={}},async Save(){var n=await this.$api.call("UJI.SavPermohonan",{...this.forms,XmlPermohonanDet:this.datagrid});n.success&&this.rebindSidebar++},CloseReport(){},AddParameter(){}}};var g=function(){var a=this,t=a._self._c;return t("div",{staticStyle:{display:"flex"}},[t("SidePane",{attrs:{statusId:",2,3,4,",rebind:a.rebindSidebar,addButton:!0},on:{"item-click":a.ItemClick}}),t("div",{directives:[{name:"show",rawName:"v-show",value:a.forms.PermohonanID,expression:"forms.PermohonanID"}],staticClass:"form-inline",staticStyle:{padding:"20px 20px",width:"calc(100vw - 500px)",overflow:"auto",height:"calc(100vh - 66px)"}},[t("div",{directives:[{name:"show",rawName:"v-show",value:a.forms.BayarStatus==2,expression:"forms.BayarStatus == 2"}],staticClass:"dvWarn"},[a._v(" "+a._s(a.forms.CatatanKhusus)+" ")]),t("div",{staticStyle:{float:"right","font-family":"raleway",background:"white","margin-top":"-20px"},attrs:{id:"dvRightBox"}},[t("div",{staticStyle:{padding:"10px 20px","text-align":"center"}},[t("div",{staticStyle:{"font-size":"xx-large"}},[a._v(a._s(a.forms.NoPengujian))]),t("div",[a._v(a._s(a.forms.ShareCode))])]),t("div",{directives:[{name:"show",rawName:"v-show",value:a.forms.StatusID>=4&&a.forms.BayarStatus==1,expression:"forms.StatusID >= 4 && forms.BayarStatus == 1"}],staticStyle:{padding:"10px 20px","border-top":"1px solid #e3e3e3",background:"#4caf50",color:"white","text-align":"center"}},[t("span",[a._v("Sudah Dibayarkan")])]),t("div",{directives:[{name:"show",rawName:"v-show",value:a.forms.StatusID==9,expression:"forms.StatusID == 9"}],staticStyle:{padding:"10px 20px","border-top":"1px solid #e3e3e3",background:"#8bc34a",color:"white","text-align":"center"}},[t("span",[a._v("Sudah Diserahkan")])])]),t(o,{attrs:{type:"text",label:"Nama Pelanggan",value:a.forms.Nama,width:"300px"},on:{"update:value":function(e){return a.$set(a.forms,"Nama",e)}}}),t(u,{attrs:{label:"Alamat",value:a.forms.Alamat,width:"300px"},on:{"update:value":function(e){return a.$set(a.forms,"Alamat",e)}}}),a._v(" "),t(o,{attrs:{type:"text",label:"No. Ponsel",value:a.forms.Phone,width:"300px"},on:{"update:value":function(e){return a.$set(a.forms,"Phone",e)}}}),t(m,{attrs:{items:a.jenis,label:"Nama/Jenis Contoh",value:a.forms.JenisID,width:"300px"},on:{"update:value":function(e){return a.$set(a.forms,"JenisID",e)}}}),t(d,{staticStyle:{"padding-top":"4px"},attrs:{label:"Tanggal Masuk",value:a.forms.TglMasuk},on:{"update:value":function(e){return a.$set(a.forms,"TglMasuk",e)}}}),t(u,{attrs:{label:"Kegiatan/Paket Pekerjaan",value:a.forms.NamaKegiatan,width:"300px"},on:{"update:value":function(e){return a.$set(a.forms,"NamaKegiatan",e)}}}),a._v(" "),t(i,{attrs:{label:"Sumber/SatKer"}},[t(m,{attrs:{items:a.satker,value:a.forms.SumberDana},on:{"update:value":function(e){return a.$set(a.forms,"SumberDana",e)}}}),t(o,{directives:[{name:"show",rawName:"v-show",value:a.forms.SumberDana<"5",expression:"forms.SumberDana < '5'"}],staticStyle:{"margin-left":"5px"},attrs:{type:"text",placeholder:"Satker / Kab / Kota",value:a.forms.SatKer},on:{"update:value":function(e){return a.$set(a.forms,"SatKer",e)}}})],1),t(i,{attrs:{label:"Surat Permohonan/Tgl."}},[t(o,{attrs:{type:"text",value:a.forms.SuratNo,placeholder:"No. Surat"},on:{"update:value":function(e){return a.$set(a.forms,"SuratNo",e)}}}),t(d,{staticStyle:{"margin-left":"5px"},attrs:{value:a.forms.SuratTgl},on:{"update:value":function(e){return a.$set(a.forms,"SuratTgl",e)}}})],1),t(i,{attrs:{label:"Surat Permohonan"}},[t(f,{key:a.rebindUpload,attrs:{value:a.forms.SuratUrl},on:{"update:value":function(e){return a.$set(a.forms,"SuratUrl",e)}},scopedSlots:a._u([{key:"default",fn:function({opener:e,fileName:r}){return[t(s,{directives:[{name:"show",rawName:"v-show",value:r,expression:"fileName"}],staticStyle:{"margin-right":"8px"},attrs:{small:"",text:"",outlined:""},on:{click:function(w){return a.Download(a.forms.SuratUrl)}}},[a._v(a._s(r||a.forms.SuratUrl))]),t(s,{attrs:{small:""},on:{click:e}},[t(l,[a._v("mdi-upload")])],1)]}}])})],1),t(v,{attrs:{datagrid:a.datagrid,dbref:"UJI_SelPermohonanDet",dbparams:a.dbparams,disabled:!0,columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Nama Contoh",value:"NamaContoh"},{name:"Jml",value:"JmlContoh"},{name:"Metode",value:"Metode"},{name:"Harga",value:"Harga",class:"align-right"},{name:"",value:"Delete"}]},on:{"update:datagrid":function(e){a.datagrid=e}},scopedSlots:a._u([{key:"row-NamaContoh",fn:function({row:e}){return[t(o,{attrs:{value:e.NamaContoh,placeholder:"(asal/ukuran contoh)"},on:{"update:value":function(r){return a.$set(e,"NamaContoh",r)}}})]}},{key:"row-JmlContoh",fn:function({row:e}){return[t(o,{attrs:{type:"number",value:e.JmlContoh,placeholder:"Jml",width:"60px"},on:{"update:value":function(r){return a.$set(e,"JmlContoh",r)}}})]}},{key:"row-Harga",fn:function({row:e}){return[a._v(" "+a._s(a._f("format")(e.Harga*e.JmlContoh))+" ")]}},{key:"row-Delete",fn:function({idx:e}){return[t(l,{attrs:{color:"error"},on:{click:function(r){return a.DelParameter(e)}}},[a._v("mdi-trash-can")])]}},{key:"footer",fn:function(){return[t("tr",[t("td",{attrs:{colspan:"4"}},[t(s,{attrs:{text:"",small:""},on:{click:function(e){a.paramUji.show=!0}}},[t(l,{attrs:{left:""}},[a._v("mdi-plus-circle")]),a._v(" Tambah Parameter Uji ")],1)],1),t("td",{staticClass:"align-right",staticStyle:{"font-weight":"bold",padding:"8px 12px"}},[a._v(" "+a._s(a._f("format")(a.datagrid.reduce((e,r)=>e+r.Harga*r.JmlContoh,0)))+" ")]),t("td")])]},proxy:!0}])}),t("br"),t("br"),t("div",{staticStyle:{display:"flex"}},[t(s,{attrs:{color:"primary"},on:{click:function(e){return a.OpenReport(e)}}},[t(l,[a._v("mdi-printer")])],1),a.forms.StatusID==1?t(s,{staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:a.Save}},[a._v(" SIMPAN ")]):a._e(),t(h),a.forms.StatusID==1?t(s,{staticStyle:{"margin-left":"5px"},attrs:{color:"success"},on:{click:function(e){a.pay.show=!0}}},[a._v(" BAYAR ")]):a._e()],1)],1),t("ReportPopup",{directives:[{name:"show",rawName:"v-show",value:a.showReport,expression:"showReport"},{name:"click-outside",rawName:"v-click-outside",value:a.CloseReport,expression:"CloseReport"}],attrs:{reportUrl:a.reportUrl}}),t("ParameterUji",{attrs:{forms:a.paramUji,jenisId:a.forms.JenisID},on:{submit:a.AddParameter}})],1)},x=[],S=p(_,g,x,!1,null,null);const b=S.exports;export{b as default};
