!function(){function t(t,r){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,r){if(t){if("string"==typeof t)return e(t,r);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?e(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){n&&(t=n);var a=0,o=function(){};return{s:o,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,u=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return u=t.done,t},e:function(t){l=!0,i=t},f:function(){try{u||null==n.return||n.return()}finally{if(l)throw i}}}}function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",i=r.toStringTag||"@@toStringTag";function u(r,n,o,i){var u=n&&n.prototype instanceof s?n:s,c=Object.create(u.prototype);return a(c,"_invoke",function(r,n,a){var o,i,u,s=0,c=a||[],f=!1,p={p:0,n:0,v:t,a:m,f:m.bind(t,4),d:function(e,r){return o=e,i=0,u=t,p.n=r,l}};function m(r,n){for(i=r,u=n,e=0;!f&&s&&!a&&e<c.length;e++){var a,o=c[e],m=p.p,d=o[2];r>3?(a=d===n)&&(u=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=t):o[0]<=m&&((a=r<2&&m<o[1])?(i=0,p.v=n,p.n=o[1]):m<d&&(a=r<3||o[0]>n||n>d)&&(o[4]=r,o[5]=n,p.n=d,i=0))}if(a||r>1)return l;throw f=!0,n}return function(a,c,d){if(s>1)throw TypeError("Generator is already running");for(f&&1===c&&m(c,d),i=c,u=d;(e=i<2?t:u)||!f;){o||(i?i<3?(i>1&&(p.n=-1),m(i,u)):p.n=u:p.v=u);try{if(s=2,o){if(i||(a="next"),e=o[a]){if(!(e=e.call(o,u)))throw TypeError("iterator result is not an object");if(!e.done)return e;u=e.value,i<2&&(i=0)}else 1===i&&(e=o.return)&&e.call(o),i<2&&(u=TypeError("The iterator does not provide a '"+a+"' method"),i=1);o=t}else if((e=(f=p.n<0)?u:r.call(n,p))!==l)break}catch(e){o=t,i=1,u=e}finally{s=1}}return{value:e,done:f}}}(r,o,i),!0),c}var l={};function s(){}function c(){}function f(){}e=Object.getPrototypeOf;var p=[][o]?e(e([][o]())):(a(e={},o,function(){return this}),e),m=f.prototype=s.prototype=Object.create(p);function d(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,a(t,i,"GeneratorFunction")),t.prototype=Object.create(m),t}return c.prototype=f,a(m,"constructor",f),a(f,"constructor",c),c.displayName="GeneratorFunction",a(f,i,"GeneratorFunction"),a(m),a(m,i,"Generator"),a(m,o,function(){return this}),a(m,"toString",function(){return"[object Generator]"}),(n=function(){return{w:u,m:d}})()}function a(t,e,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}a=function(t,e,r,n){function i(e,r){a(t,e,function(t){return this._invoke(e,r,t)})}e?o?o(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[e]=r:(i("next",0),i("throw",1),i("return",2))},a(t,e,r,n)}function o(t,e,r,n,a,o,i){try{var u=t[o](i),l=u.value}catch(t){return void r(t)}u.done?e(l):Promise.resolve(l).then(n,a)}function i(t){return function(){var e=this,r=arguments;return new Promise(function(n,a){var i=t.apply(e,r);function u(t){o(i,n,a,u,l,"next",t)}function l(t){o(i,n,a,u,l,"throw",t)}u(void 0)})}}System.register(["./index-legacy-BUdDePUl.js"],function(e,a){"use strict";var o,u,l,s,c,f,p;return{setters:[function(t){o=t.n,u=t.S,l=t.k,s=t._,c=t.h,f=t.g,p=t.f}],execute:function(){var a=document.createElement("style");a.textContent=".table-cekprogress table{min-width:750px;width:100%}.table-cekprogress table .is-masalah{padding-left:10px;position:absolute;margin-top:-9px;max-width:400px;overflow:hidden;text-overflow:ellipsis}.table-cekprogress table .is-masalah.is-done{color:gray}\n/*$vite$:1*/",document.head.appendChild(a);e("default",o({components:{SidePane:u},data:function(){return{datagrid:[],dbparams:{PermohonanID:0},lembarKerja:[],loading:!1,forms:{PermohonanID:0,ParameterID:0,Masalah:""},rebind:1,rebindSidebar:1,masalah:{show:!1}}},computed:{showSubmitButton:function(){return this.lembarKerja.filter(function(t){return!t.Alasan&&!t.ApprovedBy}).length>0&&"submitted"!=this.forms.LkStatus}},methods:{SideFilter:function(t){return!t.LkStatus},ItemClick:function(t){var e=this;return i(n().m(function r(){return n().w(function(r){for(;;)switch(r.n){case 0:e.Populate(t.PermohonanID);case 1:return r.a(2)}},r)}))()},OpenPDF:function(t){window.open(this.$api.url+t.replace(/\.\w{3,4}$/,"pdf"),"_blank")},Populate:function(t){var e=this;return i(n().m(function r(){var a;return n().w(function(r){for(;;)switch(r.n){case 0:return e.dbparams={PermohonanID:t},r.n=1,e.$api.call("UJI.SelPermohonan",{PermohonanID:t});case 1:(a=r.v).data.length?e.forms=a.data[0]:e.forms={},e.PopulateLK(t);case 2:return r.a(2)}},r)}))()},PopulateLK:function(t){var e=this;return i(n().m(function r(){var a;return n().w(function(r){for(;;)switch(r.n){case 0:return e.lembarKerja=[],r.n=1,e.$api.call("UJI.SelLembarKerja",{PermohonanID:t});case 1:a=r.v,e.lembarKerja=a.data;case 2:return r.a(2)}},r)}))()},DeleteLK:function(t){var e=this;return i(n().m(function r(){return n().w(function(r){for(;;)switch(r.n){case 0:if(confirm("Anda yakin menghapus Lembar Kerja ini?")){r.n=1;break}return r.a(2);case 1:return r.n=2,e.$api.call("UJI.DelLembarKerja",{LembarKerjaID:t});case 2:r.v.success&&e.PopulateLK(e.forms.PermohonanID);case 3:return r.a(2)}},r)}))()},MenuPDF:function(t,e){var r=this;return i(n().m(function a(){return n().w(function(n){for(;;)switch(n.n){case 0:if("Tampilkan"!=t){n.n=1;break}window.open(r.$api.url+e.LkUrl.replace(/\.\w{3,4}$/,".pdf"),"_blank"),n.n=3;break;case 1:if("Perbaiki"!=t){n.n=3;break}return r.$api.notify("Silahkan tunggu .."),r.loading=!0,n.n=2,r.$api.get("/reports/uji/refine-lk/"+e.LembarKerjaID);case 2:window.open(r.$api.url+e.LkUrl.replace(/\.\w{3,4}$/,".pdf"),"_blank"),r.loading=!1;case 3:return n.a(2)}},a)}))()},Open:function(t,e){window.open(this.$api.url+"/reports/uji/lembarkerja/"+e,"_blank")},SubmitLK:function(){var t=this;return i(n().m(function e(){return n().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("UJI_UpdLkStatus",{PermohonanID:t.forms.PermohonanID,Status:"submitted"});case 1:t.forms.LkStatus="submitted",t.rebindSidebar++;case 2:return e.a(2)}},e)}))()},fileUploaded:function(e,a){var o=this;return i(n().m(function i(){var u,l,s;return n().w(function(n){for(;;)switch(n.n){case 0:return o.$api.notify("Upload Sukses"),n.n=1,o.$api.call("UJI_SavLembarKerja",{PermohonanID:o.forms.PermohonanID,XmlRawUrl:e.infos,RevisedID:"object"==r(a)?null:a});case 1:if(!n.v.success){n.n=3;break}return o.forms.LkStatus=null,n.n=2,o.PopulateLK(o.forms.PermohonanID);case 2:u=t(o.lembarKerja);try{for(u.s();!(l=u.n()).done;)s=l.value,o.$api.get("/reports/uji/convert-lk/"+s.LembarKerjaID)}catch(i){u.e(i)}finally{u.f()}case 3:return n.a(2)}},i)}))()}}},function(){var t=this,e=t._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{attrs:{statusId:",5,",rebind:t.rebindSidebar},on:{"item-click":t.ItemClick}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.Nama,expression:"forms.Nama"}],staticStyle:{overflow:"auto",height:"calc(100vh - 66px)"}},[e("div",{staticStyle:{background:"#039ae4",color:"white",padding:"15px 15px 10px 15px",display:"flex"}},[e("div",{staticStyle:{display:"flex",width:"calc(100% - 200px)"}},[e("div",{staticStyle:{"max-width":"450px",overflow:"hidden","text-wrap":"nowrap","text-overflow":"ellipsis"}},[t._v(" "+t._s(t.forms.Nama)+" ")]),e("div",{staticStyle:{background:"white",color:"#039ae4","margin-left":"10px",padding:"5px 8px","border-radius":"5px","font-size":"small",position:"relative",top:"-3px"}},[t._v(" "+t._s(t.forms.NoPengujian)+" ")])]),e(l),6==t.forms.StatusID?e(s,{staticStyle:{color:"white !important",position:"relative",top:"-2px"},attrs:{small:"",disabled:""}},[t._v(" SELESAI ")]):t._e()],1),e(c,{staticClass:"table-cekprogress",attrs:{datagrid:t.datagrid,dbref:"UJI_SelCekProgress",doRebind:t.rebind,dbparams:t.dbparams,disabled:!0,columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Jml",value:"JmlContoh",width:"50px"},{name:"Keterangan",value:"Keterangan"},{name:"Waktu",value:"Waktu"}]},on:{"update:datagrid":function(e){t.datagrid=e}},scopedSlots:t._u([{key:"row-NamaParameter",fn:function(r){var n=r.row;return[e("div",{class:{"is-masalah":2==n.Ordr,"is-done":n.HasilStatus}},[e("div",[t._v(t._s(n.NamaParameter))]),e("div",[t._v(t._s(1==n.Ordr?n.Metode:""))])])]}},{key:"row-Keterangan",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.Keterangan)+" ")]}},{key:"row-Waktu",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.Waktu)+" Hari ")]}}])}),t._l(t.lembarKerja,function(r,n){return e("div",{key:n,staticStyle:{"font-size":"12px",background:"#f3f3f3",display:"flex","margin-bottom":"1px",padding:"8px"}},[r.ApprovedBy?t._e():e(s,{directives:[{name:"tooltip",rawName:"v-tooltip",value:"Hapus Lembar Kerja",expression:"'Hapus Lembar Kerja'"}],staticStyle:{"margin-top":"4px"},attrs:{"x-small":"",text:"",color:"error"},on:{click:function(e){return t.DeleteLK(r.LembarKerjaID)}}},[e(f,[t._v(" mdi-close ")])],1),e(s,{attrs:{small:"",text:"",color:"primary"},on:{click:function(e){return t.Open(r.LkUrl,r.LembarKerjaID)}}},[t._v(" "+t._s(r.Nama)+" ")]),e(l),r.Alasan?e(s,{directives:[{name:"tooltip",rawName:"v-tooltip",value:r.Alasan,expression:"lk.Alasan"}],staticStyle:{margin:"4px"},attrs:{"x-small":"",text:"",outlined:"",color:"error"}},[t._v(" DITOLAK ")]):t._e(),r.Alasan?e(p,{attrs:{accept:".pdf"},on:{change:function(e){return t.fileUploaded(e,r.LembarKerjaID)}},scopedSlots:t._u([{key:"default",fn:function(r){var n=r.opener;return[e(s,{staticStyle:{margin:"4px"},attrs:{"x-small":"",text:"",outlined:"",color:"primary"},on:{click:n}},[t._v(" REVISI ")])]}}],null,!0)}):r.ApprovedBy?e(s,{staticStyle:{margin:"4px"},attrs:{"x-small":"",text:"",outlined:""}},[t._v(" SUDAH DISETUJUI ")]):t._e()],1)}),e("div",{staticStyle:{display:"flex"}},[e(p,{staticStyle:{"margin-top":"8px"},attrs:{multiple:!0,accept:".pdf"},on:{change:t.fileUploaded},scopedSlots:t._u([{key:"default",fn:function(r){var n=r.opener;return[e(s,{attrs:{color:"primary"},on:{click:n}},[t._v(" UPLOAD LEMBAR KERJA ")])]}}])}),t.showSubmitButton?e(s,{staticStyle:{margin:"8px 0 0 8px"},attrs:{color:"success"},on:{click:t.SubmitLK}},[t._v(" SUBMIT ")]):t._e()],1)],2)],1)},[],!1,null,null).exports)}}})}();
