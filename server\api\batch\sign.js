
const moment = require("moment");
const fs = require("fs");
const JSZip = require("jszip");
const shell = require("shelljs");
const {degrees, PDFDocument, rgb, StandardFonts} = require('pdf-lib');

const exportPDF = (file, outdir = '.') => {
  let libre = "/opt/libreoffice7.0/program/soffice" 
  if(!fs.existsSync(libre)) {
    libre = `"C:\\Program Files\\LibreOffice\\program\\soffice.exe"`
  }
  const out = shell.exec(
    `${libre} --headless --convert-to pdf "${file}" --outdir ${outdir}`,
    {
      silent: true
    }
  )
  if (out.code !== 0) {
    console.error(`ERR: ${out.stderr}`)
  }
}


const sign = async (data) => {
  const zip = new JSZip();
  let filedata = fs.readFileSync("test.xlsx")
  zip.loadAsync(filedata).then(async (z) => {
    for(let [filename, file] of Object.entries(zip.files)) {
      // TODO Your code goes here
      if (filename.match(/.xml$/)) {
        let str = await z.file(filename).async("string")
        for(let key in data) {
          str = str.replace(new RegExp(`{d.${key}}`, "g"), data[key])
        }
        z.file(filename, str)
      }
    }

    let blob = await z.generateAsync({type:"nodebuffer"})
    fs.writeFileSync("test-out.xlsx", blob, "binary")
    exportPDF("test-out.xlsx")
  })
}

sign({
  SerialNumber: "XXX/YYY/ZZZ",
  TanggalTTD: "30 Juni 2023"
})