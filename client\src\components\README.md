# Vue.js Components Manual

This manual provides comprehensive documentation for all Vue.js components in the `client/src/components` directory. Each component is documented with its purpose, props, events, usage examples, and key features.

## Table of Contents

1. [Main Components](#main-components)
   - [DropDown](#dropdown)
   - [Modal](#modal)
   - [NavBar](#navbar)
   - [Page](#page)
   - [Panel](#panel)
   - [Prompt](#prompt)
   - [ReportViewer](#reportviewer)
2. [Chart Components](#chart-components)
   - [Bar](#bar)
   - [Doughnut](#doughnut)
   - [Line](#line)
3. [Form Components](#form-components)
   - [Input](#input)
   - [Select](#select)
   - [TextArea](#textarea)
   - [Checkbox](#checkbox)
   - [Radio](#radio)
   - [DatePicker](#datepicker)
   - [TimePicker](#timepicker)
   - [Label](#label)
   - [List](#list)
   - [Map](#map)
   - [MenuButton](#menubutton)
   - [Popover](#popover)
   - [Radio2](#radio2)
   - [Search](#search)
4. [Grid Components](#grid-components)
   - [Grid](#grid)
   - [Grid2](#grid2)
5. [Other Components](#other-components)
   - [Notification](#notification)
   - [Report](#report)
   - [Uploader](#uploader)
   - [Users](#users)

## Main Components

### DropDown

A dropdown menu component built with Vuetify's v-menu.

**Purpose**: Provides a dropdown selection interface with customizable menu items.

**Props**:
- `items` (Array): Array of menu items with properties:
  - `text` (String): Display text for the item
  - `icon` (String, optional): Icon name to display
  - `click` (Function): Click handler function

**Usage**:
```vue
<DropDown :items="menuItems">
  <v-btn>Click Me</v-btn>
</DropDown>
```

**Features**:
- Uses Vuetify's v-menu for positioning
- Supports icons for menu items
- Click handlers for each item
- Dense list styling

### Modal

A comprehensive modal dialog component with error handling and mobile support.

**Purpose**: Displays modal dialogs with customizable content, actions, and error states.

**Props**:
- `show` (Boolean): Controls modal visibility
- `title` (String): Modal title
- `error` (String): Error message to display
- `errorAction` (String): Error action message
- `warning` (String): Warning message to display
- `loading` (Boolean, default: false): Shows loading state
- `cancelText` (String, default: 'Batal'): Cancel button text
- `submitText` (String, default: 'Simpan'): Submit button text
- `showActions` (Boolean, default: true): Whether to show action buttons

**Events**:
- `submit`: Emitted when submit button is clicked
- `cancel`: Emitted when modal is cancelled
- `error-action-no`: Emitted when error action "No" is clicked
- `error-action-yes`: Emitted when error action "Yes" is clicked

**Features**:
- Mobile-responsive (fullscreen on mobile)
- Error and warning state handling
- Loading state with blur effect
- Customizable action buttons
- Indonesian language support

### NavBar

A responsive navigation bar component with menu system and notifications.

**Purpose**: Provides main navigation interface with menu items and user notifications.

**Computed Properties**:
- `menu`: Menu items from Vuex store
- `user`: Current user from Vuex store

**Features**:
- Responsive design with mobile hamburger menu
- Dropdown menus for sub-items
- Notification badge with message count
- User display name
- Integration with Vuex for state management
- Auto-refreshing messages every 6 minutes

**Menu Structure**:
- Supports hierarchical menu items
- JavaScript URL evaluation support
- Router navigation for standard URLs

### Page

A page layout component with header and content areas.

**Purpose**: Provides consistent page layout structure with optional sidebar support.

**Props**:
- `title` (String): Page title
- `sidebar` (Boolean): Whether to show sidebar layout

**Computed Properties**:
- `isPageFocused`: Focus state from Vuex store
- `classId`: CSS class based on title

**Features**:
- Responsive header with title and toolbar slot
- Sidebar-aware content layout
- Mobile-responsive design
- CSS class generation from title

### Panel

A data panel component that fetches and displays data from API.

**Purpose**: Automatically fetches and displays data from database references.

**Props**:
- `dbref` (String): Database reference for API call
- `dbparams` (Object|Array): Parameters for the API call
- `autobind` (Boolean, default: true): Whether to auto-populate on creation

**Data Properties**:
- `data` (Array): Fetched data array
- `first` (Object): First item from data array
- `oldparams` (String): JSON string of previous parameters

**Features**:
- Automatic data fetching on creation and parameter changes
- Reactive data updates
- Provides scoped slot with data and first item
- API integration with post method

### Prompt

A prompt dialog component for user input.

**Purpose**: Shows modal prompts for text input with customizable types.

**Computed Properties**:
- `pData`: Prompt data from Vuex store
- `show`: Prompt visibility from Vuex store

**Features**:
- Supports textarea and input types
- Integration with Vuex for state management
- Customizable prompt configuration
- Save handler with value processing

### ReportViewer

A report viewing component with download capabilities.

**Purpose**: Displays reports in iframe with Excel download functionality.

**Props**:
- `url` (String): Report URL
- `params` (Object): Report parameters
- `show` (Boolean): Visibility control
- `options` (Object): Report generation options
- `filetype` (String, default: 'html'): File type (html/xlsx)

**Features**:
- Iframe-based report display
- Excel download functionality
- Loading states
- Error handling with toast notifications
- Support for both HTML and Excel formats

## Chart Components

### Bar

A bar chart component using vue-chartjs.

**Purpose**: Renders bar charts with gradient backgrounds.

**Props**:
- `options`: Chart configuration options

**Features**:
- Extends vue-chartjs Bar component
- Automatic gradient creation for datasets
- Reactive prop mixin for data updates
- Custom gradient generation based on base colors

**Gradient Creation**:
- Converts hex colors to RGB
- Creates linear gradient from solid to transparent
- Applies gradient to each dataset

### Doughnut

A doughnut chart component using vue-chartjs.

**Purpose**: Renders doughnut/pie charts.

**Props**:
- `options`: Chart configuration options

**Features**:
- Extends vue-chartjs Doughnut component
- Reactive prop mixin for data updates
- Simple rendering without custom gradients

### Line

A line chart component using vue-chartjs.

**Purpose**: Renders line charts.

**Props**:
- `options`: Chart configuration options

**Features**:
- Extends vue-chartjs Line component
- Reactive prop mixin for data updates
- Standard line chart rendering

## Form Components

### Input

A comprehensive input component with masking and validation.

**Purpose**: Provides enhanced input functionality with number masking and validation.

**Props**:
- `value` (String|Number): Input value
- `width` (String): Input width
- `leftIcon` (String): Left icon name
- `rightIcon` (String): Right icon name
- `postfix` (String): Postfix text
- `format` (String): Number format
- `type` (String, default: 'text'): Input type

**Features**:
- Number formatting with thousand separators
- Icon support (left and right)
- Postfix display
- Search input with debounced updates
- Focus and blur handling
- Required field validation
- Mobile-friendly masking

### Select

A dropdown select component with API integration.

**Purpose**: Provides dropdown selection with database integration.

**Props**:
- `value` (String|Number|Object): Selected value
- `dbref` (String): Database reference for options
- `dbparams` (Object): API parameters
- `items` (Array): Static options array
- `placeholder` (String): Placeholder text
- `valueKey` (String): Property name for value
- `textKey` (String): Property name for display text
- `disabled` (Boolean): Disabled state
- `required` (Boolean): Required validation
- `width` (String, default: '180px'): Component width
- `height` (String, default: '200px'): Dropdown height
- `valueAsObject` (Boolean, default: false): Return full object

**Features**:
- API integration for dynamic options
- Static option support
- Customizable value and text keys
- Required field validation
- Auto-selection for single items
- Object return mode

## Grid Components

### Grid

A responsive grid component with mobile support.

**Purpose**: Displays data in table format with responsive design.

**Props**:
- `id` (String): Unique identifier
- `datagrid` (Array): Data array
- `columns` (Array): Column definitions
- `filter` (Function): Filter function
- `disabled` (Boolean): Disabled state
- `editMode` (Array): Editable columns
- `dbref` (String): Database reference
- `dbparams` (Object): API parameters
- `height` (String): Grid height
- `width` (String): Grid width
- `groupBy` (String): Grouping column
- `onEdit` (Function): Edit callback
- `selectedRow` (Number): Selected row index
- `autopaging` (Boolean, default: true): Auto pagination
- `preHead` (Boolean, default: false): Pre-header row
- `doPrint` (Number): Print trigger
- `doRebind` (Number): Rebind trigger
- `hasChild` (Boolean): Has child rows

**Features**:
- Responsive design (desktop/mobile modes)
- Component-based rendering
- Slot support for customization
- Scoped slot support
- Mobile-specific grid implementation

## Other Components

### Notification

A simple notification component.

**Purpose**: Provides notification display functionality.

**Features**:
- Vuetify text field integration
- Dense styling support
- Required field validation

### Report

A report parameter component.

**Purpose**: Handles report parameters and configuration.

### Uploader

A file upload component with QR code support.

**Purpose**: Handles file uploads with mobile scanning capability.

**Props**:
- `value` (String): Uploaded file URL
- `convert` (String): Conversion format
- `width` (String, default: '150px'): Component width
- `height` (String, default: '150px'): Component height
- `multiple` (Boolean, default: false): Multiple file support

**Features**:
- File upload with preview
- QR code generation for mobile upload
- WebSocket integration for mobile uploads
- File type icons
- Context menu with options
- Loading states

### Users

A user-specific grid component.

**Purpose**: Displays user data with specialized functionality.

## Component Architecture Patterns

### Common Patterns Used:

1. **Vuex Integration**: Many components use Vuex for state management
2. **API Integration**: Components frequently use `$api` for data fetching
3. **Responsive Design**: Mobile-first approach with Vuetify
4. **Slot-based Composition**: Extensive use of slots for customization
5. **Event-driven Communication**: Props and events for parent-child communication
6. **Indonesian Language Support**: Default text in Indonesian language

### Styling Approach:

- **SCSS Modules**: Each component has its own scoped styles
- **Vuetify Integration**: Heavy use of Vuetify components
- **Responsive Breakpoints**: Mobile-specific styling with media queries
- **CSS Variables**: Dynamic styling based on component state

### State Management:

- **Reactive Props**: Components use reactive props for data binding
- **Computed Properties**: Complex state calculations
- **Watchers**: Reactive updates based on prop changes
- **Event Emission**: Parent notification through custom events

This manual provides a comprehensive reference for all components in the project. Each component is designed to be reusable and follows consistent patterns for maintainability and extensibility.