!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function c(t,o,i,a){var c=o&&o.prototype instanceof u?o:u,d=Object.create(c.prototype);return e(d,"_invoke",function(t,e,o){var i,a,c,u=0,d=o||[],l=!1,p={p:0,n:0,v:n,a:f,f:f.bind(n,4),d:function(t,e){return i=t,a=0,c=n,p.n=e,s}};function f(t,e){for(a=t,c=e,r=0;!l&&u&&!o&&r<d.length;r++){var o,i=d[r],f=p.p,h=i[2];t>3?(o=h===e)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=n):i[0]<=f&&((o=t<2&&f<i[1])?(a=0,p.v=e,p.n=i[1]):f<h&&(o=t<3||i[0]>e||e>h)&&(i[4]=t,i[5]=e,p.n=h,a=0))}if(o||t>1)return s;throw l=!0,e}return function(o,d,h){if(u>1)throw TypeError("Generator is already running");for(l&&1===d&&f(d,h),a=d,c=h;(r=a<2?n:c)||!l;){i||(a?a<3?(a>1&&(p.n=-1),f(a,c)):p.n=c:p.v=c);try{if(u=2,i){if(a||(o="next"),r=i[o]){if(!(r=r.call(i,c)))throw TypeError("iterator result is not an object");if(!r.done)return r;c=r.value,a<2&&(a=0)}else 1===a&&(r=i.return)&&r.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=n}else if((r=(l=p.n<0)?c:t.call(e,p))!==s)break}catch(r){i=n,a=1,c=r}finally{u=1}}return{value:r,done:l}}}(t,i,a),!0),d}var s={};function u(){}function d(){}function l(){}r=Object.getPrototypeOf;var p=[][i]?r(r([][i]())):(e(r={},i,function(){return this}),r),f=l.prototype=u.prototype=Object.create(p);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,l):(t.__proto__=l,e(t,a,"GeneratorFunction")),t.prototype=Object.create(f),t}return d.prototype=l,e(f,"constructor",l),e(l,"constructor",d),d.displayName="GeneratorFunction",e(l,a,"GeneratorFunction"),e(f),e(f,a,"Generator"),e(f,i,function(){return this}),e(f,"toString",function(){return"[object Generator]"}),(t=function(){return{w:c,m:h}})()}function e(t,n,r,o){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}e=function(t,n,r,o){function a(n,r){e(t,n,function(t){return this._invoke(n,r,t)})}n?i?i(t,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):t[n]=r:(a("next",0),a("throw",1),a("return",2))},e(t,n,r,o)}function n(t,e,n,r,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}System.register(["./index-legacy-BUdDePUl.js"],function(e,r){"use strict";var o,i,a,c,s,u;return{setters:[function(t){o=t.n,i=t.i,a=t.a,c=t.g,s=t.l,u=t._}],execute:function(){var r=document.createElement("style");r.textContent=".sidebar-container{position:absolute;top:0;right:-300px;height:100%;display:flex;transition:right .3s ease-in-out}.sidebar-container:hover{right:0}.sidebar-opener{position:absolute;top:12px;left:-50px;width:50px;height:40px;padding:5px;background:#fff;border-radius:20px 0 0 20px;box-shadow:-2px 0 5px rgba(0,0,0,.1);cursor:pointer;z-index:2;display:flex;align-items:center;justify-content:center}.sidebar-content{width:300px;background:#fff;height:100%;padding:10px;box-shadow:-2px 0 5px rgba(0,0,0,.1);z-index:1}.sidebar-content .searchbar{margin-bottom:0}.sidebar-content .searchbar input{background:transparent!important;border-bottom:0!important}.sidebar-content .--item{border-radius:10px;margin-right:10px}.sidebar-content .--item .no-uji{font-size:10px;padding:3px 5px 0;background:#ddd;border-radius:5px;color:#333;margin-right:5px}.sidebar-content .--item.selected .no-uji{background:#fff}\n/*$vite$:1*/",document.head.appendChild(r);var d={data:function(){return{xshow:!1,forms:{},loading:!1}},props:{show:Boolean},watch:{show:function(t){this.xshow=t,t||(this.forms={})},xshow:function(t){this.$emit("update:show",t)}},methods:{Save:function(){var e,r=this;return(e=t().m(function e(){return t().w(function(t){for(;;)switch(t.n){case 0:return r.loading=!0,t.n=1,r.$api.call("ISO_SavPenjaminMutu",r.forms);case 1:t.v.success&&(r.$emit("save"),r.$emit("update:show",!1)),r.loading=!1;case 2:return t.a(2)}},e)}),function(){var t=this,r=arguments;return new Promise(function(o,i){var a=e.apply(t,r);function c(t){n(a,o,i,c,s,"next",t)}function s(t){n(a,o,i,c,s,"throw",t)}c(void 0)})})()}}},l=o(d,function(){var t=this,e=t._self._c;return e(i,{attrs:{title:"Tambah Dokumen",show:t.xshow,loading:t.loading,width:"800px"},on:{"update:show":function(e){t.xshow=e},submit:t.Save}},[e("div",{staticStyle:{width:"400px"}},[e(a,{attrs:{type:"text",value:t.forms.DocName,label:"Nama Dokumen",width:"400px"},on:{"update:value":function(e){return t.$set(t.forms,"DocName",e)}}}),e(a,{attrs:{type:"text",value:t.forms.DocUrl,label:"Url",width:"400px"},on:{"update:value":function(e){return t.$set(t.forms,"DocUrl",e)}}})],1)])},[],!1,null,null).exports;e("default",o({components:{AddDoc:l},data:function(){return{keyword:"",rebind:1,showAddDoc:!1,currentUrl:""}},methods:{ItemClick:function(t){this.currentUrl=t.DocUrl},ItemsChange:function(t){!this.currentUrl&&t.length&&(this.currentUrl=t[0].DocUrl)}}},function(){var t=this,e=t._self._c;return e("div",{staticStyle:{height:"calc(100vh - 68px)",overflow:"hidden",position:"relative"}},[e("iframe",{staticStyle:{"margin-top":"-55px",height:"calc(100% + 55px)"},attrs:{src:t.currentUrl,width:"100%"}}),e("div",{staticClass:"sidebar-container"},[e("div",{staticClass:"sidebar-opener"},[e(c,{attrs:{large:""}},[t._v("mdi-chevron-left")])],1),e("div",{staticClass:"sidebar-content"},[e("div",{staticStyle:{padding:"10px",display:"flex"}},[e(a,{staticClass:"searchbar",staticStyle:{"margin-left":"-8px"},attrs:{type:"text",value:t.keyword,placeholder:"Cari ..",width:"266px",rightIcon:"mdi-magnify"},on:{"update:value":function(e){t.keyword=e}}})],1),e(s,{attrs:{dbref:"ISO_SelPenjaminMutu",dbparams:{Keyword:t.keyword},height:"calc(100% - 85px)",rebind:t.rebind,selectOnLoad:!0},on:{itemClick:t.ItemClick,change:t.ItemsChange},scopedSlots:t._u([{key:"default",fn:function(n){var r=n.row;return[e("div",{staticStyle:{padding:"10px"}},[e("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(r.DocName)+" ")]),e("div",{staticStyle:{color:"gray",display:"flex","font-size":"13px"}},[t._v(" "+t._s(t._f("format")(r.CreatedAt))+" ")])])]}}])}),e(u,{staticStyle:{width:"100%"},attrs:{outlined:"",color:"primary"},on:{click:function(e){t.showAddDoc=!0}}},[e(c,{attrs:{left:""}},[t._v("mdi-plus")]),t._v(" TAMBAH BARU ")],1)],1)]),e("AddDoc",{attrs:{show:t.showAddDoc},on:{"update:show":function(e){t.showAddDoc=e},save:function(e){t.rebind++}}})],1)},[],!1,null,null).exports)}}})}();
