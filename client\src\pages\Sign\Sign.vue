<template>
  <div style="display: flex">
    <SidePane @item-click="ShowSertifikat" />
    <div v-if="rawUrl" class="right-pane">
      <iframe
        :src="rawUrl"
        style="width: 100%; height: 100%"
        frameborder="0"
      ></iframe>
      <div style="position: fixed; bottom: 20px; right: 40px; display: flex">
        <MenuButton
          :menu="['Download', 'Upload']"
          @item-click="MenuManual($event)"
        >
          <template v-slot="{ on }">
            <v-btn>
              <v-icon v-on="on"> mdi-dots-vertical </v-icon>
            </v-btn>
          </template>
        </MenuButton>
        <v-btn
          class="close-right-pa ne"
          @click="rawUrl = ''"
          style="margin-right: 8px"
          v-show="!loading"
        >
          BATAL
        </v-btn>
        <v-btn
          color="primary"
          @click="showPassphrase = true"
          :disabled="loading"
          v-show="!signedUrl"
        >
          TANDA TANGANI
        </v-btn>
        <input
          :key="resetFile"
          type="file"
          ref="uploader"
          @change="uploadFile"
          v-show="false"
        />
      </div>
    </div>
    <Modal title="Passphrase:" :show.sync="showPassphrase" @submit="Sign">
      <Input type="password" :value.sync="passphrase" />
    </Modal>
  </div>
</template>
<script>
import SidePane from './SidePane.vue'
export default {
  components: {
    SidePane,
  },
  data: () => ({
    rawUrl: '',
    signedUrl: '',
    resetFile: 0,
    forms: {},
    passphrase: '',
    showPassphrase: false,
    loading: false,
  }),
  methods: {
    ShowSertifikat(val) {
      this.forms = val
      this.rawUrl = this.$api.url + val.RawUrl.replace(/(xlsx|docx)$/, 'pdf')
      this.signedUrl = val.SignedUrl
      if (val.SignedUrl) {
        this.rawUrl =
          this.$api.url + val.SignedUrl.replace(/(xlsx|docx)$/, 'pdf')
      }
    },
    async MenuManual(txt) {
      if (txt === 'Download') {
        let d = await this.$api.post('/reports/uji/manual-download', {
          PermohonanID: this.forms.PermohonanID,
          FilePath: this.forms.RawUrl,
        })
        window.open(this.$api.url + '/reports/get/' + d.RawUrl, '_blank')
      } else if (txt === 'Upload') {
        this.$refs.uploader.click()
      }
    },
    async uploadFile(event) {
      if (!confirm('Anda yakin TTD manual?')) {
        return
      }
      var form = new FormData()
      form.append('file', event.target.files[0])
      let res = await this.$api.upload(form)
      if (res.success) {
        let SignedUrl = res.data
        res = await this.$api.post('/reports/uji/manual-sign', {
          PermohonanID: this.forms.PermohonanID,
          FilePath: this.forms.RawUrl,
          SignedUrl,
        })
        if (res.success) {
          this.$api.notify(res.message, 'success')
          this.rawUrl = ''
        } else {
          this.$api.notify(res.message, 'error')
          this.passphrase = ''
        }
      }
      this.resetFile++
    },
    async Sign() {
      this.showPassphrase = false
      // if (!this.passphrase) {
      //   // this.passphrase = prompt('Masukkan Passphrase anda:')
      //   this.showPassphrase = true
      // }
      this.loading = true
      let res = await this.$api.post('/reports/uji/sign', {
        PermohonanID: this.forms.PermohonanID,
        Passphrase: this.passphrase,
        FilePath: this.rawUrl.replace(this.$api.url, ''),
      })
      this.loading = false
      if (res.success) {
        this.$api.notify(res.message, 'success')
        this.rawUrl = ''
      } else {
        this.$api.notify(res.message, 'error')
        this.passphrase = ''
      }
    },
  },
}
</script>
<style lang="scss"></style>
