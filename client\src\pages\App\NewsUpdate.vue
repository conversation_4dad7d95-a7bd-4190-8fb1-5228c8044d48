<template>
  <Modal
    title="Info Pembaruan"
    :show.sync="xshow"
    width="800px"
    cancelText="OK"
    submitText=""
  >
    <v-card-text style="width: 800px">
      <b>Halaman Laporan:</b><br />
      Halaman Laporan BNBA untuk user Kabupaten/Kota
      <br /><br />
      <b>Sync e-RTLH:</b><br />
      Sudah Aktif <PERSON>
      <br /><br />
      <b>Halaman Database:</b><br />
      Penambahan Kolom IDBDT, dapat diaktifkan dengan menekan tombol Pengaturan
      Kolom
      <br /><br />
    </v-card-text>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    forms: {},
    isChecked: [],
  }),
  props: {
    show: Boolean,
    columns: Array,
  },
  watch: {
    show(val) {
      this.xshow = val
      if (val) this.populate()
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    populate() {
      this.isChecked = this.columns.map((c) => !c.hide)
    },
    async Save() {
      // let ret = await this.api.call('', {})
      // if (ret.success)
      this.$emit('update:show', false)
      this.$emit('onUpdate', this.isChecked)
    },
  },
}
</script>
<style lang="scss"></style>
