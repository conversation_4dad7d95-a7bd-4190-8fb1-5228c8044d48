!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,n,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function u(e,o,i,a){var u=o&&o.prototype instanceof s?o:s,f=Object.create(u.prototype);return r(f,"_invoke",function(e,r,o){var i,a,u,s=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:v,f:v.bind(t,4),d:function(e,r){return i=e,a=0,u=t,p.n=r,c}};function v(e,r){for(a=e,u=r,n=0;!l&&s&&!o&&n<f.length;n++){var o,i=f[n],v=p.p,y=i[2];e>3?(o=y===r)&&(u=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=v&&((o=e<2&&v<i[1])?(a=0,p.v=r,p.n=i[1]):v<y&&(o=e<3||i[0]>r||r>y)&&(i[4]=e,i[5]=r,p.n=y,a=0))}if(o||e>1)return c;throw l=!0,r}return function(o,f,y){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&v(f,y),a=f,u=y;(n=a<2?t:u)||!l;){i||(a?a<3?(a>1&&(p.n=-1),v(a,u)):p.n=u:p.v=u);try{if(s=2,i){if(a||(o="next"),n=i[o]){if(!(n=n.call(i,u)))throw TypeError("iterator result is not an object");if(!n.done)return n;u=n.value,a<2&&(a=0)}else 1===a&&(n=i.return)&&n.call(i),a<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((n=(l=p.n<0)?u:e.call(r,p))!==c)break}catch(n){i=t,a=1,u=n}finally{s=1}}return{value:n,done:l}}}(e,i,a),!0),f}var c={};function s(){}function f(){}function l(){}n=Object.getPrototypeOf;var p=[][i]?n(n([][i]())):(r(n={},i,function(){return this}),n),v=l.prototype=s.prototype=Object.create(p);function y(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,l):(t.__proto__=l,r(t,a,"GeneratorFunction")),t.prototype=Object.create(v),t}return f.prototype=l,r(v,"constructor",l),r(l,"constructor",f),f.displayName="GeneratorFunction",r(l,a,"GeneratorFunction"),r(v),r(v,a,"Generator"),r(v,i,function(){return this}),r(v,"toString",function(){return"[object Generator]"}),(e=function(){return{w:u,m:y}})()}function r(t,e,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}r=function(t,e,n,o){function a(e,n){r(t,e,function(t){return this._invoke(e,n,t)})}e?i?i(t,e,{value:n,enumerable:!o,configurable:!o,writable:!o}):t[e]=n:(a("next",0),a("throw",1),a("return",2))},r(t,e,n,o)}function n(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){a(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function a(e,r,n){return(r=function(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,r||"default");if("object"!=t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==t(r)?r:r+""}(r))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}System.register(["./index-legacy-BUdDePUl.js"],function(t,r){"use strict";var o,a,u,c,s,f,l;return{setters:[function(t){o=t.n,a=t.o,u=t.i,c=t.c,s=t.d,f=t.b,l=t.f}],execute:function(){var r=document.createElement("style");r.textContent=".modal-data-detail-alat .imgbox{margin:auto}\n/*$vite$:1*/",document.head.appendChild(r);var p={data:function(){return{xshow:!1,forms:{}}},props:{show:Boolean,alatId:[String,Number],aktifitas:String,tanggal:[String,Date]},computed:i({},a({user:"getUser"})),watch:{show:function(t){this.xshow=t,t&&(this.aktifitas&&(this.forms.Aktifitas=this.aktifitas),this.tanggal&&(this.forms.Tanggal=this.tanggal))},xshow:function(t){this.$emit("update:show",t)}},methods:{Save:function(){var t,r=this;return(t=e().m(function t(){return e().w(function(t){for(;;)switch(t.n){case 0:return t.n=1,r.$api.call("PML.SavAlatActivity",i({AlatId:r.alatId},r.forms));case 1:t.v.success&&r.$emit("update:show",!1);case 2:return t.a(2)}},t)}),function(){var e=this,r=arguments;return new Promise(function(o,i){var a=t.apply(e,r);function u(t){n(a,o,i,u,c,"next",t)}function c(t){n(a,o,i,u,c,"throw",t)}u(void 0)})})()}}};t("A",o(p,function(){var t=this,e=t._self._c;return e(u,{attrs:{title:"Data Detail Alat",show:t.xshow,width:"450px"},on:{"update:show":function(e){t.xshow=e},submit:t.Save}},[e("div",{staticClass:"rounded-forms",staticStyle:{"min-width":"300px"}},[e(c,{staticStyle:{width:"100%"},attrs:{width:"100%",label:"Jenis Aktifitas",items:[{val:"kalibrasi",txt:"Kalibrasi"},{val:"pemeliharaan",txt:"Pemeliharaan"},{val:"kerusakan",txt:"Kerusakan"}],value:t.forms.Aktifitas},on:{"update:value":function(e){return t.$set(t.forms,"Aktifitas",e)}}}),e(s,{staticStyle:{width:"100%"},attrs:{width:"100%",label:"Tanggal",value:t.forms.Tanggal},on:{"update:value":function(e){return t.$set(t.forms,"Tanggal",e)}}}),e(f,{staticStyle:{width:"100%"},attrs:{width:"100%",label:"Keterangan",value:t.forms.Keterangan},on:{"update:value":function(e){return t.$set(t.forms,"Keterangan",e)}}}),t._v(" "),e(l,{attrs:{label:"Bukti",value:t.forms.Bukti},on:{"update:value":function(e){return t.$set(t.forms,"Bukti",e)}}})],1)])},[],!1,null,null).exports)}}})}();
