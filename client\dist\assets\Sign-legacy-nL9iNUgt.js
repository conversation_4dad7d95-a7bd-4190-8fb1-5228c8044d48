!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var r,n,a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",o=a.toStringTag||"@@toStringTag";function s(t,a,i,o){var s=a&&a.prototype instanceof l?a:l,u=Object.create(s.prototype);return e(u,"_invoke",function(t,e,a){var i,o,s,l=0,u=a||[],d=!1,p={p:0,n:0,v:r,a:f,f:f.bind(r,4),d:function(t,e){return i=t,o=0,s=r,p.n=e,c}};function f(t,e){for(o=t,s=e,n=0;!d&&l&&!a&&n<u.length;n++){var a,i=u[n],f=p.p,h=i[2];t>3?(a=h===e)&&(s=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=r):i[0]<=f&&((a=t<2&&f<i[1])?(o=0,p.v=e,p.n=i[1]):f<h&&(a=t<3||i[0]>e||e>h)&&(i[4]=t,i[5]=e,p.n=h,o=0))}if(a||t>1)return c;throw d=!0,e}return function(a,u,h){if(l>1)throw TypeError("Generator is already running");for(d&&1===u&&f(u,h),o=u,s=h;(n=o<2?r:s)||!d;){i||(o?o<3?(o>1&&(p.n=-1),f(o,s)):p.n=s:p.v=s);try{if(l=2,i){if(o||(a="next"),n=i[a]){if(!(n=n.call(i,s)))throw TypeError("iterator result is not an object");if(!n.done)return n;s=n.value,o<2&&(o=0)}else 1===o&&(n=i.return)&&n.call(i),o<2&&(s=TypeError("The iterator does not provide a '"+a+"' method"),o=1);i=r}else if((n=(d=p.n<0)?s:t.call(e,p))!==c)break}catch(n){i=r,o=1,s=n}finally{l=1}}return{value:n,done:d}}}(t,i,o),!0),u}var c={};function l(){}function u(){}function d(){}n=Object.getPrototypeOf;var p=[][i]?n(n([][i]())):(e(n={},i,function(){return this}),n),f=d.prototype=l.prototype=Object.create(p);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,e(t,o,"GeneratorFunction")),t.prototype=Object.create(f),t}return u.prototype=d,e(f,"constructor",d),e(d,"constructor",u),u.displayName="GeneratorFunction",e(d,o,"GeneratorFunction"),e(f),e(f,o,"Generator"),e(f,i,function(){return this}),e(f,"toString",function(){return"[object Generator]"}),(t=function(){return{w:s,m:h}})()}function e(t,r,n,a){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}e=function(t,r,n,a){function o(r,n){e(t,r,function(t){return this._invoke(r,n,t)})}r?i?i(t,r,{value:n,enumerable:!a,configurable:!a,writable:!a}):t[r]=n:(o("next",0),o("throw",1),o("return",2))},e(t,r,n,a)}function r(t,e,r,n,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function n(t){return function(){var e=this,n=arguments;return new Promise(function(a,i){var o=t.apply(e,n);function s(t){r(o,a,i,s,c,"next",t)}function c(t){r(o,a,i,s,c,"throw",t)}s(void 0)})}}System.register(["./index-legacy-BUdDePUl.js"],function(e,r){"use strict";var a,i,o,s,c,l,u;return{setters:[function(t){a=t.n,i=t.a,o=t.l,s=t._,c=t.g,l=t.w,u=t.i}],execute:function(){var r=document.createElement("style");r.textContent=".sidepane{padding:0!important;width:300px;border-right:1px solid #ddd;height:calc(100vh - 64px);overflow:hidden}.sidepane .searchbar{margin-bottom:0}.sidepane .searchbar input{background:transparent!important;border-bottom:0!important}\n/*$vite$:1*/",document.head.appendChild(r);var d={data:function(){return{keyword:""}},props:{statusId:String,rebind:Number,addButton:Boolean},computed:{dbparams:function(){return{IsApproved:1,Keyword:this.keyword||""}}},methods:{ItemClick:function(t){this.$emit("item-click",t)},DaftarBaru:function(){this.$emit("item-click",{PengujianID:0})}}},p=a(d,function(){var t=this,e=t._self._c;return e("div",{staticClass:"sidepane"},[e("div",{staticStyle:{padding:"10px",display:"flex"}},[e(i,{staticClass:"searchbar",attrs:{type:"text",value:t.keyword,placeholder:"Cari ..",width:"270px",rightIcon:"mdi-magnify"},on:{"update:value":function(e){t.keyword=e}}})],1),e("div",{staticStyle:{height:"calc(100% - 47px)"}},[e(o,{attrs:{dbref:"UJI_SelTandaTanganList",dbparams:t.dbparams,height:t.addButton?"calc(100% - 60px)":"100%",rebind:t.rebind,selectOnLoad:!0},on:{itemClick:t.ItemClick},scopedSlots:t._u([{key:"default",fn:function(r){var n=r.row;return[e("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"0 10px"}},[e("div",{staticStyle:{color:"gray",float:"right"}},[t._v(" "+t._s(t._f("format")(n.TglMasuk))+" ")]),e("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(n.NamaPelanggan)+" ")]),e("div",{staticStyle:{color:"gray",float:"right"}},[t._v(" "+t._s(n.StatusID>=8&&1!=n.BayarStatus?"BELUM BAYAR":n.SignedUrl?"SUDAH DITANDATANGAN":n.StatusName)+" ")]),e("div",{staticStyle:{color:"gray",display:"flex"}},[e("span",{staticStyle:{"font-size":"10px",padding:"3px 5px 0 5px",background:"#ddd","border-radius":"5px",color:"#333","margin-right":"5px"}},[t._v(" "+t._s(n.NoPengujian)+" ")]),e("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis",width:"160px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(n.NamaLK||"-")+" ")])])])]}}])}),t.addButton?e("div",{staticStyle:{padding:"10px","border-top":"1px solid #ddd"}},[e(s,{staticStyle:{width:"calc(100% - 10px)"},attrs:{outlined:"",color:"primary"},on:{click:t.DaftarBaru}},[e(c,{attrs:{left:""}},[t._v("mdi-plus")]),t._v(" DAFTAR BARU ")],1)],1):t._e()],1)])},[],!1,null,null).exports;e("default",a({components:{SidePane:p},data:function(){return{rawUrl:"",signedUrl:"",resetFile:0,forms:{},passphrase:"",showPassphrase:!1,loading:!1}},methods:{ShowSertifikat:function(t){this.forms=t,this.rawUrl=this.$api.url+t.RawUrl.replace(/(xlsx|docx)$/,"pdf"),this.signedUrl=t.SignedUrl,t.SignedUrl&&(this.rawUrl=this.$api.url+t.SignedUrl.replace(/(xlsx|docx)$/,"pdf"))},MenuManual:function(e){var r=this;return n(t().m(function n(){var a;return t().w(function(t){for(;;)switch(t.n){case 0:if("Download"!==e){t.n=2;break}return t.n=1,r.$api.post("/reports/uji/manual-download",{PermohonanID:r.forms.PermohonanID,FilePath:r.forms.RawUrl});case 1:a=t.v,window.open(r.$api.url+"/reports/get/"+a.RawUrl,"_blank"),t.n=3;break;case 2:"Upload"===e&&r.$refs.uploader.click();case 3:return t.a(2)}},n)}))()},uploadFile:function(e){var r=this;return n(t().m(function n(){var a,i,o;return t().w(function(t){for(;;)switch(t.n){case 0:if(confirm("Anda yakin TTD manual?")){t.n=1;break}return t.a(2);case 1:return(a=new FormData).append("file",e.target.files[0]),t.n=2,r.$api.upload(a);case 2:if(!(i=t.v).success){t.n=4;break}return o=i.data,t.n=3,r.$api.post("/reports/uji/manual-sign",{PermohonanID:r.forms.PermohonanID,FilePath:r.forms.RawUrl,SignedUrl:o});case 3:(i=t.v).success?(r.$api.notify(i.message,"success"),r.rawUrl=""):(r.$api.notify(i.message,"error"),r.passphrase="");case 4:r.resetFile++;case 5:return t.a(2)}},n)}))()},Sign:function(){var e=this;return n(t().m(function r(){var n;return t().w(function(t){for(;;)switch(t.n){case 0:return e.showPassphrase=!1,e.loading=!0,t.n=1,e.$api.post("/reports/uji/sign",{PermohonanID:e.forms.PermohonanID,Passphrase:e.passphrase,FilePath:e.rawUrl.replace(e.$api.url,"")});case 1:n=t.v,e.loading=!1,n.success?(e.$api.notify(n.message,"success"),e.rawUrl=""):(e.$api.notify(n.message,"error"),e.passphrase="");case 2:return t.a(2)}},r)}))()}}},function(){var t=this,e=t._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{on:{"item-click":t.ShowSertifikat}}),t.rawUrl?e("div",{staticClass:"right-pane"},[e("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.rawUrl,frameborder:"0"}}),e("div",{staticStyle:{position:"fixed",bottom:"20px",right:"40px",display:"flex"}},[e(l,{attrs:{menu:["Download","Upload"]},on:{"item-click":function(e){return t.MenuManual(e)}},scopedSlots:t._u([{key:"default",fn:function(r){var n=r.on;return[e(s,[e(c,t._g({},n),[t._v(" mdi-dots-vertical ")])],1)]}}],null,!1,2745980243)}),e(s,{directives:[{name:"show",rawName:"v-show",value:!t.loading,expression:"!loading"}],staticClass:"close-right-pa ne",staticStyle:{"margin-right":"8px"},on:{click:function(e){t.rawUrl=""}}},[t._v(" BATAL ")]),e(s,{directives:[{name:"show",rawName:"v-show",value:!t.signedUrl,expression:"!signedUrl"}],attrs:{color:"primary",disabled:t.loading},on:{click:function(e){t.showPassphrase=!0}}},[t._v(" TANDA TANGANI ")]),e(i,{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],key:t.resetFile,ref:"uploader",attrs:{type:"file"},on:{change:t.uploadFile}})],1)]):t._e(),e(u,{attrs:{title:"Passphrase:",show:t.showPassphrase},on:{"update:show":function(e){t.showPassphrase=e},submit:t.Sign}},[e(i,{attrs:{type:"password",value:t.passphrase},on:{"update:value":function(e){t.passphrase=e}}})],1)],1)},[],!1,null,null).exports)}}})}();
