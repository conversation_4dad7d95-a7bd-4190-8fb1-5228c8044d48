!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function n(t){for(var n=1;n<arguments.length;n++){var o=null!=arguments[n]?arguments[n]:{};n%2?e(Object(o),!0).forEach(function(e){r(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):e(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function r(e,n,r){return(n=function(e){var n=function(e,n){if("object"!=t(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,n||"default");if("object"!=t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"==t(n)?n:n+""}(n))in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function c(n,r,o,i){var c=r&&r.prototype instanceof u?r:u,s=Object.create(c.prototype);return a(s,"_invoke",function(n,r,o){var a,i,c,u=0,s=o||[],p=!1,d={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,n){return a=e,i=0,c=t,d.n=n,l}};function f(n,r){for(i=n,c=r,e=0;!p&&u&&!o&&e<s.length;e++){var o,a=s[e],f=d.p,v=a[2];n>3?(o=v===r)&&(c=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=t):a[0]<=f&&((o=n<2&&f<a[1])?(i=0,d.v=r,d.n=a[1]):f<v&&(o=n<3||a[0]>r||r>v)&&(a[4]=n,a[5]=r,d.n=v,i=0))}if(o||n>1)return l;throw p=!0,r}return function(o,s,v){if(u>1)throw TypeError("Generator is already running");for(p&&1===s&&f(s,v),i=s,c=v;(e=i<2?t:c)||!p;){a||(i?i<3?(i>1&&(d.n=-1),f(i,c)):d.n=c:d.v=c);try{if(u=2,a){if(i||(o="next"),e=a[o]){if(!(e=e.call(a,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,i<2&&(i=0)}else 1===i&&(e=a.return)&&e.call(a),i<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=t}else if((e=(p=d.n<0)?c:n.call(r,d))!==l)break}catch(e){a=t,i=1,c=e}finally{u=1}}return{value:e,done:p}}}(n,o,i),!0),s}var l={};function u(){}function s(){}function p(){}e=Object.getPrototypeOf;var d=[][r]?e(e([][r]())):(a(e={},r,function(){return this}),e),f=p.prototype=u.prototype=Object.create(d);function v(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,a(t,i,"GeneratorFunction")),t.prototype=Object.create(f),t}return s.prototype=p,a(f,"constructor",p),a(p,"constructor",s),s.displayName="GeneratorFunction",a(p,i,"GeneratorFunction"),a(f),a(f,i,"Generator"),a(f,r,function(){return this}),a(f,"toString",function(){return"[object Generator]"}),(o=function(){return{w:c,m:v}})()}function a(t,e,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}a=function(t,e,n,r){function i(e,n){a(t,e,function(t){return this._invoke(e,n,t)})}e?o?o(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r}):t[e]=n:(i("next",0),i("throw",1),i("return",2))},a(t,e,n,r)}function i(t,e,n,r,o,a,i){try{var c=t[a](i),l=c.value}catch(t){return void n(t)}c.done?e(l):Promise.resolve(l).then(r,o)}function c(t){return function(){var e=this,n=arguments;return new Promise(function(r,o){var a=t.apply(e,n);function c(t){i(a,r,o,c,l,"next",t)}function l(t){i(a,r,o,c,l,"throw",t)}c(void 0)})}}System.register(["./index-legacy-BUdDePUl.js","./AlatDet-legacy-Dv-MfQFx.js"],function(t,e){"use strict";var r,a,i,l,u,s,p,d,f,v,m,h;return{setters:[function(t){r=t.n,a=t.c,i=t.k,l=t._,u=t.g,s=t.q,p=t.r,d=t.s,f=t.t,v=t.u,m=t.v},function(t){h=t.A}],execute:function(){var e=document.createElement("style");e.textContent=".tbl-jadwal{border-collapse:collapse}.tbl-jadwal td{padding:5px 8px;border:1px solid black;width:67px}.tbl-jadwal td.active{background:#e3f2fd}.tbl-jadwal thead{display:block;background:#333;color:#ddd;font-weight:700}.tbl-jadwal thead th{padding:5px 8px;text-align:center;position:sticky;width:67px}.tbl-jadwal tbody{display:block;height:calc(100vh - 150px);overflow:auto}\n/*$vite$:1*/",document.head.appendChild(e);t("default",r({components:{AlatDet:h},data:function(){return{tahun:(new Date).getFullYear(),items:[],showDet:!1,popover:{x:0,y:0,show:!1,item:null,month:null},menus:[{title:"Rencana Pemeliharaan",cmd:"MP",icon:"mdi-circle",iconColor:"#ddd"},{title:"Realisasi Pemeliharaan",cmd:"MR",icon:"mdi-circle",iconColor:"primary"},{title:"Rencana Kalibrasi",cmd:"KP",icon:"mdi-square",iconColor:"#FFE0B2"},{title:"Realisasi Kalibrasi",cmd:"KR",icon:"mdi-square",iconColor:"#EF6C00"},{title:"Clear",cmd:"X",icon:"mdi-close",iconColor:"danger"}],months:["Jan","Feb","Mar","Apr","Mei","Jun","Jul","Agu","Sep","Okt","Nov","Des"]}},computed:{yearOptions:function(){for(var t=[],e=-1;e<3;e++)t.push({val:(new Date).getFullYear()-e,txt:(new Date).getFullYear()-e});return t}},watch:{tahun:function(){this.populate()},showDet:function(t){t||this.populate()}},mounted:function(){this.populate()},methods:{populate:function(){var t=this;return c(o().m(function e(){var n,r;return o().w(function(e){for(;;)switch(e.n){case 0:return(n=document.querySelector("#tbl-jadwal td.active"))&&n.classList.remove("active"),e.n=1,t.$api.call("PML_SelJadwal",{Tahun:t.tahun});case 1:r=e.v,t.items=r.data;case 2:return e.a(2)}},e)}))()},PopulateRencana:function(){var t=this;return c(o().m(function e(){return o().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("PML_SavJadwalPlan",{Tahun:t.tahun});case 1:t.populate();case 2:return e.a(2)}},e)}))()},openMenu:function(t,e,n){var r=this;this.popover.item=e,this.popover.AlatId=e.AlatId,this.popover.Tanggal=m(n+" 01 "+this.tahun).format("YYYY-MM-DD"),t.preventDefault();var o=document.querySelector("#tbl-jadwal td.active");o&&o.classList.remove("active"),t.target.classList.add("active"),this.popover.show=!1,this.popover.x=t.clientX,this.popover.y=t.clientY,this.$nextTick(function(){r.popover.show=!0})},menuClick:function(t){var e=this;return c(o().m(function r(){var a,i;return o().w(function(r){for(;;)switch(r.n){case 0:if(a=e.menus.find(function(e){return e.title==t}),!t.match(/Realisasi/)){r.n=1;break}e.popover.Aktifitas=t.match(/kalibrasi/i)?"kalibrasi":"pemeliharaan",e.showDet=!0,r.n=3;break;case 1:if(!a){r.n=3;break}return i=m(e.popover.Tanggal).format("MMM"),e.popover.item[i]+=a.cmd,r.n=2,e.$api.call("PML_SavJadwal",n(n({},e.popover),{},{Tipe:a.cmd}));case 2:e.populate();case 3:return r.a(2)}},r)}))()},Print:function(){var t=this;return c(o().m(function e(){var n;return o().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.post("/reports/template/KalibrasiPemeliharaan.docx",{sp:"PML_RptJadwal"});case 1:(n=e.v).success&&t.$api.download("/report/"+n.data);case 2:return e.a(2)}},e)}))()}}},function(){var t=this,e=t._self._c;return e("div",{staticStyle:{height:"100vh"}},[e("div",{staticStyle:{height:"calc(100vh - 80px)",overflow:"auto"}},[e("div",{staticStyle:{display:"flex",width:"1150px"}},[e(a,{attrs:{width:"100px",items:t.yearOptions,value:t.tahun},on:{"update:value":function(e){t.tahun=e}}}),e(i),e(l,{staticStyle:{"margin-top":"3px"},attrs:{small:"",outlined:"",color:"primary"},on:{click:t.PopulateRencana}},[t._v(" ISI RENCANA ")]),e(u,{staticStyle:{"margin-left":"8px"},on:{click:t.Print}},[t._v("mdi-printer")])],1),e("table",{staticClass:"tbl-jadwal",attrs:{id:"tbl-jadwal"}},[e("thead",[e("tr",[e("th",{staticStyle:{width:"350px"}},[t._v("Nama Alat")]),t._l(t.months,function(n){return e("th",{key:n},[t._v(t._s(n))])})],2)]),e("tbody",t._l(t.items,function(n,r){return e("tr",{key:r},[e("td",{staticStyle:{width:"350px"}},[e("div",{staticStyle:{"font-size":"14px","font-weight":"bold"}},[t._v(" "+t._s(n.NamaAlat)+" "+t._s(n.Merk?"(".concat(n.Merk,")"):"")+" ")]),e("div",{staticStyle:{"font-size":"small",color:"gray"}},[t._v(" "+t._s(n.NomorAlat||"-")+" ")])]),t._l(t.months,function(r,o){return e("td",{key:r,on:{contextmenu:function(e){return t.openMenu(e,n,o+1)}}},[e("div",[e(u,{directives:[{name:"show",rawName:"v-show",value:n[r].includes("MP"),expression:"item[m].includes('MP')"}],attrs:{color:"#ddd"}},[t._v("mdi-circle")]),e(u,{directives:[{name:"show",rawName:"v-show",value:n[r].includes("MR"),expression:"item[m].includes('MR')"}],attrs:{color:"primary"}},[t._v(" mdi-circle ")])],1),e("div",[e(u,{directives:[{name:"show",rawName:"v-show",value:n[r].includes("KP"),expression:"item[m].includes('KP')"}],attrs:{color:"#FFE0B2"}},[t._v("mdi-square")]),e(u,{directives:[{name:"show",rawName:"v-show",value:n[r].includes("KR"),expression:"item[m].includes('KR')"}],attrs:{color:"#EF6C00"}},[t._v(" mdi-square ")])],1)])})],2)}),0)]),e(s,{attrs:{"position-x":t.popover.x,"position-y":t.popover.y,absolute:"","offset-y":""},model:{value:t.popover.show,callback:function(e){t.$set(t.popover,"show",e)},expression:"popover.show"}},[e(p,{attrs:{dense:""}},t._l(t.menus,function(n,r){return e(d,{key:r,on:{click:function(e){return t.menuClick(n.title)}}},[e(f,[e(u,{attrs:{color:n.iconColor}},[t._v(t._s(n.icon))])],1),e(v,[t._v(t._s(n.title))])],1)}),1)],1),e("alat-det",{attrs:{show:t.showDet,alatId:t.popover.AlatId,aktifitas:t.popover.Aktifitas,tanggal:t.popover.Tanggal},on:{"update:show":function(e){t.showDet=e}}})],1)])},[],!1,null,null).exports)}}})}();
