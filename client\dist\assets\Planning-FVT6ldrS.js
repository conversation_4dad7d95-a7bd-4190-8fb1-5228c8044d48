import{n as i,i as r,a as n,g as o,l,_ as c}from"./index-DYIZrBBo.js";const d={data:()=>({xshow:!1,forms:{},loading:!1}),props:{show:<PERSON><PERSON><PERSON>},watch:{show(a){this.xshow=a,a||(this.forms={})},xshow(a){this.$emit("update:show",a)}},methods:{async Save(){this.loading=!0,(await this.$api.call("ISO_SavPenjaminMutu",this.forms)).success&&(this.$emit("save"),this.$emit("update:show",!1)),this.loading=!1}}};var h=function(){var t=this,e=t._self._c;return e(r,{attrs:{title:"Tambah Dokumen",show:t.xshow,loading:t.loading,width:"800px"},on:{"update:show":function(s){t.xshow=s},submit:t.Save}},[e("div",{staticStyle:{width:"400px"}},[e(n,{attrs:{type:"text",value:t.forms.DocName,label:"Nama Dokumen",width:"400px"},on:{"update:value":function(s){return t.$set(t.forms,"DocName",s)}}}),e(n,{attrs:{type:"text",value:t.forms.DocUrl,label:"Url",width:"400px"},on:{"update:value":function(s){return t.$set(t.forms,"DocUrl",s)}}})],1)])},u=[],p=i(d,h,u,!1,null,null);const _=p.exports,f={components:{AddDoc:_},data:()=>({keyword:"",rebind:1,showAddDoc:!1,currentUrl:""}),methods:{ItemClick(a){this.currentUrl=a.DocUrl},ItemsChange(a){!this.currentUrl&&a.length&&(this.currentUrl=a[0].DocUrl)}}};var m=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{height:"calc(100vh - 68px)",overflow:"hidden",position:"relative"}},[e("iframe",{staticStyle:{"margin-top":"-55px",height:"calc(100% + 55px)"},attrs:{src:t.currentUrl,width:"100%"}}),e("div",{staticClass:"sidebar-container"},[e("div",{staticClass:"sidebar-opener"},[e(o,{attrs:{large:""}},[t._v("mdi-chevron-left")])],1),e("div",{staticClass:"sidebar-content"},[e("div",{staticStyle:{padding:"10px",display:"flex"}},[e(n,{staticClass:"searchbar",staticStyle:{"margin-left":"-8px"},attrs:{type:"text",value:t.keyword,placeholder:"Cari ..",width:"266px",rightIcon:"mdi-magnify"},on:{"update:value":function(s){t.keyword=s}}})],1),e(l,{attrs:{dbref:"ISO_SelPenjaminMutu",dbparams:{Keyword:t.keyword},height:"calc(100% - 85px)",rebind:t.rebind,selectOnLoad:!0},on:{itemClick:t.ItemClick,change:t.ItemsChange},scopedSlots:t._u([{key:"default",fn:function({row:s}){return[e("div",{staticStyle:{padding:"10px"}},[e("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(s.DocName)+" ")]),e("div",{staticStyle:{color:"gray",display:"flex","font-size":"13px"}},[t._v(" "+t._s(t._f("format")(s.CreatedAt))+" ")])])]}}])}),e(c,{staticStyle:{width:"100%"},attrs:{outlined:"",color:"primary"},on:{click:function(s){t.showAddDoc=!0}}},[e(o,{attrs:{left:""}},[t._v("mdi-plus")]),t._v(" TAMBAH BARU ")],1)],1)]),e("AddDoc",{attrs:{show:t.showAddDoc},on:{"update:show":function(s){t.showAddDoc=s},save:function(s){t.rebind++}}})],1)},v=[],w=i(f,m,v,!1,null,null);const g=w.exports;export{g as default};
