{"name": "server", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"serve": "NODE_ENV=development nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@simplewebauthn/server": "^9.0.3", "archiver": "^5.3.0", "axios": "^1.6.7", "bcrypt": "^5.1.1", "body-parser": "^1.19.0", "carbone": "^3.2.3", "connect-timeout": "^1.9.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "docx-templates": "^4.5.3", "dotenv": "^16.4.5", "easy-pdf-merge": "^0.2.6", "eslint": "^7.28.0", "exceljs": "^4.1.1", "exif": "^0.6.0", "express": "^4.18.2", "express-fileupload": "^1.1.7-alpha.4", "file-type": "^19.0.0", "http": "^0.0.1-security", "https": "^1.0.0", "jsonwebtoken": "^9.0.2", "minisearch": "^6.3.0", "moment": "^2.30.1", "mssql": "^6.2.0", "multer": "^1.4.2", "mysql": "^2.18.1", "nodemailer": "^6.9.13", "or": "^0.2.0", "pdf-img-convert": "^1.2.1", "pdf-lib": "^1.17.1", "pdf-merger-js": "^3.2.1", "pdf-parse": "^1.1.1", "qrcode": "^1.5.3", "sharp": "^0.33.5", "shelljs": "^0.8.4", "telegraf": "^4.12.2", "tesseract.js": "^5.0.4", "websocket": "^1.0.31", "whatsapp-web.js": "^1.23.0", "ws": "^7.3.1"}, "eslintConfig": {"root": true, "env": {"node": true, "es6": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2020, "parser": "babel-es<PERSON>"}, "rules": {"block-spacing": 1, "object-curly-spacing": 1, "indent": ["error", 2], "no-unused-vars": "off"}}}