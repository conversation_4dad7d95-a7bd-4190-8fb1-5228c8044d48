{"name": "silakon", "version": "1.0.0", "description": "", "private": true, "workspaces": ["client", "server"], "scripts": {"start": "npm-run-all -l -p client server", "build": "npm run build --workspace=client", "client": "npm run dev --workspace=client", "server": "npm run serve --workspace=server"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"eslint-plugin-prettier": "4.0.0", "eslint-plugin-vue": "^9.15.1", "jszip": "^3.10.1"}, "devDependencies": {"eslint-config-recommended": "^4.1.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"vue/no-reserved-component-names": ["off"], "vue/multi-word-component-names": ["off"], "vue/no-unused-components": ["off"]}}}