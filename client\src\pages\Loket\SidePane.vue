<template>
  <div class="sidepane col-12 col-lg-2 col-md-3 col-sm-12">
    <div style="padding: 10px; display: flex">
      <Input
        type="text"
        :value.sync="keyword"
        placeholder="Cari .."
        width="270px"
        rightIcon="mdi-magnify"
        class="searchbar"
      />
    </div>
    <div style="height: calc(100% - 47px)">
      <List
        dbref="UJI_SelPermohonanList"
        :dbparams="dbparams"
        @itemClick="ItemClick"
        :height="addButton ? 'calc(100% - 60px)' : '100%'"
        :rebind="rebind"
        :filters="filters"
        :selectOnLoad="true"
      >
        <template v-slot="{ row }">
          <div style="font-size: 13px; padding: 10px">
            <div style="color: gray; float: right">
              {{ row.TglMasuk | format }}
            </div>
            <div
              :style="{ color: getRowColor(row) }"
              style="
                font-weight: bold;
                font-family: Raleway;
                text-transform: uppercase;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 180px;
                height: 20px;
                white-space: nowrap;
              "
            >
              {{ row.NamaPelanggan }}
            </div>
            <div style="color: gray; float: right">
              {{
                row.StatusID >= 8 && row.BayarStatus != 1
                  ? 'BELUM BAYAR'
                  : row.StatusName
              }}
            </div>
            <div style="color: gray; display: flex">
              <span class="no-uji">
                {{ row.NoPengujian }}
              </span>
              <span
                v-if="row.LkStatus == 'rejected'"
                style="
                  font-size: 10px;
                  padding: 3px 5px 0 5px;
                  background: red;
                  border-radius: 5px;
                  color: white;
                  margin-right: 5px;
                "
              >
                LK
              </span>
              <span
                v-else-if="row.LkStatus == 'submitted'"
                style="
                  font-size: 10px;
                  padding: 3px 5px 0 5px;
                  background: green;
                  border-radius: 5px;
                  color: white;
                  margin-right: 5px;
                "
              >
                LK
              </span>
              <span
                v-else-if="row.LkStatus == 'signed'"
                style="
                  font-size: 10px;
                  padding: 3px 5px 0 5px;
                  background: #00897b;
                  border-radius: 5px;
                  color: white;
                  margin-right: 5px;
                "
              >
                LK
              </span>
              <div
                v-else
                style="
                  overflow: hidden;
                  text-overflow: ellipsis;
                  width: 160px;
                  height: 20px;
                  white-space: nowrap;
                "
              >
                {{ row.JenisUji }}
              </div>
            </div>
          </div>
        </template>
        <template v-slot:lastrow>
          <v-btn
            v-if="keyword && !ShowLastYear"
            small
            outlined
            color="primary"
            style="width: calc(100% - 10px); margin-top: 20px"
            @click="ShowLastYear = 1"
          >
            TAMPILKAN TAHUN SEBELUMNYA
          </v-btn>
        </template>
      </List>
      <div style="padding: 10px; border-top: 1px solid #ddd" v-if="addButton">
        <v-btn
          outlined
          color="primary"
          style="width: calc(100% - 10px)"
          @click="DaftarBaru"
        >
          <v-icon left>mdi-plus</v-icon>
          DAFTAR BARU
        </v-btn>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
export default {
  data: () => ({
    keyword: '',
    ShowLastYear: 0,
  }),
  props: {
    statusId: String,
    rebind: Number,
    addButton: Boolean,
    filters: Object,
  },
  watch: {
    keyword(val) {
      if (!val) this.ShowLastYear = 0
    },
  },
  computed: {
    dbparams() {
      return {
        StatusID: this.statusId,
        Keyword: this.keyword || undefined,
        ShowLastYear: this.ShowLastYear,
      }
    },
  },
  methods: {
    getRowColor(row) {
      return !row.BayarDate && moment().diff(row.TglMasuk, 'day') > 14
        ? 'red'
        : '#333'
    },
    ItemClick(val) {
      this.$emit('item-click', val)
    },
    DaftarBaru() {
      this.$emit('item-click', { PengujianID: 0 })
    },
  },
}
</script>
<style lang="scss">
.sidepane {
  padding: 0px !important;
  // width: 300px;
  border-right: 1px solid #ddd;
  /*margin-left: -20px;*/
  height: calc(100vh - 64px);
  overflow: hidden;
  padding-right: 0;

  .searchbar {
    margin-bottom: 0;
    input {
      background: transparent !important;
      border-bottom: 0 !important;
    }
  }
  .--item {
    border-radius: 10px;
    margin-right: 10px;
    .no-uji {
      font-size: 10px;
      padding: 3px 5px 0 5px;
      background: #ddd;
      border-radius: 5px;
      color: #333;
      margin-right: 5px;
    }
  }
  .--item.selected {
    .no-uji {
      background: white;
    }
  }
}
</style>
