try{self["workbox:core:7.2.0"]&&_()}catch(a){}const N=(a,...e)=>{let t=a;return e.length>0&&(t+=" :: ".concat(JSON.stringify(e))),t},E=N;class l extends Error{constructor(e,t){const s=E(e,t);super(s),this.name=e,this.details=t}}const f={googleAnalytics:"googleAnalytics",precache:"precache-v2",prefix:"workbox",runtime:"runtime",suffix:typeof registration<"u"?registration.scope:""},b=a=>[f.prefix,a,f.suffix].filter(e=>e&&e.length>0).join("-"),I=a=>{for(const e of Object.keys(f))a(e)},C={updateDetails:a=>{I(e=>{typeof a[e]=="string"&&(f[e]=a[e])})},getGoogleAnalyticsName:a=>a||b(f.googleAnalytics),getPrecacheName:a=>a||b(f.precache),getPrefix:()=>f.prefix,getRuntimeName:a=>a||b(f.runtime),getSuffix:()=>f.suffix};function k(a,e){const t=e();return a.waitUntil(t),t}try{self["workbox:precaching:7.2.0"]&&_()}catch(a){}const x="__WB_REVISION__";function O(a){if(!a)throw new l("add-to-cache-list-unexpected-type",{entry:a});if(typeof a=="string"){const r=new URL(a,location.href);return{cacheKey:r.href,url:r.href}}const{revision:e,url:t}=a;if(!t)throw new l("add-to-cache-list-unexpected-type",{entry:a});if(!e){const r=new URL(t,location.href);return{cacheKey:r.href,url:r.href}}const s=new URL(t,location.href),n=new URL(t,location.href);return s.searchParams.set(x,e),{cacheKey:s.href,url:n.href}}class W{constructor(){this.updatedURLs=[],this.notUpdatedURLs=[],this.handlerWillStart=async({request:e,state:t})=>{t&&(t.originalRequest=e)},this.cachedResponseWillBeUsed=async({event:e,state:t,cachedResponse:s})=>{if(e.type==="install"&&t&&t.originalRequest&&t.originalRequest instanceof Request){const n=t.originalRequest.url;s?this.notUpdatedURLs.push(n):this.updatedURLs.push(n)}return s}}}class S{constructor({precacheController:e}){this.cacheKeyWillBeUsed=async({request:t,params:s})=>{const n=(s==null?void 0:s.cacheKey)||this._precacheController.getCacheKeyForURL(t.url);return n?new Request(n,{headers:t.headers}):t},this._precacheController=e}}let p;function M(){if(p===void 0){const a=new Response("");if("body"in a)try{new Response(a.body),p=!0}catch(e){p=!1}p=!1}return p}async function A(a,e){let t=null;if(a.url&&(t=new URL(a.url).origin),t!==self.location.origin)throw new l("cross-origin-copy-response",{origin:t});const s=a.clone(),r={headers:new Headers(s.headers),status:s.status,statusText:s.statusText},i=M()?s.body:await s.blob();return new Response(i,r)}const D=a=>new URL(String(a),location.href).href.replace(new RegExp("^".concat(location.origin)),"");function P(a,e){const t=new URL(a);for(const s of e)t.searchParams.delete(s);return t.href}async function q(a,e,t,s){const n=P(e.url,t);if(e.url===n)return a.match(e,s);const r=Object.assign(Object.assign({},s),{ignoreSearch:!0}),i=await a.keys(e,r);for(const c of i){const o=P(c.url,t);if(n===o)return a.match(c,s)}}class j{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}const F=new Set;async function H(){for(const a of F)await a()}function B(a){return new Promise(e=>setTimeout(e,a))}try{self["workbox:strategies:7.2.0"]&&_()}catch(a){}function m(a){return typeof a=="string"?new Request(a):a}class G{constructor(e,t){this._cacheKeys={},Object.assign(this,t),this.event=t.event,this._strategy=e,this._handlerDeferred=new j,this._extendLifetimePromises=[],this._plugins=[...e.plugins],this._pluginStateMap=new Map;for(const s of this._plugins)this._pluginStateMap.set(s,{});this.event.waitUntil(this._handlerDeferred.promise)}async fetch(e){const{event:t}=this;let s=m(e);if(s.mode==="navigate"&&t instanceof FetchEvent&&t.preloadResponse){const i=await t.preloadResponse;if(i)return i}const n=this.hasCallback("fetchDidFail")?s.clone():null;try{for(const i of this.iterateCallbacks("requestWillFetch"))s=await i({request:s.clone(),event:t})}catch(i){if(i instanceof Error)throw new l("plugin-error-request-will-fetch",{thrownErrorMessage:i.message})}const r=s.clone();try{let i;i=await fetch(s,s.mode==="navigate"?void 0:this._strategy.fetchOptions);for(const c of this.iterateCallbacks("fetchDidSucceed"))i=await c({event:t,request:r,response:i});return i}catch(i){throw n&&await this.runCallbacks("fetchDidFail",{error:i,event:t,originalRequest:n.clone(),request:r.clone()}),i}}async fetchAndCachePut(e){const t=await this.fetch(e),s=t.clone();return this.waitUntil(this.cachePut(e,s)),t}async cacheMatch(e){const t=m(e);let s;const{cacheName:n,matchOptions:r}=this._strategy,i=await this.getCacheKey(t,"read"),c=Object.assign(Object.assign({},r),{cacheName:n});s=await caches.match(i,c);for(const o of this.iterateCallbacks("cachedResponseWillBeUsed"))s=await o({cacheName:n,matchOptions:r,cachedResponse:s,request:i,event:this.event})||void 0;return s}async cachePut(e,t){const s=m(e);await B(0);const n=await this.getCacheKey(s,"write");if(!t)throw new l("cache-put-with-no-response",{url:D(n.url)});const r=await this._ensureResponseSafeToCache(t);if(!r)return!1;const{cacheName:i,matchOptions:c}=this._strategy,o=await self.caches.open(i),h=this.hasCallback("cacheDidUpdate"),g=h?await q(o,n.clone(),["__WB_REVISION__"],c):null;try{await o.put(n,h?r.clone():r)}catch(u){if(u instanceof Error)throw u.name==="QuotaExceededError"&&await H(),u}for(const u of this.iterateCallbacks("cacheDidUpdate"))await u({cacheName:i,oldResponse:g,newResponse:r.clone(),request:n,event:this.event});return!0}async getCacheKey(e,t){const s="".concat(e.url," | ").concat(t);if(!this._cacheKeys[s]){let n=e;for(const r of this.iterateCallbacks("cacheKeyWillBeUsed"))n=m(await r({mode:t,request:n,event:this.event,params:this.params}));this._cacheKeys[s]=n}return this._cacheKeys[s]}hasCallback(e){for(const t of this._strategy.plugins)if(e in t)return!0;return!1}async runCallbacks(e,t){for(const s of this.iterateCallbacks(e))await s(t)}*iterateCallbacks(e){for(const t of this._strategy.plugins)if(typeof t[e]=="function"){const s=this._pluginStateMap.get(t);yield r=>{const i=Object.assign(Object.assign({},r),{state:s});return t[e](i)}}}waitUntil(e){return this._extendLifetimePromises.push(e),e}async doneWaiting(){let e;for(;e=this._extendLifetimePromises.shift();)await e}destroy(){this._handlerDeferred.resolve(null)}async _ensureResponseSafeToCache(e){let t=e,s=!1;for(const n of this.iterateCallbacks("cacheWillUpdate"))if(t=await n({request:this.request,response:t,event:this.event})||void 0,s=!0,!t)break;return s||t&&t.status!==200&&(t=void 0),t}}class ${constructor(e={}){this.cacheName=C.getRuntimeName(e.cacheName),this.plugins=e.plugins||[],this.fetchOptions=e.fetchOptions,this.matchOptions=e.matchOptions}handle(e){const[t]=this.handleAll(e);return t}handleAll(e){e instanceof FetchEvent&&(e={event:e,request:e.request});const t=e.event,s=typeof e.request=="string"?new Request(e.request):e.request,n="params"in e?e.params:void 0,r=new G(this,{event:t,request:s,params:n}),i=this._getResponse(r,s,t),c=this._awaitComplete(i,r,s,t);return[i,c]}async _getResponse(e,t,s){await e.runCallbacks("handlerWillStart",{event:s,request:t});let n;try{if(n=await this._handle(t,e),!n||n.type==="error")throw new l("no-response",{url:t.url})}catch(r){if(r instanceof Error){for(const i of e.iterateCallbacks("handlerDidError"))if(n=await i({error:r,event:s,request:t}),n)break}if(!n)throw r}for(const r of e.iterateCallbacks("handlerWillRespond"))n=await r({event:s,request:t,response:n});return n}async _awaitComplete(e,t,s,n){let r,i;try{r=await e}catch(c){}try{await t.runCallbacks("handlerDidRespond",{event:n,request:s,response:r}),await t.doneWaiting()}catch(c){c instanceof Error&&(i=c)}if(await t.runCallbacks("handlerDidComplete",{event:n,request:s,response:r,error:i}),t.destroy(),i)throw i}}class d extends ${constructor(e={}){e.cacheName=C.getPrecacheName(e.cacheName),super(e),this._fallbackToNetwork=e.fallbackToNetwork!==!1,this.plugins.push(d.copyRedirectedCacheableResponsesPlugin)}async _handle(e,t){const s=await t.cacheMatch(e);return s||(t.event&&t.event.type==="install"?await this._handleInstall(e,t):await this._handleFetch(e,t))}async _handleFetch(e,t){let s;const n=t.params||{};if(this._fallbackToNetwork){const r=n.integrity,i=e.integrity,c=!i||i===r;s=await t.fetch(new Request(e,{integrity:e.mode!=="no-cors"?i||r:void 0})),r&&c&&e.mode!=="no-cors"&&(this._useDefaultCacheabilityPluginIfNeeded(),await t.cachePut(e,s.clone()))}else throw new l("missing-precache-entry",{cacheName:this.cacheName,url:e.url});return s}async _handleInstall(e,t){this._useDefaultCacheabilityPluginIfNeeded();const s=await t.fetch(e);if(!await t.cachePut(e,s.clone()))throw new l("bad-precaching-response",{url:e.url,status:s.status});return s}_useDefaultCacheabilityPluginIfNeeded(){let e=null,t=0;for(const[s,n]of this.plugins.entries())n!==d.copyRedirectedCacheableResponsesPlugin&&(n===d.defaultPrecacheCacheabilityPlugin&&(e=s),n.cacheWillUpdate&&t++);t===0?this.plugins.push(d.defaultPrecacheCacheabilityPlugin):t>1&&e!==null&&this.plugins.splice(e,1)}}d.defaultPrecacheCacheabilityPlugin={async cacheWillUpdate({response:a}){return!a||a.status>=400?null:a}};d.copyRedirectedCacheableResponsesPlugin={async cacheWillUpdate({response:a}){return a.redirected?await A(a):a}};class V{constructor({cacheName:e,plugins:t=[],fallbackToNetwork:s=!0}={}){this._urlsToCacheKeys=new Map,this._urlsToCacheModes=new Map,this._cacheKeysToIntegrities=new Map,this._strategy=new d({cacheName:C.getPrecacheName(e),plugins:[...t,new S({precacheController:this})],fallbackToNetwork:s}),this.install=this.install.bind(this),this.activate=this.activate.bind(this)}get strategy(){return this._strategy}precache(e){this.addToCacheList(e),this._installAndActiveListenersAdded||(self.addEventListener("install",this.install),self.addEventListener("activate",this.activate),this._installAndActiveListenersAdded=!0)}addToCacheList(e){const t=[];for(const s of e){typeof s=="string"?t.push(s):s&&s.revision===void 0&&t.push(s.url);const{cacheKey:n,url:r}=O(s),i=typeof s!="string"&&s.revision?"reload":"default";if(this._urlsToCacheKeys.has(r)&&this._urlsToCacheKeys.get(r)!==n)throw new l("add-to-cache-list-conflicting-entries",{firstEntry:this._urlsToCacheKeys.get(r),secondEntry:n});if(typeof s!="string"&&s.integrity){if(this._cacheKeysToIntegrities.has(n)&&this._cacheKeysToIntegrities.get(n)!==s.integrity)throw new l("add-to-cache-list-conflicting-integrities",{url:r});this._cacheKeysToIntegrities.set(n,s.integrity)}if(this._urlsToCacheKeys.set(r,n),this._urlsToCacheModes.set(r,i),t.length>0){const c="Workbox is precaching URLs without revision info: ".concat(t.join(", "),"\nThis is generally NOT safe. Learn more at https://bit.ly/wb-precache");console.warn(c)}}}install(e){return k(e,async()=>{const t=new W;this.strategy.plugins.push(t);for(const[r,i]of this._urlsToCacheKeys){const c=this._cacheKeysToIntegrities.get(i),o=this._urlsToCacheModes.get(r),h=new Request(r,{integrity:c,cache:o,credentials:"same-origin"});await Promise.all(this.strategy.handleAll({params:{cacheKey:i},request:h,event:e}))}const{updatedURLs:s,notUpdatedURLs:n}=t;return{updatedURLs:s,notUpdatedURLs:n}})}activate(e){return k(e,async()=>{const t=await self.caches.open(this.strategy.cacheName),s=await t.keys(),n=new Set(this._urlsToCacheKeys.values()),r=[];for(const i of s)n.has(i.url)||(await t.delete(i),r.push(i.url));return{deletedURLs:r}})}getURLsToCacheKeys(){return this._urlsToCacheKeys}getCachedURLs(){return[...this._urlsToCacheKeys.keys()]}getCacheKeyForURL(e){const t=new URL(e,location.href);return this._urlsToCacheKeys.get(t.href)}getIntegrityForCacheKey(e){return this._cacheKeysToIntegrities.get(e)}async matchPrecache(e){const t=e instanceof Request?e.url:e,s=this.getCacheKeyForURL(t);if(s)return(await self.caches.open(this.strategy.cacheName)).match(s)}createHandlerBoundToURL(e){const t=this.getCacheKeyForURL(e);if(!t)throw new l("non-precached-url",{url:e});return s=>(s.request=new Request(e),s.params=Object.assign({cacheKey:t},s.params),this.strategy.handle(s))}}let U;const v=()=>(U||(U=new V),U);try{self["workbox:routing:7.2.0"]&&_()}catch(a){}const K="GET",R=a=>a&&typeof a=="object"?a:{handle:a};class w{constructor(e,t,s=K){this.handler=R(t),this.match=e,this.method=s}setCatchHandler(e){this.catchHandler=R(e)}}class Q extends w{constructor(e,t,s){const n=({url:r})=>{const i=e.exec(r.href);if(i&&!(r.origin!==location.origin&&i.index!==0))return i.slice(1)};super(n,t,s)}}class z{constructor(){this._routes=new Map,this._defaultHandlerMap=new Map}get routes(){return this._routes}addFetchListener(){self.addEventListener("fetch",e=>{const{request:t}=e,s=this.handleRequest({request:t,event:e});s&&e.respondWith(s)})}addCacheListener(){self.addEventListener("message",e=>{if(e.data&&e.data.type==="CACHE_URLS"){const{payload:t}=e.data,s=Promise.all(t.urlsToCache.map(n=>{typeof n=="string"&&(n=[n]);const r=new Request(...n);return this.handleRequest({request:r,event:e})}));e.waitUntil(s),e.ports&&e.ports[0]&&s.then(()=>e.ports[0].postMessage(!0))}})}handleRequest({request:e,event:t}){const s=new URL(e.url,location.href);if(!s.protocol.startsWith("http"))return;const n=s.origin===location.origin,{params:r,route:i}=this.findMatchingRoute({event:t,request:e,sameOrigin:n,url:s});let c=i&&i.handler;const o=e.method;if(!c&&this._defaultHandlerMap.has(o)&&(c=this._defaultHandlerMap.get(o)),!c)return;let h;try{h=c.handle({url:s,request:e,event:t,params:r})}catch(u){h=Promise.reject(u)}const g=i&&i.catchHandler;return h instanceof Promise&&(this._catchHandler||g)&&(h=h.catch(async u=>{if(g)try{return await g.handle({url:s,request:e,event:t,params:r})}catch(L){L instanceof Error&&(u=L)}if(this._catchHandler)return this._catchHandler.handle({url:s,request:e,event:t});throw u})),h}findMatchingRoute({url:e,sameOrigin:t,request:s,event:n}){const r=this._routes.get(s.method)||[];for(const i of r){let c;const o=i.match({url:e,sameOrigin:t,request:s,event:n});if(o)return c=o,(Array.isArray(c)&&c.length===0||o.constructor===Object&&Object.keys(o).length===0||typeof o=="boolean")&&(c=void 0),{route:i,params:c}}return{}}setDefaultHandler(e,t=K){this._defaultHandlerMap.set(t,R(e))}setCatchHandler(e){this._catchHandler=R(e)}registerRoute(e){this._routes.has(e.method)||this._routes.set(e.method,[]),this._routes.get(e.method).push(e)}unregisterRoute(e){if(!this._routes.has(e.method))throw new l("unregister-route-but-not-found-with-method",{method:e.method});const t=this._routes.get(e.method).indexOf(e);if(t>-1)this._routes.get(e.method).splice(t,1);else throw new l("unregister-route-route-not-registered")}}let y;const J=()=>(y||(y=new z,y.addFetchListener(),y.addCacheListener()),y);function X(a,e,t){let s;if(typeof a=="string"){const r=new URL(a,location.href),i=({url:c})=>c.href===r.href;s=new w(i,e,t)}else if(a instanceof RegExp)s=new Q(a,e,t);else if(typeof a=="function")s=new w(a,e,t);else if(a instanceof w)s=a;else throw new l("unsupported-route-type",{moduleName:"workbox-routing",funcName:"registerRoute",paramName:"capture"});return J().registerRoute(s),s}function Y(a,e=[]){for(const t of[...a.searchParams.keys()])e.some(s=>s.test(t))&&a.searchParams.delete(t);return a}function*Z(a,{ignoreURLParametersMatching:e=[/^utm_/,/^fbclid$/],directoryIndex:t="index.html",cleanURLs:s=!0,urlManipulation:n}={}){const r=new URL(a,location.href);r.hash="",yield r.href;const i=Y(r,e);if(yield i.href,t&&i.pathname.endsWith("/")){const c=new URL(i.href);c.pathname+=t,yield c.href}if(s){const c=new URL(i.href);c.pathname+=".html",yield c.href}if(n){const c=n({url:r});for(const o of c)yield o.href}}class ee extends w{constructor(e,t){const s=({request:n})=>{const r=e.getURLsToCacheKeys();for(const i of Z(n.url,t)){const c=r.get(i);if(c){const o=e.getIntegrityForCacheKey(c);return{cacheKey:c,integrity:o}}}};super(s,e.strategy)}}function te(a){const e=v(),t=new ee(e,a);X(t)}const se="-precache-",ae=async(a,e=se)=>{const s=(await self.caches.keys()).filter(n=>n.includes(e)&&n.includes(self.registration.scope)&&n!==a);return await Promise.all(s.map(n=>self.caches.delete(n))),s};function ne(){self.addEventListener("activate",a=>{const e=C.getPrecacheName();a.waitUntil(ae(e).then(t=>{}))})}function re(a){v().precache(a)}function ie(a,e){re(a),te(e)}function ce(){self.addEventListener("activate",()=>self.clients.claim())}ie([{"revision":null,"url":"assets/Alat-DTahq9c5.js"},{"revision":null,"url":"assets/Alat-legacy-ZQwjXNxZ.js"},{"revision":null,"url":"assets/Alat-Vy52ge9T.css"},{"revision":null,"url":"assets/AlatDet-CpPgz90t.css"},{"revision":null,"url":"assets/AlatDet-ecQII-bj.js"},{"revision":null,"url":"assets/AlatDet-legacy-Dv-MfQFx.js"},{"revision":null,"url":"assets/DaftarAlat-DDpOQME2.css"},{"revision":null,"url":"assets/DaftarAlat-legacy-C9v_RWC6.js"},{"revision":null,"url":"assets/DaftarAlat-wmnz21Zd.js"},{"revision":null,"url":"assets/Dashboard-7zsWCFsM.css"},{"revision":null,"url":"assets/Dashboard-h6ngG_Eq.js"},{"revision":null,"url":"assets/Dashboard-legacy-CLmExr9O.js"},{"revision":null,"url":"assets/index-bdjAbwYg.css"},{"revision":null,"url":"assets/index-DYIZrBBo.js"},{"revision":null,"url":"assets/index-legacy-BUdDePUl.js"},{"revision":null,"url":"assets/Jadwal-DIeZB83T.js"},{"revision":null,"url":"assets/Jadwal-legacy-OcRvajBW.js"},{"revision":null,"url":"assets/Jadwal-p8zRJyOw.css"},{"revision":null,"url":"assets/LembarKerja-hFr2lISC.css"},{"revision":null,"url":"assets/LembarKerja-legacy-D5csLIv3.js"},{"revision":null,"url":"assets/LembarKerja-RJL_Trv_.js"},{"revision":null,"url":"assets/Pembayaran-Ci3OSjS2.js"},{"revision":null,"url":"assets/Pembayaran-legacy-mOeh50yl.js"},{"revision":null,"url":"assets/Pencarian-1cjZnzjt.css"},{"revision":null,"url":"assets/Pencarian-BWpWbKKF.js"},{"revision":null,"url":"assets/Pencarian-legacy-COaGmreZ.js"},{"revision":null,"url":"assets/Penerimaan-BS-gFkJ7.css"},{"revision":null,"url":"assets/Penerimaan-legacy-O9KiubfZ.js"},{"revision":null,"url":"assets/Penerimaan-tYHzBOkw.js"},{"revision":null,"url":"assets/Pengujian-gelqyqH7.js"},{"revision":null,"url":"assets/Pengujian-legacy-BdAqjN7P.js"},{"revision":null,"url":"assets/Persetujuan-BmvmMGBu.css"},{"revision":null,"url":"assets/Persetujuan-D7pAWGjH.js"},{"revision":null,"url":"assets/Persetujuan-DT5LrqYx.js"},{"revision":null,"url":"assets/Persetujuan-legacy-CaSrXWrH.js"},{"revision":null,"url":"assets/Persetujuan-legacy-CTlZu3TY.js"},{"revision":null,"url":"assets/Planning-DamOlA03.css"},{"revision":null,"url":"assets/Planning-FVT6ldrS.js"},{"revision":null,"url":"assets/Planning-legacy-D2166FiB.js"},{"revision":null,"url":"assets/polyfills-legacy-BQdD6ZPe.js"},{"revision":null,"url":"assets/Profile-B5As4wSY.js"},{"revision":null,"url":"assets/Profile-legacy-E3eg1zOs.js"},{"revision":null,"url":"assets/RevisiUlang-Bo20Rycx.js"},{"revision":null,"url":"assets/RevisiUlang-D8RqWVdf.css"},{"revision":null,"url":"assets/RevisiUlang-legacy-BbQFNjDd.js"},{"revision":null,"url":"assets/Sertifikat-D_EiOYSA.js"},{"revision":null,"url":"assets/Sertifikat-legacy-Dy3s8ZaI.js"},{"revision":null,"url":"assets/Sign-BGySdF8H.js"},{"revision":null,"url":"assets/Sign-Cbr41Y69.js"},{"revision":null,"url":"assets/Sign-legacy-CIkSMr_g.js"},{"revision":null,"url":"assets/Sign-legacy-nL9iNUgt.js"},{"revision":null,"url":"assets/Suhu-Dcgps8b6.js"},{"revision":null,"url":"assets/Suhu-legacy-Ci7CmBTk.js"},{"revision":null,"url":"assets/VListItemAction-Cb7Lha4G.js"},{"revision":null,"url":"assets/VListItemAction-legacy-B1rbhhFI.js"},{"revision":"bc4bd8065896a8a5933b66acf2ae96eb","url":"index.html"},{"revision":"1872c500de691dce40960bb85481de07","url":"registerSW.js"},{"revision":"f250f4162384b968452cd8bbeb7a59bc","url":"web/bootstrap.min.css"},{"revision":"74ea885d0932dede1c3e8ed18e456aa1","url":"web/jquery.js"},{"revision":"49c7c474cbe84e8253ec76b88e440fd0","url":"web/main.css"},{"revision":"4297babefac5c2ce75b60d0499150549","url":"web/owl.carousel.min.js"},{"revision":"6af0dcfe0fb296edbdff2817236d9d81","url":"manifest.webmanifest"}]);ne();let T=!1;self.addEventListener("message",a=>{a.data&&a.data.type==="SKIP_WAITING"&&(console.log("SW: Received SKIP_WAITING message"),T=!0,self.skipWaiting())});self.addEventListener("install",a=>{console.log("SW: Installing new service worker")});self.addEventListener("activate",a=>{console.log("SW: Activating new service worker"),T&&a.waitUntil(ce())});self.addEventListener("push",a=>{if(a.data)try{const e=a.data.json(),t={body:e.body,icon:"/img/notification-icon.png",badge:"/img/notification-badge.png",data:{url:e.url||"/"}};a.waitUntil(self.registration.showNotification(e.title,t))}catch(e){console.error("SW: Error handling push notification:",e)}});self.addEventListener("notificationclick",a=>{a.notification.close(),a.waitUntil(self.clients.openWindow(a.notification.data.url))});
