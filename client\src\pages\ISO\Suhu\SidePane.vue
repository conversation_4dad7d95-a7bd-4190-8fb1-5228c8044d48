<template>
  <div class="sidepane">
    <div style="padding: 10px; display: flex">
      <Input
        type="text"
        :value.sync="keyword"
        placeholder="Cari .."
        width="100%"
        rightIcon="mdi-magnify"
        class="searchbar"
      />
    </div>
    <div style="height: calc(100% - 47px)">
      <List
        dbref="ISO.SelRuang"
        :dbparams="dbparams"
        @itemClick="ItemClick"
        :height="addButton ? 'calc(100% - 60px)' : '100%'"
        :rebind="rebind"
      >
        <template v-slot="{ row }">
          <div style="font-size: 13px; margin-bottom: 10px; padding: 5px 10px">
            <div style="color: gray; float: right">
              {{ row.TglPencatatan }}
            </div>
            <div
              style="
                font-weight: bold;
                font-family: Raleway;
                text-transform: uppercase;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 180px;
                height: 20px;
                white-space: nowrap;
              "
            >
              {{ row.Nama<PERSON><PERSON> }}
            </div>
            <div style="color: gray; display: flex">
              <div
                style="
                  overflow: hidden;
                  text-overflow: ellipsis;
                  width: 100%;
                  height: 20px;
                  white-space: nowrap;
                "
              >
                {{ row.Hasil }}
              </div>
            </div>
          </div>
        </template>
      </List>
      <div style="padding: 10px; border-top: 1px solid #ddd">
        <v-btn
          outlined
          color="primary"
          style="width: calc(100% - 10px)"
          @click="DaftarBaru"
        >
          <v-icon left>mdi-plus</v-icon>
          TAMBAH ALAT
        </v-btn>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data: () => ({
    keyword: '',
  }),
  props: {
    statusId: String,
    rebind: Number,
    addButton: Boolean,
    filters: Object,
  },
  computed: {
    dbparams() {
      return { Keyword: this.keyword || '' }
    },
  },
  methods: {
    ItemClick(val) {
      this.$emit('item-click', val)
    },
    DaftarBaru() {
      this.$emit('item-click', { AlatId: '-' })
    },
  },
}
</script>
<style lang="scss">
.sidepane {
  padding: 0px !important;
  width: 300px;
  border-right: 1px solid #ddd;
  /*margin-left: -20px;*/
  height: calc(100vh - 64px);
  overflow: hidden;

  .searchbar {
    margin-bottom: 0;
    width: 100%;
    input {
      background: transparent !important;
      border-bottom: 0 !important;
    }
  }
}
</style>
