!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var i,r,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function s(t,n,a,o){var s=n&&n.prototype instanceof l?n:l,u=Object.create(s.prototype);return e(u,"_invoke",function(t,e,n){var a,o,s,l=0,u=n||[],h=!1,d={p:0,n:0,v:i,a:v,f:v.bind(i,4),d:function(t,e){return a=t,o=0,s=i,d.n=e,c}};function v(t,e){for(o=t,s=e,r=0;!h&&l&&!n&&r<u.length;r++){var n,a=u[r],v=d.p,p=a[2];t>3?(n=p===e)&&(s=a[(o=a[4])?5:(o=3,3)],a[4]=a[5]=i):a[0]<=v&&((n=t<2&&v<a[1])?(o=0,d.v=e,d.n=a[1]):v<p&&(n=t<3||a[0]>e||e>p)&&(a[4]=t,a[5]=e,d.n=p,o=0))}if(n||t>1)return c;throw h=!0,e}return function(n,u,p){if(l>1)throw TypeError("Generator is already running");for(h&&1===u&&v(u,p),o=u,s=p;(r=o<2?i:s)||!h;){a||(o?o<3?(o>1&&(d.n=-1),v(o,s)):d.n=s:d.v=s);try{if(l=2,a){if(o||(n="next"),r=a[n]){if(!(r=r.call(a,s)))throw TypeError("iterator result is not an object");if(!r.done)return r;s=r.value,o<2&&(o=0)}else 1===o&&(r=a.return)&&r.call(a),o<2&&(s=TypeError("The iterator does not provide a '"+n+"' method"),o=1);a=i}else if((r=(h=d.n<0)?s:t.call(e,d))!==c)break}catch(r){a=i,o=1,s=r}finally{l=1}}return{value:r,done:h}}}(t,a,o),!0),u}var c={};function l(){}function u(){}function h(){}r=Object.getPrototypeOf;var d=[][a]?r(r([][a]())):(e(r={},a,function(){return this}),r),v=h.prototype=l.prototype=Object.create(d);function p(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,e(t,o,"GeneratorFunction")),t.prototype=Object.create(v),t}return u.prototype=h,e(v,"constructor",h),e(h,"constructor",u),u.displayName="GeneratorFunction",e(h,o,"GeneratorFunction"),e(v),e(v,o,"Generator"),e(v,a,function(){return this}),e(v,"toString",function(){return"[object Generator]"}),(t=function(){return{w:s,m:p}})()}function e(t,i,r,n){var a=Object.defineProperty;try{a({},"",{})}catch(t){a=0}e=function(t,i,r,n){function o(i,r){e(t,i,function(t){return this._invoke(i,r,t)})}i?a?a(t,i,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[i]=r:(o("next",0),o("throw",1),o("return",2))},e(t,i,r,n)}function i(t,e,i,r,n,a,o){try{var s=t[a](o),c=s.value}catch(t){return void i(t)}s.done?e(c):Promise.resolve(c).then(r,n)}function r(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)}return i}function n(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?r(Object(i),!0).forEach(function(e){a(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):r(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function a(t,e,i){return(e=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}System.register(["./index-legacy-BUdDePUl.js","./AlatDet-legacy-Dv-MfQFx.js","./VListItemAction-legacy-B1rbhhFI.js"],function(e,r){"use strict";var a,s,c,l,u,h,d,v,p,m,g,f,y,b,_,S,w,z,x,k,j;return{setters:[function(t){a=t.x,s=t.M,c=t.y,l=t.T,u=t.I,h=t.z,d=t.A,v=t.B,p=t.C,m=t.D,g=t.n,f=t.o,y=t.r,b=t.s,_=t.E,S=t.u,w=t.F,z=t._,x=t.g},function(t){k=t.A},function(t){j=t._}],execute:function(){var r=document.createElement("style");r.textContent=".theme--light.v-image{color:rgba(0,0,0,.87)}.theme--dark.v-image{color:#fff}.v-image{z-index:0}.v-image__image,.v-image__placeholder{z-index:-1;position:absolute;top:0;left:0;width:100%;height:100%}.v-image__image{background-repeat:no-repeat}.v-image__image--preload{filter:blur(2px)}.v-image__image--contain{background-size:contain}.v-image__image--cover{background-size:cover}.v-responsive{position:relative;overflow:hidden;flex:1 0 auto;max-width:100%;display:flex}.v-responsive__content{flex:1 0 0px;max-width:100%}.v-application--is-ltr .v-responsive__sizer~.v-responsive__content{margin-left:-100%}.v-application--is-rtl .v-responsive__sizer~.v-responsive__content{margin-right:-100%}.v-responsive__sizer{transition:padding-bottom .2s cubic-bezier(.25,.8,.5,1);flex:1 0 0px}.v-avatar{align-items:center;border-radius:50%;display:inline-flex;justify-content:center;line-height:normal;position:relative;text-align:center;vertical-align:middle;overflow:hidden}.v-avatar img,.v-avatar svg,.v-avatar .v-icon,.v-avatar .v-image,.v-avatar .v-responsive__content{border-radius:inherit;display:inline-flex;height:inherit;width:inherit}\n/*$vite$:1*/",document.head.appendChild(r);var O=a(s).extend({name:"v-responsive",props:{aspectRatio:[String,Number],contentClass:String},computed:{computedAspectRatio:function(){return Number(this.aspectRatio)},aspectStyle:function(){return this.computedAspectRatio?{paddingBottom:1/this.computedAspectRatio*100+"%"}:void 0},__cachedSizer:function(){return this.aspectStyle?this.$createElement("div",{style:this.aspectStyle,staticClass:"v-responsive__sizer"}):[]}},methods:{genContent:function(){return this.$createElement("div",{staticClass:"v-responsive__content",class:this.contentClass},c(this))}},render:function(t){return t("div",{staticClass:"v-responsive",style:this.measurableStyles,on:this.$listeners},[this.__cachedSizer,this.genContent()])}}),A="undefined"!=typeof window&&"IntersectionObserver"in window,E=a(O,l).extend({name:"v-img",directives:{intersect:u},props:{alt:String,contain:Boolean,eager:Boolean,gradient:String,lazySrc:String,options:{type:Object,default:function(){return{root:void 0,rootMargin:void 0,threshold:void 0}}},position:{type:String,default:"center center"},sizes:String,src:{type:[String,Object],default:""},srcset:String,transition:{type:[Boolean,String],default:"fade-transition"}},data:function(){return{currentSrc:"",image:null,isLoading:!0,calculatedAspectRatio:void 0,naturalWidth:void 0,hasError:!1}},computed:{computedAspectRatio:function(){return Number(this.normalisedSrc.aspect||this.calculatedAspectRatio)},normalisedSrc:function(){return this.src&&"object"===o(this.src)?{src:this.src.src,srcset:this.srcset||this.src.srcset,lazySrc:this.lazySrc||this.src.lazySrc,aspect:Number(this.aspectRatio||this.src.aspect)}:{src:this.src,srcset:this.srcset,lazySrc:this.lazySrc,aspect:Number(this.aspectRatio||0)}},__cachedImage:function(){if(!(this.normalisedSrc.src||this.normalisedSrc.lazySrc||this.gradient))return[];var t=[],e=this.isLoading?this.normalisedSrc.lazySrc:this.currentSrc;this.gradient&&t.push("linear-gradient(".concat(this.gradient,")")),e&&t.push('url("'.concat(e,'")'));var i=this.$createElement("div",{staticClass:"v-image__image",class:{"v-image__image--preload":this.isLoading,"v-image__image--contain":this.contain,"v-image__image--cover":!this.contain},style:{backgroundImage:t.join(", "),backgroundPosition:this.position},key:+this.isLoading});return this.transition?this.$createElement("transition",{attrs:{name:this.transition,mode:"in-out"}},[i]):i}},watch:{src:function(){this.isLoading?this.loadImage():this.init(void 0,void 0,!0)},"$vuetify.breakpoint.width":"getSrc"},mounted:function(){this.init()},methods:{init:function(t,e,i){if(!A||i||this.eager){if(this.normalisedSrc.lazySrc){var r=new Image;r.src=this.normalisedSrc.lazySrc,this.pollForSize(r,null)}this.normalisedSrc.src&&this.loadImage()}},onLoad:function(){this.getSrc(),this.isLoading=!1,this.$emit("load",this.src),this.image&&(this.normalisedSrc.src.endsWith(".svg")||this.normalisedSrc.src.startsWith("data:image/svg+xml"))&&(this.image.naturalHeight&&this.image.naturalWidth?(this.naturalWidth=this.image.naturalWidth,this.calculatedAspectRatio=this.image.naturalWidth/this.image.naturalHeight):this.calculatedAspectRatio=1)},onError:function(){this.hasError=!0,this.$emit("error",this.src)},getSrc:function(){this.image&&(this.currentSrc=this.image.currentSrc||this.image.src)},loadImage:function(){var t=this,e=new Image;this.image=e,e.onload=function(){e.decode?e.decode().catch(function(e){d("Failed to decode image, trying to render anyway\n\n"+"src: ".concat(t.normalisedSrc.src)+(e.message?"\nOriginal error: ".concat(e.message):""),t)}).then(t.onLoad):t.onLoad()},e.onerror=this.onError,this.hasError=!1,this.sizes&&(e.sizes=this.sizes),this.normalisedSrc.srcset&&(e.srcset=this.normalisedSrc.srcset),e.src=this.normalisedSrc.src,this.$emit("loadstart",this.normalisedSrc.src),this.aspectRatio||this.pollForSize(e),this.getSrc()},pollForSize:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,r=function(){var n=t.naturalHeight,a=t.naturalWidth;n||a?(e.naturalWidth=a,e.calculatedAspectRatio=a/n):t.complete||!e.isLoading||e.hasError||null==i||setTimeout(r,i)};r()},genContent:function(){var t=O.options.methods.genContent.call(this);return this.naturalWidth&&this._b(t.data,"div",{style:{width:"".concat(this.naturalWidth,"px")}}),t},__genPlaceholder:function(){var t=c(this,"placeholder");if(t){var e=this.isLoading?[this.$createElement("div",{staticClass:"v-image__placeholder"},t)]:[];return this.transition?this.$createElement("transition",{props:{appear:!0,name:this.transition}},e):e[0]}}},render:function(t){var e=O.options.render.call(this,t),i=h(e.data,{staticClass:"v-image",attrs:{"aria-label":this.alt,role:this.alt?"img":void 0},class:this.themeClasses,directives:A?[{name:"intersect",modifiers:{once:!0},value:{handler:this.init,options:this.options}}]:void 0});return e.children=[this.__cachedSizer,this.__cachedImage,this.__genPlaceholder(),this.genContent()],t(e.tag,i,e.children)}}),I=a(p,s,v).extend({name:"v-avatar",props:{left:Boolean,right:Boolean,size:{type:[Number,String],default:48}},computed:{classes:function(){return n({"v-avatar--left":this.left,"v-avatar--right":this.right},this.roundedClasses)},styles:function(){return n({height:m(this.size),minWidth:m(this.size),width:m(this.size)},this.measurableStyles)}},render:function(t){var e={staticClass:"v-avatar",class:this.classes,style:this.styles,on:this.$listeners};return t("div",this.setBackgroundColor(this.color,e),c(this))}}),C=I.extend({name:"v-list-item-avatar",props:{horizontal:Boolean,size:{type:[Number,String],default:40}},computed:{classes:function(){return n(n({"v-list-item__avatar--horizontal":this.horizontal},I.options.computed.classes.call(this)),{},{"v-avatar--tile":this.tile||this.horizontal})}},render:function(t){var e=I.options.render.call(this,t);return e.data=e.data||{},e.data.staticClass+=" v-list-item__avatar",e}}),P={components:{AlatDet:k},data:function(){return{alatId:null,forms:{},showDet:!1,imageDet:"",activities:[]}},computed:n({},f({user:"getUser"})),created:function(){this.alatId=this.$route.params.id,this.populate()},methods:{populate:function(){var e,r=this;return(e=t().m(function e(){var i,n,a;return t().w(function(t){for(;;)switch(t.n){case 0:return t.n=1,r.$api.call("WEB.SelAlat",{AlatId:r.alatId});case 1:return n=t.v,r.forms=(null===(i=n.data)||void 0===i?void 0:i[0])||{},t.n=2,r.$api.call("WEB.SelAlatActivity",{AlatId:r.alatId});case 2:a=t.v,r.activities=a.data||[];case 3:return t.a(2)}},e)}),function(){var t=this,r=arguments;return new Promise(function(n,a){var o=e.apply(t,r);function s(t){i(o,n,a,s,c,"next",t)}function c(t){i(o,n,a,s,c,"throw",t)}s(void 0)})})()}}};e("default",g(P,function(){var t=this,e=t._self._c;return e("div",[e("br"),e("div",{staticStyle:{"background-color":"silver",width:"80vw",height:"80vw","border-radius":"10px",margin:"auto","background-position":"center","background-size":"cover"},style:{backgroundImage:"url(https://silakon.dpubinmarcipka.jatengprov.go.id"+t.forms.Foto+")"}}),e("br"),e("div",{staticStyle:{width:"100vw",padding:"15px"}},[e("div",{staticStyle:{"font-size":"x-large","font-family":"raleway"}},[t._v(" "+t._s(t.forms.NamaAlat)+" ")]),e("div",{staticStyle:{color:"gray"}},[t._v(" "+t._s(t.forms.Merk)+" / "+t._s(t.forms.Tipe)+" ("+t._s(t.forms.Tahun)+") ")]),e("br"),e(y,{attrs:{subheader:"","two-line":""}},t._l(t.activities,function(i){return e(b,{key:i.AlatId},[e(C,[e(E,{attrs:{src:"https://silakon.dpubinmarcipka.jatengprov.go.id"+i.Bukti}})],1),e(_,[e(S,[t._v(t._s(t._f("format")(i.Tanggal))+" - "+t._s(i.Aktifitas))]),e(w,[t._v(t._s(i.Keterangan))])],1),e(j,[e(z,{attrs:{icon:""},on:{click:function(e){t.imageDet=i.Bukti}}},[e(x,{attrs:{color:"grey lighten-1"}},[t._v("mdi-information")])],1)],1)],1)}),1),e("br"),t.user?e(z,{staticStyle:{width:"100%"},attrs:{color:"primary"},on:{click:function(e){t.showDet=!0}}},[t._v("TAMBAH DATA")]):t._e()],1),e("div",{directives:[{name:"show",rawName:"v-show",value:""!=t.imageDet,expression:"imageDet != ''"}],staticStyle:{width:"100vw",height:"100vw",position:"absolute",bottom:"0",background:"white","text-align":"center","padding-top":"10vw","border-radius":"20px 20px 0 0","box-shadow":"0 0 10px rgba(0, 0, 0, 0.3)"},on:{click:function(e){t.imageDet=""}}},[e("div",{staticStyle:{"background-color":"silver",width:"80vw",height:"80vw","border-radius":"10px",margin:"auto","background-position":"center","background-size":"cover"},style:{backgroundImage:"url(https://silakon.dpubinmarcipka.jatengprov.go.id"+t.imageDet+")"}}),e(z,{staticStyle:{margin:"auto"},attrs:{color:"primary",text:""}},[t._v("TUTUP")])],1),e("alat-det",{attrs:{show:t.showDet,alatId:t.alatId},on:{"update:show":function(e){t.showDet=e}}})],1)},[],!1,null,null).exports)}}})}();
