<template>
  <div
    style="
      position: fixed;
      width: 80vw;
      height: 90vh;
      background: #666;
      bottom: 0;
      border-radius: 20px 20px 0 0;
      z-index: 2;
    "
  >
    <iframe
      style="width: 100%; height: 100%; border-radius: 20px 20px 0 0"
      frameborder="0"
      @onload="loading = false"
      :src="this.$api.url + reportUrl"
    ></iframe>
  </div>
</template>
<script>
export default {
  data: () => ({
    loading: true,
  }),
  props: {
    reportUrl: String,
  },
  watch: {
    reportUrl() {
      this.loading = true
    },
  },
  mounted() {
    this.loading = true
  },
}
</script>
