const mysql = require('mysql')
const util = require('util')
const moment = require('moment')

const DB_NAME = 'bp2'

var pool = mysql.createPool({
  connectionLimit: 10,
  host: '*********',
  user: 'devusr',
  password: 'Node50123!',
  // host: process.env.DB_HOST,
  // user: process.env.DB_USER,
  // password: process.env.DB_PASS,
  database: DB_NAME,
})

pool.query = util.promisify(pool.query) // Magic happens here.
const ENCKEY = 'PTNJptNXl4R!KYn#re6U9qDn7Hga7Xk$'

async function aes_encrypt(content, key) {
  const ret = await pool.query(`CALL Arch_SelEncrypt(?, ?)`, [content, key])
  return ret[0][0].Content
}

async function buildQuery(sp_name, param) {
  sp_name = sp_name.replace(/[^a-z0-9_.]/ig, '')
  var dbp = await pool.query(
    `SELECT * FROM information_schema.PARAMETERS 
    WHERE SPECIFIC_SCHEMA = '${DB_NAME}' AND SPECIFIC_NAME = '${sp_name}' AND PARAMETER_MODE = 'IN'`
  )

  var sql = 'CALL ' + sp_name + '('
  var i = 0
  var run_param = []
  for (let idx in dbp) {
    var param_name = dbp[idx].PARAMETER_NAME //.substr(1);
    var param_value = param[param_name] || param[param_name.replace(/^_/, '')]
    if (param_name === '_EncKey') {
      sql += '?'
      run_param.push(ENCKEY)
    } else if (param_name === '_DeviceIdRef') {
      sql += '?'
      run_param.push(param._deviceId)
    } else if (param_name === '_IpAddrRef') {
      sql += '?'
      run_param.push(param._ip)
    } else if (param_value !== null && param_value !== undefined) {
      if (param_name.substr(0, 4) === '_Xml') {
        //$sql += `<xml>.str_replace("'", "", str_replace("{", "<'", str_replace("}", ">'", $_VAR[$dbp['PARAMETER_NAME']]))).</xml>`;
        sql += '?'
        run_param.push(
          `<xml>${param_value.replace(/{/gi, '<').replace(/}/gi, '>')}</xml>`
        )
      } else if (param_value === 'undefined' || param_value === 'null') {
        sql += '?'
        run_param.push(null)
      } else if (dbp[idx].DATA_TYPE == 'varbinary') {
        if (param_value.type === 'Buffer') {
          sql += '?'
          run_param.push(Buffer.from(param_value.data))
        } else if (param_value) {
          sql += '?'
          const encrypted = await aes_encrypt(param_value, ENCKEY)
          console.log(encrypted)
          run_param.push(Buffer.from(encrypted))
        } else {
          sql += '?'
          run_param.push(null)
        }
      } 
      // else if (param_value.type === 'Buffer' && dbp[idx].DATA_TYPE !== 'varbinary') {
      //   error = `invalid input for ${param_name.replace(/^_/, '')}`
      // } 
      else if (param_value === '' && (dbp[idx].DATA_TYPE == 'int' || dbp[idx].DATA_TYPE == 'bigint')) {
        sql += '?'
        run_param.push(null)
      } else if (dbp[idx].DATA_TYPE == 'date' && param_value) {
        sql += '?'
        run_param.push(param_value.replace('T', ' ').replace('.000Z', ''))
      } else if (dbp[idx].DATA_TYPE == 'datetime' && param_value) {
        sql += '?'
        run_param.push(param_value.replace('T', ' ').replace('.000Z', ''))
      } else {
        sql += '?'
        run_param.push(param_value)
      }
    } else if (param_name == '_UserIDRef') {
      sql += '?'
      //run_param.push(1);
      run_param.push(param._userId)
    } else {
      sql += '?'
      run_param.push(null)
    }

    i++
    if (i < dbp.length) sql += ','
  }
  sql += ')'
  return {sql: sql, params: run_param}
}

module.exports = {
  exec: async function (sp, params, opts = {}) {
    let start = moment()
    sp = sp.replace(/[^\w_.]/gi, '')
    // console.log(sp, params);
    const query = await buildQuery(sp, params)
    if (query.error) {
      return [
        {
          ErrCode: 100,
          Message: query.error,
          Error: query.error
        }
      ]
    }
    // console.log(query.sql, query.params)
    const result = await pool.query(query.sql, query.params).catch(err => {
      console.error(`Error on API CALL: (${sp}): ${err.message}`)
      console.log(query.params)
      const match = err.message.match(/column '_([^\n]+)'/)
      return [
        [
          {
            ErrCode: 100,
            Message: match
              ? `Error: ${match[1]} salah / harus diisi`
              : 'Error on API Call',
            Error: `(${sp}) ` + err.message
          }
        ]
      ]
    })
    let delta = moment().diff(start, 'second')
    if (delta > 5 && !sp.match(/Rpt/)) {
      console.error(`${sp} took ${delta} secs`)
    }
    // console.log(moment().diff(start, 'second'))
    if (opts.returnAll) {
      return result
    }
    if (result.length > 2) {
      if (result[0].length && result[0][0]._Settings) {
        return result
      }
      return result[result.length - 2]
    } else return result[0]
  },
  async getColumns(table) {
    table = table.replace(/[^a-z0-9_.]/ig, '')
    var result = await pool
      .query(
        `SELECT ORDINAL_POSITION, COLUMN_NAME, DATA_TYPE, COLUMN_TYPE, IS_NULLABLE FROM information_schema.COLUMNS c 
        WHERE TABLE_NAME = '${table}'`
      )
      .catch(err => {
        console.error(`Error on getColumns(${table}): ${err.message}`)
        return null
      })

    return result
  },
  async spHasPagination(sp_name) {
    sp_name = sp_name.replace(/[^a-z0-9_.]/ig, '')
    var dbp = await pool.query(
      `SELECT * FROM information_schema.PARAMETERS 
      WHERE SPECIFIC_SCHEMA = '${DB_NAME}' AND SPECIFIC_NAME = '${sp_name}' AND PARAMETER_MODE = 'IN'`
    )
    let has = false
    for (let idx in dbp) {
      if (dbp[idx].PARAMETER_NAME === '_PageRef') {
        has = true
        break
      }
    }
    return has
  },
  async query(sql) {
    var result = await pool.query(sql).catch(err => {
      console.error(`Error on Query: ${err.message}`)
      return [[{ErrCode: 100, Message: 'Error on Query', Error: err.message}]]
    })
    return result
  },
  esc: pool.escape,
  pool: pool,
  dbname: DB_NAME,
}
