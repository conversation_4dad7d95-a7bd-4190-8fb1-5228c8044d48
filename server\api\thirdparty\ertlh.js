const axios = require("axios");
var db = require("../../common/db");

const url = function(method) {
  return `http://datartlh.perumahan.pu.go.id/api/ertlh_${method}.php?api_key=zSjsYvl1h3sj01ve0UoX29lhWq7jkyDd4yOIpKRit54LJTjFlWg6Yal3o7Iy5uo6`;
};

module.exports = {
  async read(kdwilayah) {
    let d = await axios
      .get(url("read") + "&kdwilayah=" + kdwilayah)
      .catch((err) => {
        console.log(err);
      });
    // console.log(d);
    return d ? d.data : [];
  },
  async delete(nik, kode_wilayah) {
    await axios
      .post(url("delete"), {
        nik: nik,
        kode_wilayah: kode_wilayah,
      })
      .catch((err) => {
        console.log(err);
      });
  },
  async insert(nik) {
    let d = await db.exec("PRM_SelExportERTLH_NIK", {NIK: nik});
    if (d.length) {
      // console.log(url("insert"))
      // console.log(JSON.stringify(d[0]))
      let ret = await axios.post(url("insert"), d[0]).catch((err) => {});
      // console.log(ret)
    }
  },
};
