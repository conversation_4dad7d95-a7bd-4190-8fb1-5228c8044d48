<template>
  <div id="login-page">
    <div class="login-box">
      <div class="lbtitle">
        BALAI PENGUJIAN & PERALATAN
        <div style="font-size: small">PROVINSI JAWA TENGAH</div>
      </div>
      <div style="padding: 30px">
        <Input
          label="Nama"
          v-if="daftar"
          v-model:value="forms.name"
          class="inline"
          left-icon="face"
          width="100%"
        />
        <Input
          label="No.WhatsApp"
          v-model:value="forms.username"
          class="inline"
          left-icon="mdi-whatsapp"
          width="100%"
        />
        <v-btn
          v-show="forms.username.match(/[\d\+]+/) && confirmationBtn"
          color="primary"
          text
          style="width: 220px"
          @click="sendOtp"
        >
          {{ confirmationBtn }}
        </v-btn>
        <Input
          v-if="loginMethod != 'passkey'"
          :label="
            forms.username.match(/[\d\+]+/) ? 'Confirmation Code' : 'Password'
          "
          v-model:value="forms.password"
          type="password"
          left-icon="lock"
          width="100%"
        />
        <br />
        <v-btn
          v-if="loginMethod == 'passkey'"
          color="success"
          @click="loginWithPasskey"
          :loading="loading"
          style="width: 210px; margin-left: -5px"
        >
          <v-icon left>mdi-fingerprint</v-icon>
          LOGIN BIOMETRIK
        </v-btn>
        <v-btn
          v-else
          color="success"
          @click="submit"
          :loading="loading"
          :disabled="!forms.password"
          style="width: 210px; margin-left: -5px"
        >
          {{ daftar ? 'daftar' : 'login' }}
        </v-btn>
        <v-btn
          x-small
          text
          style="position: relatibe; top: 10px"
          v-if="daftar"
          @click="daftar = false"
        >
          login
        </v-btn>
        <v-btn
          x-small
          text
          style="position: relatibe; top: 10px"
          v-if="!daftar"
          @click="daftar = true"
        >
          daftar
        </v-btn>
        <v-btn
          x-small
          text
          color="success"
          style="position: relatibe; top: 10px; margin-left: 10px"
          v-if="!daftar"
          @click="
            loginMethod = loginMethod == 'passkey' ? 'password' : 'passkey'
          "
          :disabled="loading"
        >
          Login {{ loginMethod == 'passkey' ? 'OTP' : 'Biometrik' }}
        </v-btn>
      </div>
    </div>
    <!-- <NewsUpdate :show="showNews" /> -->

    <!-- Passkey Registration Modal -->
    <PasskeyRegistrationModal
      :show="showPasskeyRegistration"
      :userId="currentUserId"
      :username="forms.username"
      @registered="onPasskeyRegistered"
      @skipped="onPasskeyRegistrationSkipped"
      @update:show="showPasskeyRegistration = $event"
    />

    <img
      style="position: absolute; left: 35%; top: 12%; width: 100px"
      src="https://www.shareicon.net/data/2015/03/17/8496_new_256x256.png"
    />
  </div>
</template>

<script>
// import NewsUpdate from './NewsUpdate'
import PasskeyRegistrationModal from '@/components/PasskeyRegistrationModal.vue'
import { mapActions } from 'vuex'
import { startAuthentication } from '@simplewebauthn/browser'
export default {
  components: {
    // NewsUpdate,
    PasskeyRegistrationModal,
  },
  data: () => ({
    loading: false,
    daftar: false,
    confirmationBtn: 'KIRIM KODE KONFIRMASI',
    forms: {
      name: '',
      username: '',
      password: '',
    },
    showNews: false,
    showPasskeyRegistration: false,
    currentUserId: 0,
    loginMethod: null, // 'password' or 'passkey'
    userLogin: null,
  }),
  created() {
    let basics = localStorage.getItem('basics')
    localStorage.clear()
    if (basics) localStorage.setItem('basics', basics)
    sessionStorage.clear()

    basics = JSON.parse(basics)
    if (basics?.hasPasskeys) this.loginMethod = 'passkey'
    if (basics?.username) this.forms.username = basics.username

    // Check query string for mode parameter
    const urlParams = new URLSearchParams(window.location.search)
    const mode = urlParams.get('mode')
    if (mode === 'daftar') {
      this.daftar = true
    }
  },
  methods: {
    ...mapActions(['setMenu', 'setUser']),
    getListMenu(menu) {
      let mlist = {}
      menu.forEach((m) => {
        if (m.child && m.child.length) {
          if (m.MenuUrl) {
            mlist[m.MenuUrl] = this.getListMenu(m.child)
          } else mlist = Object.assign(mlist, this.getListMenu(m.child))
        } else if (m.MenuUrl) mlist[m.MenuUrl] = true
      })
      return mlist
    },
    async sendOtp() {
      this.confirmationBtn = ''
      let res = await this.$api.post(
        '/api/otp',
        {
          Name: this.forms.name,
          Phone: this.forms.username,
        },
        {
          notify: true,
        },
      )
      if (res.success) {
        setTimeout(() => {
          this.confirmationBtn = 'KIRIM KODE KONFIRMASI'
        }, 30000)
      } else {
        this.confirmationBtn = 'KIRIM KODE KONFIRMASI'
      }
    },
    async submit() {
      this.loading = true
      this.loginMethod = 'password'
      // this.forms.username = "test";
      let ret = await this.$api.login({
        name: this.forms.name,
        username: this.forms.username.replace(/^0/, '+62'),
        password: this.forms.password,
      })
      this.loading = false
      if (ret.length) {
        this.setUser(ret[0])
        this.currentUserId = ret[0].UserID
        this.userLogin = ret
        // Check if user has passkeys
        await this.checkUserPasskeys()

        if (!this.showPasskeyRegistration) {
          this.doLogin()
        }
      }
    },
    async doLogin() {
      if (!this.userLogin) return

      const ret = this.userLogin
      this.userLogin = null
      let menu = await this.$api.call('Arch_SelMenu', { UserId: 1 })
      if (menu) {
        this.setMenu(menu)
        localStorage.setItem(
          'menu-access',
          JSON.stringify(this.getListMenu(menu.data)),
        )
      }
      if (ret[0].HomeUrl) this.$router.push({ path: ret[0].HomeUrl })
      else this.$router.push('/Main/App/Home')
    },
    async loginWithPasskey() {
      this.loading = true
      this.loginMethod = 'passkey'
      try {
        // Get authentication options from server
        const optionsResponse = await this.$api.post('/api/passkey/login', {
          username: this.forms.username,
        })

        if (!optionsResponse.success) {
          this.$api.notify(
            optionsResponse.message || 'Gagal memulai autentikasi',
            'error',
          )
          return
        }

        // Start authentication with the browser
        const credential = await startAuthentication(optionsResponse.data)

        // Send credential to server for verification
        const verificationResponse = await this.$api.post(
          '/api/passkey/verify',
          {
            username: this.forms.username,
            credential,
          },
        )

        if (verificationResponse.success && verificationResponse.data) {
          let basics = localStorage.getItem('basics')
          basics = basics ? JSON.parse(basics) : {}
          basics.username = this.forms.username
          basics.hasBiometric = true
          localStorage.setItem('basics', JSON.stringify(basics))

          this.setUser(verificationResponse.data)
          if (verificationResponse.token) {
            this.$api.setToken(verificationResponse.token)
          }
          let menu = await this.$api.call('Arch_SelMenu')
          if (menu) {
            this.setMenu(menu)
            localStorage.setItem(
              'menu-access',
              JSON.stringify(this.getListMenu(menu.data)),
            )
            if (verificationResponse.data.HomeUrl)
              this.$router.push({ path: verificationResponse.data.HomeUrl })
            else this.$router.push('/Main/App/Home')
          }
          this.$api.notify('Login berhasil dengan biometrik!', 'success')
        } else {
          this.$api.notify(
            verificationResponse.message || 'Autentikasi gagal',
            'error',
          )
        }
      } catch (error) {
        console.error('FIDO2 authentication error:', error)
        if (error.name === 'NotAllowedError') {
          this.$api.notify(
            'Autentikasi dibatalkan atau tidak diizinkan',
            'error',
          )
        } else if (error.name === 'NotSupportedError') {
          this.$api.notify(
            'Perangkat tidak mendukung autentikasi biometrik',
            'error',
          )
        } else {
          this.$api.notify(
            'Gagal autentikasi biometrik: ' + error.message,
            'error',
          )
        }
      } finally {
        this.loading = false
      }
    },

    async checkUserPasskeys() {
      if (!this.currentUserId) return

      try {
        const response = await this.$api.post('/api/passkey/check', {
          userId: this.currentUserId,
        })

        if (response.success && !response.data.hasPasskeys) {
          // User doesn't have passkeys, show registration modal
          this.showPasskeyRegistration = true
        } else {
          let basics = localStorage.getItem('basics')
          basics = basics ? JSON.parse(basics) : {}
          basics.username = this.forms.username
          basics.hasPasskeys = true
          localStorage.setItem('basics', JSON.stringify(basics))
        }
      } catch (error) {
        console.error('Error checking user passkeys:', error)
        // Don't show error to user, just continue with login
      }
    },

    onPasskeyRegistered() {
      // Passkey was successfully registered
      this.$api.notify(
        'Passkey registered successfully! You can now use it for faster and more secure login.',
        'success',
      )
      this.doLogin()
    },

    onPasskeyRegistrationSkipped() {
      // User chose to skip passkey registration
      this.$api.notify(
        'Segera daftarkan login biometrik pada akun anda.',
        'info',
      )
      this.doLogin()
    },
  },
}
</script>
<style scoped>
.v-application--wrap {
  min-height: calc(100vh - 70px) !important;
  height: calc(100vh - 70px);
  overflow: hidden;
}
</style>
<style lang="scss">
.v-application {
  background: transparent !important;
}
.v-application.transparent {
  min-height: 100vh;
  background: transparent;
}
html {
  background-image: url('/imgs/bg.jpg') !important;
  background-position: 50% !important;
  background-size: cover !important;
  overflow: hidden !important;
}
#login-page {
  // background-image: url('/imgs/bg.jpg') !important;
  // background-position: 50% !important;
  // background-size: cover !important;
  position: absolute;
  // top: -65px;
  // left: -70px;
  width: 100%;
  height: 100vh;
}
.login-box {
  width: 300px;
  background: white;
  box-sizing: content-box;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  margin-left: 60%;
  margin-top: 12%;
  text-align: center;
  overflow: hidden;
  .lbtitle {
    font-family: 'Raleway';
    font-weight: 500;
    font-size: large;
    color: #333;
    border-left: 5px solid red;
    position: relative;
    padding-left: 20px;
    margin-top: 30px;
    margin-bottom: 0px;
    text-align: left;
    line-height: 20px;
  }
}

.is-mobile {
  #login-page {
    background-position: 2% -10% !important;
    background-size: 200% !important;
    background-color: #2f0c46 !important;
    top: -65px;
    left: 0;
  }
  .login-box {
    margin-top: 30vh;
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
