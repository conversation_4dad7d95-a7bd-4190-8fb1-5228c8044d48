import{n as o,i as l,Q as r,v as d,_ as n,l as m,j as u}from"./index-DYIZrBBo.js";const p={data:()=>({submitText:["BAYAR","OK"]}),props:{forms:Object},watch:{"forms.show"(s){s||(this.forms.step=0)}},methods:{async Submit(){if(this.forms.step==0)if(this.forms.PaymentType=="transfer"){let s=await this.$api.call("UJI_SavBillingID",this.forms);s.success&&(this.forms.billingId=s.data[0].BillingID,this.forms.expiredDate=d().add(2,"hour").format("DD-MMM-YYYY HH:mm:ss"),this.forms.step=1,this.$emit("refresh"))}else this.forms.step=1;else this.forms.show=!1}}};var c=function(){var t=this,a=t._self._c;return a(l,{attrs:{title:"Pembayaran",show:t.forms.show,submitText:t.submitText[t.forms.step]},on:{"update:show":function(e){return t.$set(t.forms,"show",e)},submit:t.Submit}},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.step==0,expression:"forms.step == 0"}],staticStyle:{padding:"5px"}},[t._v(" Pilih metode pembayaran: "),a("div",{staticStyle:{padding:"10px"}},[a(r,{attrs:{value:t.forms.PaymentType,data:"cash",text:"Tunai (datang ke tempat)"},on:{"update:value":function(e){return t.$set(t.forms,"PaymentType",e)}}}),a(r,{attrs:{value:t.forms.PaymentType,data:"transfer",text:"Transfer (Bank Jateng)"},on:{"update:value":function(e){return t.$set(t.forms,"PaymentType",e)}}})],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.step==1,expression:"forms.step == 1"}],staticStyle:{padding:"5px"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.PaymentType=="cash",expression:"forms.PaymentType == 'cash'"}]},[t._v(" Silahkan datang ke kantor BP2 untuk melakukan pembayaran. "),a("br"),t._v(' Permohonan akan tetap dalam status "Belum Dibayar"'),a("br"),t._v(" hingga nanti dibayarkan di loket BP2 ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.PaymentType=="transfer",expression:"forms.PaymentType == 'transfer'"}]},[t._v(" Silahkan melakukan transfer sejumlah: "),a("div",{staticStyle:{"font-size":"larger",background:"#f3f3f3",padding:"10px 15px","border-radius":"5px"}},[t._v(" Rp. "+t._s(t._f("format")(t.forms.total))+",- ")]),a("br"),a("div",[t._v("ke rekening Bank Jateng (Kode Bank: 113)")]),a("div",[t._v("dengan virtual account:")]),a("div",{staticStyle:{"font-size":"larger",background:"#f3f3f3",padding:"10px 15px","border-radius":"5px"}},[t._v(" "+t._s(t.forms.billingId)+" ")]),a("div",[t._v(" sebelum: "),a("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.forms.expiredDate))])]),a("div",[t._v(" (lihat "),a("a",{attrs:{href:"/web/TataCaraPembayaran.pdf",target:"_blank"}},[t._v("cara pembayaran")]),t._v(") ")])])])])},f=[],v=o(p,c,f,!1,null,null);const h=v.exports,_={components:{Bayar:h},data:()=>({billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0},datalist:[],statusId:",1,",rebind:1}),computed:{dbparams(){return{StatusID:this.statusId}}},watch:{"billing.show"(s){s||(this.rebind++,this.billing.TotalBayar=0)}},mounted(){this.statusId="1,2"},methods:{ItemClick(){this.billing.TotalBayar=0;let s=[];for(let t of this.datalist)t.checked&&(this.billing.TotalBayar+=t.TotalBayar,s.push(t.PermohonanID));this.billing.PermohonanID=s.join(",")},ChangeStatus(s){this.statusId=s},async ShowBilling(s){let t=await this.$api.call("UJI_SelBilling",{PermohonanID:s});t.success&&(this.billing={show:!0,step:1,total:t.data[0].TotalBayar,billingId:t.data[0].BillingID,PaymentType:"transfer"})}}};var y=function(){var t=this,a=t._self._c;return a("div",[a("div",{staticStyle:{display:"flex"}},[a("div",[a(n,{attrs:{text:t.statusId!="1,2",outlined:t.statusId!="1,2",color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("1,2")}}},[t._v(" Permohonan Baru ")])],1),a("div",[a(n,{attrs:{text:t.statusId!="3",outlined:t.statusId!="3",color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("3")}}},[t._v(" Penyerahan Sample ")])],1),a("div",[a(n,{attrs:{text:t.statusId!="3",outlined:t.statusId!="3",color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("3")}}},[t._v(" Menunggu Pembayaran ")])],1),a("div",[a(n,{attrs:{text:t.statusId!="4,5,6,7,8",outlined:t.statusId!="4,5,6,7,8",color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("4,5,6,7,8")}}},[t._v(" Dalam Proses ")])],1),a("div",[a(n,{attrs:{text:t.statusId!="9",outlined:t.statusId!="9",color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("9")}}},[t._v(" Sudah Selesai ")])],1)]),a("div",[a(m,{attrs:{dbref:"UJI_SelPermohonanList",items:t.datalist,dbparams:t.dbparams,rebind:t.rebind,selectOnLoad:!0},on:{"update:items":function(e){t.datalist=e}},scopedSlots:t._u([{key:"default",fn:function({row:e}){return[a("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"5px 10px 0 10px",display:"flex"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.statusId=="1,2",expression:"statusId == '1,2'"}],staticStyle:{padding:"8px 20px"}},[a(u,{attrs:{value:e.checked},on:{"update:value":function(i){return t.$set(e,"checked",i)},click:t.ItemClick}})],1),a("div",[a("div",[t._v(" "+t._s(e.NamaPelanggan)+" ")]),a("div",[t._v(t._s(e.NoPengujian)+" | "+t._s(e.JenisUji))])]),a("div",{staticStyle:{padding:"12px 20px","font-weight":"bold",width:"200px","text-align":"right"}},[t._v(" Rp. "+t._s(t._f("format")(e.TotalBayar))+" ")]),a("div",{staticStyle:{padding:"8px 20px"}},[t.statusId=="3"?a(n,{attrs:{text:"",outlined:"",small:"",color:"primary"},on:{click:function(i){return t.ShowBilling(e.PermohonanID)}}},[t._v(" BILLING ")]):a(n,{attrs:{text:"",outlined:"",small:"",color:"primary"}},[t._v("DETAIL")])],1)])]}}])}),a(n,{directives:[{name:"show",rawName:"v-show",value:this.billing.TotalBayar,expression:"this.billing.TotalBayar"}],staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:function(e){t.billing.show=!0}}},[t._v(" BAYAR: Rp "+t._s(t._f("format")(this.billing.TotalBayar))+",- ")])],1),a("Bayar",{attrs:{forms:t.billing},on:{"update:forms":function(e){t.billing=e},refresh:function(e){return t.$emit("refresh")}}})],1)},g=[],b=o(_,y,g,!1,null,null);const I=b.exports;export{I as default};
