<template>
  <Page title="Data Paket Parameter" style="width: 700px">
    <ParameterUji
      :show.sync="showDet"
      :forms="paramUji"
      :jenisId="params.JenisID"
      @submit="AddParameter"
    />
    <Select
      dbref="UJI.SelJenisUji"
      width="250px"
      @change="JenisUjiChanged"
    ></Select>
    <Grid
      :datagrid.sync="datagrid"
      dbref="UJI.PaketParamList"
      :dbparams="params"
      style="height: calc(100vh - 210px)"
      class="dense"
      :disabled="true"
      groupBy="NamaPaket"
      :doRebind="rebind"
      :columns="[
        {
          name: 'Parameter',
          value: 'Nama',
          width: '250px',
          editable: {
            com: 'Input',
          },
        },
        {
          name: 'Metode',
          value: 'Metode',
          width: '150px',
          editable: {
            com: 'Input',
          },
        },
        {
          name: '<PERSON>ak<PERSON>',
          value: 'Waktu',
          class: 'right',
          width: '80px',
          editable: {
            com: 'Input',
          },
        },
        {
          name: 'Harga',
          value: 'Harga',
          width: '120px',
          class: 'right',
          editable: {
            com: 'Input',
            type: 'number',
          },
        },
      ]"
    >
      <template v-slot:row-Waktu="{ row }"> {{ row.Waktu }} hari </template>
      <template v-slot:row-Harga="{ row }">
        Rp. {{ row.Harga | format }}
      </template>
      <template v-slot:group-row="{ row, columns }">
        <td :colspan="columns.length" style="background: #f3f3f3">
          <div style="padding: 5px 0; display: flex">
            <div style="font-weight: bold">{{ row.NamaPaket }}</div>
            <v-spacer />
            <v-btn x-small text color="primary" @click="OpenParameter(row)">
              UBAH PARAMETER
            </v-btn>
            <v-icon color="error" @click="Delete(row)">mdi-trash-can</v-icon>
          </div>
        </td>
      </template>
    </Grid>
    <v-btn v-show="params.JenisID" text @click="OpenParameter({})">
      <v-icon left>mdi-plus-circle</v-icon>
      TAMBAH PAKET
    </v-btn>
  </Page>
</template>
<script>
import ParameterUji from './ParameterUji.vue'
export default {
  components: {
    ParameterUji,
  },
  data: () => ({
    datagrid: [],
    rebind: 0,
    showDet: false,
    paramUji: {},
    params: {
      JenisUji: '',
      JenisID: null,
    },
  }),
  methods: {
    JenisUjiChanged(val) {
      this.params.JenisID = val
      this.rebind++
    },
    OpenParameter(row) {
      this.paramUji = row || {}
      this.showDet = true
    },
    AddParameter() {
      this.rebind++
    },
    async Delete(row) {
      if (!confirm('Hapus paket ini?')) return

      const ret = await this.$api.call('UJI_DelPaketParam', row)
      if (ret.success) {
        this.rebind++
      }
    },
  },
}
</script>
