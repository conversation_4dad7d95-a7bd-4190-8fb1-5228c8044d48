<template>
  <Modal
    title="Tambah Dokumen"
    :show.sync="xshow"
    :loading="loading"
    width="800px"
    @submit="Save"
  >
    <div style="width: 400px">
      <Input
        type="text"
        :value.sync="forms.DocName"
        label="Nama Dokumen"
        width="400px"
      />
      <Input type="text" :value.sync="forms.DocUrl" label="Url" width="400px" />
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    forms: {},
    loading: false,
  }),
  props: {
    show: Boolean,
  },
  watch: {
    show(val) {
      this.xshow = val
      if (!val) {
        this.forms = {}
      }
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    async Save() {
      this.loading = true
      let ret = await this.$api.call('ISO_SavPenjaminMutu', this.forms)
      if (ret.success) {
        this.$emit('save')
        this.$emit('update:show', false)
      }
      this.loading = false
    },
  },
}
</script>
