<template>
  <div>
    <div style="padding:0 10px;">
    <v-row>
      <v-col cols="12" lg="10" md="12">
        <v-chip
          :outlined="!chips['Mutu Air & Lingkungan']"
          rounded
          color="primary"
          style="margin-right: 8px"
          @click="toggleFilter('Mutu Air & Lingkungan')"
        >
          Mutu Air
        </v-chip>
        <v-chip
          :outlined="!chips['Bahan Bangunan']"
          rounded
          color="primary"
          style="margin-right: 8px"
          @click="toggleFilter('Bahan Bangunan')"
        >
          Bahan Bangunan
        </v-chip>
        <v-chip
          :outlined="!chips['Aspal']"
          rounded
          color="primary"
          style="margin-right: 8px"
          @click="toggleFilter('Aspal')"
        >
          Aspal
        </v-chip>
        <v-chip
          :outlined="!chips['Tanah (Geoteknik)']"
          rounded
          color="primary"
          style="margin-right: 8px"
          @click="toggleFilter('Tanah (Geoteknik)')"
        >
          Tanah
        </v-chip>
        <v-chip
          v-for="(p, idx) in parameters"
          :key="idx"
          rounded
          color="primary"
          style="margin-right: 8px"
          close
          class="xchip"
          @click:close="RemoveParameter(idx)"
        >
          {{ p.NamaParameter }}
        </v-chip>
        <v-chip
          outlined
          rounded
          color="primary"
          style="margin-right: 8px"
          @click="OpenParameter"
        >
          <v-icon left>mdi-filter</v-icon>
          Parameter
        </v-chip>
      </v-col>
      <v-col cols="12" lg="2" md="12" style="padding: 10px 0;">
        <Input
          type="text"
          placeholder="Cari..."
          :value.sync="keyword"
          width="100%"
          style="margin-bottom: 0"
          right-icon="mdi-magnify"
        />
      </v-col>
    </v-row>
    </div>
    <v-row class="grandview">
      <GrandViewCol :items="items.pendaftaran" title="PENDAFTARAN" />
      <GrandViewCol :items="items.pengesahan" title="PENGESAHAN" />
      <GrandViewCol :items="items.pembayaran" title="PEMBAYARAN" />
      <GrandViewCol :items="items.menunggu_pengujian" title="MENUNGGU" />
      <GrandViewCol :items="items.pengujian" title="PENGUJIAN" />
      <GrandViewCol :items="items.sertifikasi" title="SERTIFIKASI" />
    </v-row>
    <ParameterUji :forms="paramUji" :jenisId="jenisId" @submit="AddParameter" />
  </div>
</template>
<script>
import moment from 'moment'
import GrandViewCol from './GrandViewCol.vue'
import ParameterUji from '../Loket/ParameterUji.vue'
export default {
  components: {
    GrandViewCol,
    ParameterUji,
  },
  data: () => ({
    chartYear: new Date().getFullYear(),
    keyword: '',
    progress: {},
    datacollection: {},
    lastData: {},
    totalPAD: 0,
    chartVal: 0,
    datalist: [],
    rebind: 1,
    parameters: [],
    jenis: {
      'Mutu Air & Lingkungan': 'A',
      'Bahan Bangunan': 'B',
      Aspal: 'Ba',
      'Tanah (Geoteknik)': 'T',
    },
    paramUji: {
      show: false,
    },
    chips: {
      'Mutu Air & Lingkungan': true,
      'Bahan Bangunan': true,
      Aspal: true,
      'Tanah (Geoteknik)': true,
    },
    items: {
      pendaftaran: [],
      pengesahan: [],
      pembayaran: [],
      menunggu_pengujian: [],
      pengujian: [],
      sertifikasi: [],
    },
  }),
  computed: {
    yearOptions() {
      const years = []
      for (let i = 0; i < 5; i++) {
        years.push({
          val: new Date().getFullYear() - i,
          txt: new Date().getFullYear() - i,
        })
      }
      return years
    },
    jenisId() {
      let j = Object.keys(this.chips)
        .filter((k) => this.chips[k] === true)
        .map((k) => this.jenis[k])
      return j.length > 0 ? j[0] : null
    },
  },
  watch: {
    keyword() {
      this.doFilter()
    },
  },
  mounted() {
    this.populate()
  },
  methods: {
    async populate() {
      const ret = await this.$api.call('UJI_SelGrandView', {
        nocache: true,
      })
      this.datalist = ret.data.map((d) => {
        d.ParameterIds = JSON.parse(d.ParameterIds || '[]')
        return d
      })
      this.doFilter()
    },
    toggleFilter(jenis) {
      if (!this.chips[jenis]) this.chips[jenis] = true
      else this.chips[jenis] = false

      this.doFilter()
    },
    doFilter() {
      this.items.pendaftaran.splice(0, this.items.pendaftaran.length)
      this.items.pengesahan.splice(0, this.items.pengesahan.length)
      this.items.pembayaran.splice(0, this.items.pembayaran.length)
      this.items.menunggu_pengujian.splice(
        0,
        this.items.menunggu_pengujian.length
      )
      this.items.pengujian.splice(0, this.items.pengujian.length)
      this.items.sertifikasi.splice(0, this.items.sertifikasi.length)
      const paramIds = this.parameters.map((p) => p.ParameterID)

      for (let d of this.datalist) {
        let key = d.StatusName.toLowerCase().replace(' ', '_')
        if (!this.items[key]) this.items[key] = []

        // filter by jenis
        let isShow =
          this.chips[d.JenisUji] &&
          (d.NamaPelanggan.toLowerCase().includes(this.keyword.toLowerCase()) ||
            d.NoPengujian.toLowerCase().includes(this.keyword.toLowerCase()) ||
            !this.keyword)
        // this.items[key].push({...d, isShow})

        // filter by parameters
        if (isShow && paramIds?.length) {
          isShow = paramIds.some((id) => d.ParameterIds.includes(id))
        }

        if (isShow) this.items[key].push(d)
      }
      this.rebind++
    },
    getRowColor(row) {
      return !row.BayarDate && moment().diff(row.TglMasuk, 'day') > 14
        ? 'red'
        : '#333'
    },
    OpenParameter() {
      if (Object.keys(this.chips).filter((k) => this.chips[k]).length != 1) {
        alert('Harap pilih hanya satu jenis uji')
        return
      }
      this.paramUji.show = true
    },
    AddParameter(params) {
      this.parameters.push(...params)
      this.paramUji.show = false
      this.doFilter()
    },
    RemoveParameter(idx) {
      this.parameters.splice(idx, 1)
      this.doFilter()
    },
  },
}
</script>
<style lang="scss">
.xchip {
  padding-right: 30px;
  .v-chip__content {
    max-width: 100%;
    overflow: hidden;

    .v-icon {
      position: absolute;
      right: 12px;
    }
  }
}
.grandview {
  margin-top:0;
  // width: 300px;
  border-right: 1px solid #ddd;
  /*margin-left: -20px;*/
  min-height: 650px;
  overflow: hidden;
  padding-right: 0;
  background: white;
  padding: 12px;

  .searchbar {
    margin-bottom: 0;
    input {
      background: transparent !important;
      border-bottom: 0 !important;
    }
  }
  .--item {
    border-radius: 10px;
    margin-right: 10px;
    border: 1px solid #ddd;
    margin-bottom: 5px;
    .no-uji {
      font-size: 10px;
      padding: 3px 5px 0 5px;
      background: #ddd;
      border-radius: 5px;
      color: #333;
      margin-right: 5px;
    }
  }
  .--item.selected {
    .no-uji {
      background: white;
    }
  }
}
</style>
