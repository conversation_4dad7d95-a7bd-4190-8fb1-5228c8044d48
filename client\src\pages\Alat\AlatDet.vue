<template>
  <Modal
    title="Data Detail Alat"
    :show.sync="xshow"
    width="450px"
    @submit="Save"
  >
    <div style="min-width: 300px" class="rounded-forms">
      <Select
        style="width: 100%"
        width="100%"
        label="Jenis Aktifitas"
        :items="[
          { val: 'kali<PERSON><PERSON>', txt: '<PERSON><PERSON><PERSON>' },
          { val: 'pem<PERSON>haraan', txt: '<PERSON><PERSON><PERSON>haraan' },
          { val: 'kerusakan', txt: 'Kerusakan' },
        ]"
        :value.sync="forms.Aktifitas"
      />
      <DatePicker
        style="width: 100%"
        width="100%"
        label="Tanggal"
        :value.sync="forms.Tanggal"
      />
      <TextArea
        style="width: 100%"
        width="100%"
        label="Keterangan"
        :value.sync="forms.Keterangan"
      />
      <Uploader label="Bukti" :value.sync="forms.Bukti" />
    </div>
  </Modal>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  data: () => ({
    xshow: false,
    forms: {},
  }),
  props: {
    show: Bo<PERSON>an,
    alatId: [String, Number],
    aktifitas: String,
    tanggal: [String, Date],
  },
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
  },
  watch: {
    show(val) {
      this.xshow = val
      if (val) {
        if (this.aktifitas) this.forms.Aktifitas = this.aktifitas
        if (this.tanggal) this.forms.Tanggal = this.tanggal
      }
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    async Save() {
      let ret = await this.$api.call('PML.SavAlatActivity', {
        AlatId: this.alatId,
        ...this.forms,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.modal-data-detail-alat {
  .imgbox {
    margin: auto;
  }
}
</style>
