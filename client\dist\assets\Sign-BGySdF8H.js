import{n as s,a as n,l,_ as r,k as o}from"./index-DYIZrBBo.js";const c={data:()=>({keyword:""}),props:{statusId:String,rebind:Number,addButton:Boolean},computed:{dbparams(){return{ApprovalStep:1,Keyword:this.keyword||""}}},methods:{ItemClick(a){this.$emit("item-click",a)},DaftarBaru(){this.$emit("item-click",{PengujianID:0})}}};var d=function(){var t=this,e=t._self._c;return e("div",{staticClass:"sidepane"},[e("div",{staticStyle:{padding:"10px",display:"flex"}},[e(n,{staticClass:"searchbar",attrs:{type:"text",value:t.keyword,placeholder:"Cari ..",width:"270px",rightIcon:"mdi-magnify"},on:{"update:value":function(i){t.keyword=i}}})],1),e("div",{staticStyle:{height:"calc(100% - 47px)"}},[e(l,{attrs:{dbref:"UJI_SelPersetujuanLK",dbparams:t.dbparams,height:t.addButton?"calc(100% - 60px)":"100%",rebind:t.rebind,selectOnLoad:!0},on:{itemClick:t.ItemClick},scopedSlots:t._u([{key:"default",fn:function({row:i}){return[e("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"0 10px"}},[e("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(i.NamaPelanggan)+" ")]),e("div",{staticStyle:{color:"gray",display:"flex"}},[e("span",{staticStyle:{"font-size":"10px",padding:"3px 5px 0 5px",background:"#ddd","border-radius":"5px",color:"#333","margin-right":"5px"}},[t._v(" "+t._s(i.NoPengujian)+" ")]),e("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis",width:"160px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(i.Nama)+" ")])])])]}}])})],1)])},p=[],h=s(c,d,p,!1,null,null);const m=h.exports,_={components:{SidePane:m},data:()=>({rawUrlOri:"",rawUrl:"",forms:{},passphrase:"",showPassphrase:!1,loading:!1,rebind:1}),methods:{ShowSertifikat(a){this.forms=a,this.rawUrlOri=a.LkUrl,this.rawUrl=this.$api.url+a.LkUrl.replace(/(xlsb|xlsx?|docx?)$/,"pdf")},async Reject(){let a=prompt("Alasan?");a&&(await this.$api.call("UJI_SavLembarKerjaRejection",{LembarKerjaID:this.forms.LembarKerjaID,Alasan:a})).success&&(this.rawUrl="",this.rebind++)},async Refine(){this.loading=!0,await this.$api.get("/reports/uji/refine-lk/"+this.forms.LembarKerjaID),this.rawUrl=this.rawUrl+"?123",this.loading=!1},async Sign(){confirm("Setujui?")&&(await this.$api.call("UJI_SavLembarKerjaSign",{LembarKerjaID:this.forms.LembarKerjaID,FilePath:this.rawUrl})).success&&(this.rawUrl="",this.rebind++)}}};var f=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{attrs:{rebind:t.rebind},on:{"item-click":t.ShowSertifikat}}),t.rawUrl?e("div",{staticClass:"right-pane"},[e("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.rawUrl,frameborder:"0"}}),e("div",{staticStyle:{position:"fixed",bottom:"0",right:"20px",left:"calc(25% - 5px)",display:"flex",background:"rgba(255, 255, 255, 0.7)",padding:"20px"}},[e(r,{attrs:{color:"error",disabled:t.loading},on:{click:t.Reject}},[t._v(" TOLAK ")]),e(o),e(r,{directives:[{name:"show",rawName:"v-show",value:!t.loading,expression:"!loading"}],staticClass:"close-right-pane",staticStyle:{"margin-right":"8px"},on:{click:function(i){t.rawUrl=""}}},[t._v(" BATAL ")]),e(r,{attrs:{color:"primary",disabled:t.loading},on:{click:t.Sign}},[t._v(" SETUJUI ")])],1)]):t._e()],1)},u=[],g=s(_,f,u,!1,null,null);const x=g.exports;export{x as default};
