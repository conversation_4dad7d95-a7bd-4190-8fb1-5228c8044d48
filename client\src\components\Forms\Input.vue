<template>
  <div class="form-coms ui-input">
    <div
      class="form-label"
      :class="{ '--required': hasError }"
      v-if="$attrs.label"
    >
      {{ $attrs.label }}
    </div>
    <div
      class="--input"
      :style="{
        width: width,
      }"
    >
      <v-icon left v-if="leftIcon">{{ leftIcon }}</v-icon>
      <input
        :type="type"
        v-bind="$attrs"
        v-model="vmodel"
        class="--real"
        @click="handleClick($event)"
        @blur="handleBlur($event)"
        @keyup="handleKeyup($event)"
        @keydown="handleKeydown($event)"
        @input="handleInput($event)"
        @change="handleChange($event)"
        v-show="!toggleMask || isFocused || type != 'number'"
        @mouseleave="toggleMask = true"
        @focus="isFocused = true"
        :style="{
          'margin-left': leftIcon ? '-36px' : undefined,
          'padding-left': leftIcon ? '30px' : undefined,
          'padding-right': rightIcon ? '30px' : undefined,
        }"
      />
      <input
        v-if="type == 'number'"
        type="text"
        v-bind="$attrs"
        v-model="mask"
        v-show="toggleMask && !isFocused"
        @mouseenter="handleMouseEnter"
        :style="{
          'margin-left': leftIcon ? '-36px' : undefined,
          'padding-left': leftIcon ? '30px' : undefined,
          'padding-right': rightIcon ? '30px' : undefined,
          'text-align': type == 'number' ? 'right' : 'left',
        }"
      />
      <div class="postfix" v-if="postfix">{{ postfix }}</div>
      <v-icon right v-if="rightIcon">{{ rightIcon }}</v-icon>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Input',
  data: () => ({
    val: '',
    timeoutTracker: null,
    mask: '',
    toggleMask: true,
    isFocused: false,
  }),
  props: {
    value: [String, Number],
    width: String,
    leftIcon: String,
    rightIcon: String,
    postfix: String,
    format: String,
    type: {
      type: String,
      default: 'text',
    },
  },
  computed: {
    vmodel: {
      get() {
        return this.val || this.value
      },
      set(val) {
        this.val = val
        if (this.rightIcon != 'search') {
          if (this.type == 'number') {
            if (val && this.format != '#000')
              this.mask = parseFloat(val).toLocaleString()
            else this.mask = val
          }
          this.$emit('update:value', val)
        } else {
          clearTimeout(this.timeoutTracker)
          this.timeoutTracker = setTimeout(() => {
            this.$emit('update:value', val)
          }, 800)
        }
      },
    },
    hasError() {
      return (
        this.$attrs.label &&
        this.$attrs.label.match(/\*$/) &&
        (this.vmodel === null ||
          this.vmodel === undefined ||
          this.vmodel === '')
      )
    },
  },
  watch: {
    value(val) {
      this.val = val
      if (this.type == 'number') {
        if (val && this.format != '#000')
          this.mask = parseFloat(val).toLocaleString()
        else this.mask = val
      }
    },
  },
  created() {
    this.val = this.value
    if (this.type == 'number') {
      if (this.val && this.format != '#000')
        this.mask = parseFloat(this.val).toLocaleString()
      else this.mask = this.val
    }
  },
  methods: {
    handleBlur(evt) {
      this.$emit('blur', evt)
      this.isFocused = false
    },
    handleInput(evt) {
      this.$emit('input', this.vmodel, evt)
    },
    handleChange(evt) {
      this.$emit('change', this.vmodel, evt)
    },
    handleMouseEnter() {
      this.toggleMask = false
    },
    handleClick(evt) {
      this.$emit('click', evt)
    },
    handleKeyup(evt) {
      this.$emit('keyup', evt)
    },
    handleKeydown() {
      // if (
      //   this.type === 'number' &&
      //   evt.key.length === 1 &&
      //   !(String(this.val || '').replace(/,/g, '') + evt.key).match(
      //     /^\d{0,}.\d{0,}$/
      //   )
      // )
      //   evt.preventDefault()
    },
  },
}
</script>
<style lang="scss">
.ui-input {
  margin-bottom: 8px;
  font-size: 14px;

  .form-label {
    text-align: left;

    &.--required {
      color: red;
    }
  }
  .--input {
    width: 180px;

    input {
      padding: 6px 12px;
      border-radius: 6px;
      background: rgba(200, 200, 200, 0.2);
      border-bottom: 1px solid silver;
      width: 100%;
      color: #333;

      &[type='number'] {
        text-align: right;
      }
      &:hover,
      &:focus {
        border: 0;
        background: rgba(200, 200, 200, 0.4);
        border-bottom: 1px solid gray;
      }
    }
    .postfix,
    .prefix {
      padding: 4px 8px;
      border-bottom: 1px solid silver;
      background: #f3f3f3;
    }
    .v-icon--right {
      margin-left: 5px;
    }
    .v-icon--right {
      margin-left: -30px;
    }
  }
}
.dense {
  .ui-input {
    font-size: 14px;
  }
}
</style>
