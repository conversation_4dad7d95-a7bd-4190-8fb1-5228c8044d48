const axios = require("axios");
var db = require("../../common/db");

const creds = `user=disperakim&token=eIJCVbmZfYdusKiqqVDN`
const url = `https://caribdt.dinsos.jatengprov.go.id/api`

module.exports = {
  async getNIK(nik) {
    let d = await axios
      .post(`${url}/get_nik_perakim?${creds}&nik=${nik}`)
      .catch((err) => {
        console.log(err);
      });
      
    return d ? 
      (d.data.data ? d.data.data[0] : {}) : {};
  }
};
