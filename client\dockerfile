FROM nginx
RUN mkdir /app

RUN apt-get update && apt-get install -y \
	curl \
	python \
	make \
	g++
RUN curl -sL https://deb.nodesource.com/setup_0.12 | bash -
RUN apt-get update && apt-get install -y \
	nodejs

WORKDIR /tmp
RUN apt-get install -y libxinerama1 libfontconfig1 libdbus-glib-1-2 libcairo2 libcups2 libglu1-mesa libsm6 unzip wget
RUN wget http://downloadarchive.documentfoundation.org/libreoffice/old/*******/deb/x86_64/LibreOffice_*******_Linux_x86-64_deb.tar.gz -O libo.tar.gz
RUN tar -zxvf libo.tar.gz
WORKDIR /tmp/LibreOffice_*******_Linux_x86-64_deb/DEBS
RUN dpkg -i *.deb

RUN npm install -g nodemon
WORKDIR /app
COPY . /app
RUN npm install

COPY ./client /app/client
COPY ./nginx.conf /etc/nginx/nginx.conf