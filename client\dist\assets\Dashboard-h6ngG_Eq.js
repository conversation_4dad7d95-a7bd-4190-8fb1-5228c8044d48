import{x as B,H as D,J as $,T as w,K as x,C as T,y as I,g as s,D as k,n as h,L as P,N as g,r as d,l as _,s as r,E as u,u as c,F as o,t as b,q as y,_ as l,j as C,k as j,v as N,i as M,h as J}from"./index-DYIZrBBo.js";import{_ as f}from"./VListItemAction-Cb7Lha4G.js";const p=B(T,x(["left","bottom"]),w,$,D).extend({name:"v-badge",props:{avatar:Boolean,bordered:Boolean,color:{type:String,default:"primary"},content:{required:!1},dot:Boolean,label:{type:String,default:"$vuetify.badge"},icon:String,inline:Boolean,offsetX:[Number,String],offsetY:[Number,String],overlap:Boolean,tile:Boolean,transition:{type:String,default:"scale-rotate-transition"},value:{default:!0}},computed:{classes(){return{"v-badge--avatar":this.avatar,"v-badge--bordered":this.bordered,"v-badge--bottom":this.bottom,"v-badge--dot":this.dot,"v-badge--icon":this.icon!=null,"v-badge--inline":this.inline,"v-badge--left":this.left,"v-badge--overlap":this.overlap,"v-badge--tile":this.tile,...this.themeClasses}},computedBottom(){return this.bottom?"auto":this.computedYOffset},computedLeft(){return this.isRtl?this.left?this.computedXOffset:"auto":this.left?"auto":this.computedXOffset},computedRight(){return this.isRtl?this.left?"auto":this.computedXOffset:this.left?this.computedXOffset:"auto"},computedTop(){return this.bottom?this.computedYOffset:"auto"},computedXOffset(){return this.calcPosition(this.offsetX)},computedYOffset(){return this.calcPosition(this.offsetY)},isRtl(){return this.$vuetify.rtl},offset(){return this.overlap?this.dot?8:12:this.dot?2:4},styles(){return this.inline?{}:{bottom:this.computedBottom,left:this.computedLeft,right:this.computedRight,top:this.computedTop}}},methods:{calcPosition(e){return"calc(100% - ".concat(k(e||this.offset),")")},genBadge(){const e=this.$vuetify.lang,t=this.$attrs["aria-label"]||e.t(this.label),a=this.setBackgroundColor(this.color,{staticClass:"v-badge__badge",style:this.styles,attrs:{"aria-atomic":this.$attrs["aria-atomic"]||"true","aria-label":t,"aria-live":this.$attrs["aria-live"]||"polite",title:this.$attrs.title,role:this.$attrs.role||"status"},directives:[{name:"show",value:this.isActive}]}),n=this.$createElement("span",a,[this.genBadgeContent()]);return this.transition?this.$createElement("transition",{props:{name:this.transition,origin:this.origin,mode:this.mode}},[n]):n},genBadgeContent(){if(this.dot)return;const e=I(this,"badge");if(e)return e;if(this.content)return String(this.content);if(this.icon)return this.$createElement(s,this.icon)},genBadgeWrapper(){return this.$createElement("span",{staticClass:"v-badge__wrapper"},[this.genBadge()])}},render(e){const t=[this.genBadgeWrapper()],a=[I(this)],{"aria-atomic":n,"aria-label":i,"aria-live":m,role:v,title:ft,...S}=this.$attrs;return this.inline&&this.left?a.unshift(t):a.push(t),e("span",{staticClass:"v-badge",attrs:S,class:this.classes},a)}}),U={components:{Bayar:g,Pengajuan:P},data:()=>({showPengajuan:!1,billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0,step:0},permohonanId:0,itemMenu:[{text:"Detail"}],datalist:[],statusId:"1",rebind:1}),computed:{dbparams(){return{StatusID:this.statusId}}},watch:{"billing.show"(e){e||(this.rebind++,this.billing.TotalBayar=0)}},mounted(){this.statusId="1"},methods:{ItemClick(){this.billing.TotalBayar=0;let e=[];for(let t of this.datalist)t.checked&&(this.billing.TotalBayar+=t.TotalBayar,e.push(t.PermohonanID));this.billing.PermohonanID=e.join(",")},ChangeStatus(e){this.statusId=e},async ShowBilling(e){let t=await this.$api.call("UJI_SelBilling",{PermohonanID:e});t.success&&(this.billing={show:!0,step:1,total:t.data[0].TotalBayar,billingId:t.data[0].BillingID,PaymentType:"transfer"})},ShowDetail(e){this.permohonanId=e,this.showPengajuan=!0},async Delete(e){confirm("Hapus Pengujian?")&&(await this.$api.call("UJI_DelPermohonan",{PermohonanID:e,Keterangan:"Dihapus Pelanggan"}),this.rebind++,this.billing.TotalBayar=0)},ItemMenuClick(e,t){e.text=="Detail"?this.ShowDetail(t.PermohonanID):e.text=="Hapus"&&this.Delete(t.PermohonanID)},ClosePengajuan(){this.showPengajuan=!1,this.rebind++,this.billing.TotalBayar=0}}};var R=function(){var t=this,a=t._self._c;return a("div",[a("div",{directives:[{name:"show",rawName:"v-show",value:!t.datalist.length,expression:"!datalist.length"}],staticStyle:{padding:"50px",height:"calc(100vh - 156px)","text-align":"center"}},[t._v(" TIDAK ADA DATA PERMOHONAN BARU ")]),a(d,[a(_,{directives:[{name:"show",rawName:"v-show",value:t.datalist.length,expression:"datalist.length"}],staticStyle:{height:"calc(100vh - 136px)"},attrs:{dbref:"UJI_SelPermohonanList",items:t.datalist,dbparams:t.dbparams,rebind:t.rebind,selectOnLoad:!0},on:{"update:items":function(n){t.datalist=n}},scopedSlots:t._u([{key:"default",fn:function({row:n}){return[a(r,[a(u,[a(c,[t._v(t._s(n.NamaPelanggan))]),a(o,[t._v(" "+t._s(n.NoPengujian)+" | "+t._s(n.JenisUji)+" ")]),a(o,{staticStyle:{"font-weight":"bold"}},[t._v(" Rp. "+t._s(t._f("format")(n.TotalBayar))+" ")])],1),a(b,[a(y,{attrs:{"offset-y":""},scopedSlots:t._u([{key:"activator",fn:function({on:i}){return[a(s,t._g({},i),[t._v(" mdi-dots-vertical")])]}}],null,!0)},[a(d,{attrs:{dense:""}},t._l(t.itemMenu,function(i,m){return a(r,{key:m,on:{click:function(v){return t.ItemMenuClick(i,n)}}},[a(u,{style:{color:i.color||"#333"}},[a(c,[t._v(t._s(i.text))])],1)],1)}),1)],1)],1)],1)]}}])})],1),a("div",{staticClass:"bottom-panel"},[a(l,{staticClass:"btn-full",staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:function(n){t.showPengajuan=!0}}},[a(s,{attrs:{left:""}},[t._v("mdi-plus")]),t._v(" PERMOHONAN BARU ")],1)],1),a("Bayar",{attrs:{forms:t.billing},on:{"update:forms":function(n){t.billing=n},refresh:function(n){return t.$emit("refresh")}}}),t.showPengajuan?a("Pengajuan",{attrs:{permohonanId:t.permohonanId},on:{close:t.ClosePengajuan}}):t._e()],1)},L=[],A=h(U,R,L,!1,null,null);const O=A.exports,E={components:{Bayar:g,Pengajuan:P},data:()=>({showPengajuan:!1,billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0,step:0},permohonanId:0,itemMenu:[{text:"Detail"}],datalist:[],statusId:"2",rebind:1}),computed:{dbparams(){return{StatusID:this.statusId}}},watch:{"billing.show"(e){e||(this.rebind++,this.billing.TotalBayar=0)}},mounted(){this.statusId="2"},methods:{ItemClick(){this.billing.TotalBayar=0;let e=[];for(let t of this.datalist)t.checked&&(this.billing.TotalBayar+=t.TotalBayar,e.push(t.PermohonanID));this.billing.PermohonanID=e.join(",")},ChangeStatus(e){this.statusId=e},async ShowBilling(e){let t=await this.$api.call("UJI_SelBilling",{PermohonanID:e});t.success&&(this.billing={show:!0,step:1,total:t.data[0].TotalBayar,billingId:t.data[0].BillingID,PaymentType:"transfer"})},ShowDetail(e){this.permohonanId=e,this.showPengajuan=!0},async Delete(e){confirm("Hapus Pengujian?")&&(await this.$api.call("UJI_DelPermohonan",{PermohonanID:e,Keterangan:"Dihapus Pelanggan"}),this.rebind++,this.billing.TotalBayar=0)},ItemMenuClick(e,t){e.text=="Detail"?this.ShowDetail(t.PermohonanID):e.text=="Hapus"&&this.Delete(t.PermohonanID)},ClosePengajuan(){this.showPengajuan=!1,this.rebind++,this.billing.TotalBayar=0}}};var Y=function(){var t=this,a=t._self._c;return a("div",[t.datalist.length?a("div",{staticStyle:{padding:"20px 50px","text-align":"center","background-color":"#f3f3f3"}},[t._v(" Anda memiliki waktu "),a("b",[t._v("14 hari")]),t._v(" untuk menyerahkan contoh uji ")]):a("div",{staticStyle:{padding:"50px",height:"calc(100vh - 156px)","text-align":"center"}},[t._v(" TIDAK ADA PERMOHONAN YANG BUTUH DIKIRIMKAN ")]),a(d,[a(_,{directives:[{name:"show",rawName:"v-show",value:t.datalist.length,expression:"datalist.length"}],staticStyle:{height:"calc(100vh - 199px)"},attrs:{dbref:"UJI_SelPermohonanList",items:t.datalist,dbparams:t.dbparams,rebind:t.rebind,selectOnLoad:!0},on:{"update:items":function(n){t.datalist=n}},scopedSlots:t._u([{key:"default",fn:function({row:n}){return[a(r,[a(u,[a(c,[t._v(t._s(n.NamaPelanggan))]),a(o,[t._v(" "+t._s(n.NoPengujian)+" | "+t._s(n.JenisUji)+" ")]),a(o,{staticStyle:{"font-weight":"bold"}},[t._v(" Rp. "+t._s(t._f("format")(n.TotalBayar))+" ")])],1),a(b,[a(y,{attrs:{"offset-y":""},scopedSlots:t._u([{key:"activator",fn:function({on:i}){return[a(s,t._g({},i),[t._v(" mdi-dots-vertical")])]}}],null,!0)},[a(d,{attrs:{dense:""}},t._l(t.itemMenu,function(i,m){return a(r,{key:m,on:{click:function(v){return t.ItemMenuClick(i,n)}}},[a(u,{style:{color:i.color||"#333"}},[a(c,[t._v(t._s(i.text))])],1)],1)}),1)],1)],1)],1)]}}])})],1),a("div",{staticStyle:{position:"fixed",bottom:"0px",padding:"10px 0",width:"calc(100vw - 33px)"}},[a(l,{directives:[{name:"show",rawName:"v-show",value:this.billing.TotalBayar,expression:"this.billing.TotalBayar"}],staticClass:"btn-full",staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:function(n){t.billing.show=!0}}},[t._v(" BAYAR: Rp "+t._s(t._f("format")(this.billing.TotalBayar))+",- ")])],1),a("Bayar",{attrs:{forms:t.billing},on:{"update:forms":function(n){t.billing=n},refresh:function(n){return t.$emit("refresh")}}}),t.showPengajuan?a("Pengajuan",{attrs:{permohonanId:t.permohonanId},on:{close:t.ClosePengajuan}}):t._e()],1)},H=[],K=h(E,Y,H,!1,null,null);const F=K.exports,X={components:{Bayar:g},data:()=>({billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0,step:0},itemMenu:[{text:"Detail Pembayaran"}],datalist:[],statusId:"3",rebind:1}),computed:{dbparams(){return{StatusID:this.statusId}}},watch:{"billing.show"(e){e||(this.Populate(),this.billing.TotalBayar=0)}},mounted(){this.Populate()},methods:{ItemClick(){this.billing.TotalBayar=0;let e=[];for(let t of this.datalist)t.checked&&(this.billing.TotalBayar+=t.TotalBayar,e.push(t.PermohonanID));this.billing.PermohonanID=e.join(",")},async Populate(){let e=await this.$api.call("UJI_SelPermohonanList",{StatusID:this.statusId}),t=[],a="xxx";for(let n=0;n<e.data.length;n++){if(!e.data[n].BillingID||e.data[n].BillingID!=a)t.push(e.data[n]);else{let i=t[t.length-1];i.NamaPelanggan+=", "+e.data[n].NamaPelanggan,i.TotalBayar+=e.data[n].TotalBayar,i.JenisUji!=e.data[n].JenisUji&&(i.JenisUji+=e.data[n].JenisUji)}a=e.data[n].BillingID}this.datalist=t},ItemMenuClick(e,t){e.text=="Detail Pembayaran"&&this.ShowBilling(t.PermohonanID)},async ShowBilling(e){let t=await this.$api.call("UJI_SelBilling",{PermohonanID:e});t.success&&(this.billing={show:!0,step:1,total:t.data[0].TotalBayar,billingId:t.data[0].BillingID,expiredDate:N(t.data[0].ExpiredDate).add(7,"hour").format("DD-MMM-YYYY HH:mm:ss"),PaymentType:"transfer"})}}};var G=function(){var t=this,a=t._self._c;return a("div",[a(_,{staticStyle:{height:"calc(100vh - 194px)"},attrs:{items:t.datalist,selectOnLoad:!0},scopedSlots:t._u([{key:"default",fn:function({row:n}){return[a(r,[a(f,{directives:[{name:"show",rawName:"v-show",value:!n.BillingID,expression:"!row.BillingID"}]},[a(C,{attrs:{value:n.checked},on:{"update:value":function(i){return t.$set(n,"checked",i)},click:t.ItemClick}})],1),a(u,[a(c,[t._v(t._s(n.NamaPelanggan))]),a(o,[t._v(" "+t._s(n.NoPengujian)+" | "+t._s(n.JenisUji)+" ")]),a(o,{staticStyle:{display:"flex"}},[a("div",{staticStyle:{"font-weight":"bold"}},[t._v(" Rp. "+t._s(t._f("format")(n.TotalBayar))+" ")]),a(j),n.BayarDate?a("div",{staticStyle:{color:"green"}},[t._v("Sudah Dibayar")]):n.BillingID?a("div",{staticStyle:{color:"orangered"}},[t._v(" Menunggu Pembayaran ")]):a("div",{staticStyle:{color:"red"}},[t._v("Belum Dibayar")])],1)],1),a(b,{directives:[{name:"show",rawName:"v-show",value:n.BillingID,expression:"row.BillingID"}]},[a(y,{attrs:{"offset-y":""},scopedSlots:t._u([{key:"activator",fn:function({on:i}){return[a(s,t._g({},i),[t._v(" mdi-dots-vertical")])]}}],null,!0)},[a(d,{attrs:{dense:""}},t._l(t.itemMenu,function(i,m){return a(r,{key:m,on:{click:function(v){return t.ItemMenuClick(i,n)}}},[a(u,{style:{color:i.color||"#333"}},[a(c,[t._v(t._s(i.text))])],1)],1)}),1)],1)],1)],1)]}}])}),a("div",{staticStyle:{height:"40px"}},[a(l,{directives:[{name:"show",rawName:"v-show",value:t.billing.TotalBayar,expression:"billing.TotalBayar"}],staticClass:"btn-full",staticStyle:{"margin-left":"5px","margin-top":"-50px"},attrs:{color:"primary"},on:{click:function(n){t.billing.show=!0}}},[t._v(" BAYAR: Rp "+t._s(t._f("format")(this.billing.TotalBayar))+",- ")])],1),a("Bayar",{attrs:{forms:t.billing},on:{"update:forms":function(n){t.billing=n},refresh:function(n){return t.$emit("refresh")}}})],1)},V=[],q=h(X,G,V,!1,null,null);const z=q.exports,W={data:()=>({datagrid:[],rebind:0,dbparams:{PermohonanID:0}}),props:{data:Object},watch:{"data.PermohonanID"(e){this.dbparams.PermohonanID=e,this.rebind++}}};var Q=function(){var t=this,a=t._self._c;return a(M,{attrs:{title:"Detail Pengujian",show:t.data.show},on:{"update:show":function(n){return t.$set(t.data,"show",n)}}},[a(J,{attrs:{datagrid:t.datagrid,dbref:"UJI.PermohonanDet",dbparams:t.dbparams,disabled:!0,doRebind:t.rebind,columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Nama Contoh",value:"NamaContoh"},{name:"Metode",value:"Metode"},{name:"Masalah",value:"Masalah"},{name:"Status",value:"StatusName"}]},on:{"update:datagrid":function(n){t.datagrid=n}}})],1)},Z=[],tt=h(W,Q,Z,!1,null,null);const at=tt.exports,et={components:{ProsesDetail:at},data:()=>({detail:{show:!1,PermohonanID:""},datalist:[],statusId:"4,5,6,7,8",rebind:1}),computed:{dbparams(){return{StatusID:this.statusId}}},watch:{"billing.show"(e){e||(this.rebind++,this.billing.TotalBayar=0)}},methods:{async ShowDetail(e){this.detail.show=!0,this.detail.PermohonanID=e}}};var nt=function(){var t=this,a=t._self._c;return a("div",[a(d,[a(_,{directives:[{name:"show",rawName:"v-show",value:t.datalist.length,expression:"datalist.length"}],staticStyle:{height:"calc(100vh - 136px)"},attrs:{dbref:"UJI_SelPermohonanList",items:t.datalist,dbparams:t.dbparams,rebind:t.rebind,selectOnLoad:!0},on:{"update:items":function(n){t.datalist=n}},scopedSlots:t._u([{key:"default",fn:function({row:n}){return[a(r,[a(u,[a(c,[t._v(t._s(n.NamaPelanggan))]),a(o,[t._v(" "+t._s(n.NoPengujian)+" | "+t._s(n.JenisUji)+" ")]),a(o,{staticStyle:{"font-weight":"bold"}},[t._v(" Rp. "+t._s(t._f("format")(n.TotalBayar))+" ")])],1),a(f,[a(l,{attrs:{small:"",outlined:"",color:"primary"},on:{click:function(i){return t.ShowDetail(n.PermohonanID)}}},[t._v("DETAIL")])],1)],1)]}}])})],1),a("ProsesDetail",{attrs:{data:t.detail}})],1)},it=[],st=h(et,nt,it,!1,null,null);const lt=st.exports,ot={components:{Bayar:g},data:()=>({billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0,step:0},datalist:[],statusId:"4,5,6,7,8",rebind:1}),computed:{dbparams(){return{StatusID:this.statusId}}},watch:{"billing.show"(e){e||(this.rebind++,this.billing.TotalBayar=0)}},methods:{async ShowLembarKerja(e){window.open(this.$api.url+"/"+e.LkSignedUrl,"_blank")},async ShowSertifikat(e){window.open(this.$api.url+e.SignedUrl.replace(/xlsx/,"pdf"),"_blank")}}};var rt=function(){var t=this,a=t._self._c;return a("div",[a(d,[a(_,{directives:[{name:"show",rawName:"v-show",value:t.datalist.length,expression:"datalist.length"}],staticStyle:{height:"calc(100vh - 136px)"},attrs:{dbref:"UJI_SelPermohonanList",items:t.datalist,dbparams:t.dbparams,rebind:t.rebind,selectOnLoad:!0},on:{"update:items":function(n){t.datalist=n}},scopedSlots:t._u([{key:"default",fn:function({row:n}){return[a(r,[a(u,[a(c,[t._v(t._s(n.NamaPelanggan))]),a(o,[n.LkNama?a("span",[t._v(t._s(n.LkNama.replace(/\.pdf$/,"")))]):a("span",[t._v(t._s(n.NoPengujian))]),t._v(" | "+t._s(n.JenisUji)+" ")]),a(o,{staticStyle:{"font-weight":"bold"}},[t._v(" SELESAI ")])],1),a(f,[n.LkSignedUrl?a(l,{attrs:{small:"",outlined:"",color:"primary"},on:{click:function(i){return t.ShowLembarKerja(n)}}},[a(s,[t._v("mdi-file-cad")])],1):t._e()],1),a(f,[n.SignedUrl?a(l,{attrs:{small:"",outlined:"",color:"primary"},on:{click:function(i){return t.ShowSertifikat(n)}}},[a(s,[t._v("mdi-certificate")])],1):t._e()],1)],1)]}}])})],1),a("Bayar",{attrs:{forms:t.billing},on:{"update:forms":function(n){t.billing=n}}})],1)},ut=[],ct=h(ot,rt,ut,!1,null,null);const dt=ct.exports,ht={components:{PermohonanBaru:O,PenyerahanSample:F,MenungguPembayaran:z,DalamProses:lt,Selesai:dt},data:()=>({billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0},datalist:[],statusId:"1",rebind:1,counter:{}}),computed:{dbparams(){return{StatusID:this.statusId}}},watch:{"billing.show"(e){e||(this.rebind++,this.billing.TotalBayar=0)}},mounted(){this.statusId="1",this.GetCounter()},methods:{ItemClick(){this.billing.TotalBayar=0;let e=[];for(let t of this.datalist)t.checked&&(this.billing.TotalBayar+=t.TotalBayar,e.push(t.PermohonanID));this.billing.PermohonanID=e.join(",")},ChangeStatus(e){this.statusId=e},async GetCounter(){let e=await this.$api.call("UJI_SelPermohonanCount",{NoCache:!0});this.counter=e.data[0]},async ShowBilling(e){let t=await this.$api.call("UJI_SelBilling",{PermohonanID:e});t.success&&(this.billing={show:!0,step:1,total:t.data[0].TotalBayar,billingId:t.data[0].BillingID,PaymentType:"transfer"})}}};var mt=function(){var t=this,a=t._self._c;return a("div",{staticClass:"main-panel"},[a("div",{staticStyle:{display:"flex",background:"white"}},[a(l,{staticStyle:{width:"20%"},attrs:{text:t.statusId!="1",outlined:t.statusId!="1",color:"primary",elevation:"0",tile:""},on:{click:function(n){return t.ChangeStatus("1")}}},[a(p,{attrs:{color:"error",content:t.counter.JmlPermohonan,value:0}},[t.$api.isMobile()?a(s,[t._v("mdi-file-outline")]):a("span",[t._v("Permohonan Baru")])],1)],1),a(l,{staticStyle:{width:"20%"},attrs:{text:t.statusId!="2",outlined:t.statusId!="2",color:"primary",elevation:"0",tile:""},on:{click:function(n){return t.ChangeStatus("2")}}},[a(p,{attrs:{color:"error",content:t.counter.JmlMenungguSample,value:t.counter.JmlMenungguSample>0}},[t.$api.isMobile()?a(s,[t._v("mdi-paperclip")]):a("span",[t._v("Penyerahan Sample")])],1)],1),a(l,{staticStyle:{width:"20%"},attrs:{text:t.statusId!="3",outlined:t.statusId!="3",color:"primary",elevation:"0",tile:""},on:{click:function(n){return t.ChangeStatus("3")}}},[a(p,{attrs:{color:"error",content:t.counter.JmlMenungguBayar,value:t.counter.JmlMenungguBayar>0}},[t.$api.isMobile()?a(s,[t._v("mdi-hand-coin-outline")]):a("span",[t._v("Menunggu Pembayaran")])],1)],1),a(l,{staticStyle:{width:"20%"},attrs:{text:t.statusId!="4,5,6,7,8",outlined:t.statusId!="4,5,6,7,8",color:"primary",elevation:"0",tile:""},on:{click:function(n){return t.ChangeStatus("4,5,6,7,8")}}},[a(p,{attrs:{color:"error",content:t.counter.JmlDalamProses,value:t.counter.JmlDalamProses>0}},[t.$api.isMobile()?a(s,[t._v("mdi-flask-outline")]):a("span",[t._v("Dalam Proses")])],1)],1),a(l,{staticStyle:{width:"20%"},attrs:{text:t.statusId!="9",outlined:t.statusId!="9",color:"primary",elevation:"0",tile:""},on:{click:function(n){return t.ChangeStatus("9")}}},[a(p,{attrs:{color:"error",content:t.counter.JmlSelesai,value:t.counter.JmlSelesai>0}},[t.$api.isMobile()?a(s,[t._v("mdi-check-decagram")]):a("span",[t._v("Sudah Selesai")])],1)],1)],1),t.statusId=="1"?a("PermohonanBaru",{on:{refresh:t.GetCounter}}):t._e(),t.statusId=="2"?a("PenyerahanSample"):t._e(),t.statusId=="3"?a("MenungguPembayaran"):t._e(),t.statusId=="4,5,6,7,8"?a("DalamProses"):t._e(),t.statusId=="9"?a("Selesai"):t._e()],1)},pt=[],_t=h(ht,mt,pt,!1,null,null);const bt=_t.exports;export{bt as default};
