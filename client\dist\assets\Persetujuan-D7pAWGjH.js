import{n as s,a as n,l,_ as r,k as o}from"./index-DYIZrBBo.js";const d={data:()=>({keyword:""}),props:{statusId:String,rebind:Number,addButton:Boolean},computed:{dbparams(){return{ApprovalStep:0,Keyword:this.keyword||""}}},methods:{ItemClick(t){this.$emit("item-click",t)},DaftarBaru(){this.$emit("item-click",{PengujianID:0})}}};var c=function(){var a=this,e=a._self._c;return e("div",{staticClass:"sidepane"},[e("div",{staticStyle:{padding:"10px",display:"flex"}},[e(n,{staticClass:"searchbar",attrs:{type:"text",value:a.keyword,placeholder:"Cari ..",width:"270px",rightIcon:"mdi-magnify"},on:{"update:value":function(i){a.keyword=i}}})],1),e("div",{staticStyle:{height:"calc(100% - 47px)"}},[e(l,{attrs:{dbref:"UJI_SelPersetujuanLK",dbparams:a.dbparams,height:a.addButton?"calc(100% - 60px)":"100%",rebind:a.rebind,selectOnLoad:!0},on:{itemClick:a.ItemClick},scopedSlots:a._u([{key:"default",fn:function({row:i}){return[e("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"0 10px"}},[e("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[a._v(" "+a._s(i.NamaPelanggan)+" ")]),e("div",{staticStyle:{color:"gray",display:"flex"}},[e("span",{staticStyle:{"font-size":"10px",padding:"3px 5px 0 5px",background:"#ddd","border-radius":"5px",color:"#333","margin-right":"5px"}},[a._v(" "+a._s(i.NoPengujian)+" ")]),e("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis",width:"160px",height:"20px","white-space":"nowrap"}},[a._v(" "+a._s(i.Nama)+" ")])])])]}}])})],1)])},p=[],h=s(d,c,p,!1,null,null);const m=h.exports,_={components:{SidePane:m},data:()=>({rawUrlOri:"",rawUrl:"",forms:{},passphrase:"",showPassphrase:!1,loading:!1,rebind:1}),methods:{ShowSertifikat(t){this.forms=t,this.rawUrlOri=t.LkUrl,this.rawUrl=this.$api.url+t.LkUrl.replace(/(xlsx|docx)$/,"pdf")},async Reject(){let t=prompt("Alasan?");t&&(await this.$api.call("UJI_SavLembarKerjaRejection",{LembarKerjaID:this.forms.LembarKerjaID,Alasan:t})).success&&(this.rawUrl="",this.rebind++)},async Refine(){this.loading=!0,await this.$api.get("/reports/uji/refine-lk/"+this.forms.LembarKerjaID),this.rawUrl=this.rawUrl+"?123",this.loading=!1},Download(){window.open(this.$api.url+this.rawUrlOri,"_blank")},async Sign(){confirm("Setujui?")&&(await this.$api.call("UJI_SavLembarKerjaApproval",{LembarKerjaID:this.forms.LembarKerjaID,FilePath:this.rawUrlOri})).success&&(this.rawUrl="",this.rebind++)}}};var f=function(){var a=this,e=a._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{attrs:{rebind:a.rebind},on:{"item-click":a.ShowSertifikat}}),a.rawUrl?e("div",{staticClass:"right-pane"},[e("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:a.rawUrl,frameborder:"0"}}),e("div",{staticStyle:{position:"fixed",bottom:"0",right:"20px",left:"calc(25% - 5px)",display:"flex",background:"rgba(255, 255, 255, 0.7)",padding:"20px"}},[e(r,{attrs:{color:"error",disabled:a.loading},on:{click:a.Reject}},[a._v(" TOLAK ")]),e(o),e(r,{directives:[{name:"show",rawName:"v-show",value:!a.rawUrl.match(/\?123/),expression:"!rawUrl.match(/\\?123/)"}],attrs:{text:"",disabled:a.loading},on:{click:a.Refine}},[a._v(" REFINE ")]),e(r,{attrs:{text:"",color:"success",disabled:a.loading},on:{click:a.Download}},[a._v(" EXCEL ")]),e(r,{directives:[{name:"show",rawName:"v-show",value:!a.loading,expression:"!loading"}],staticStyle:{"margin-right":"8px"},on:{click:function(i){a.rawUrl=""}}},[a._v(" BATAL ")]),e(r,{attrs:{color:"primary",disabled:a.loading},on:{click:a.Sign}},[a._v(" SETUJUI ")])],1)]):a._e()],1)},u=[],w=s(_,f,u,!1,null,null);const v=w.exports;export{v as default};
