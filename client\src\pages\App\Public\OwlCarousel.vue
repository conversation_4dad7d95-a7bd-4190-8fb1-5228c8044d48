<template>
  <section id="main-slider">
    <div class="owl-carousel">
      <div
        v-for="(item, idx) in banner"
        :key="idx"
        class="item"
        v-show="idx == activeImage"
        :style="`background-image: url(https://silakon.dpubinmarcipka.jatengprov.go.id${item.background});`"
      >
        <div class="slider-inner">
          <div class="container">
            <div class="row" style="margin: 0">
              <div class="col-sm-6">
                <div class="carousel-content">
                  <h2 v-show="item.title">
                    <span>{{ item.title }}</span>
                  </h2>
                  <v-btn
                    color="info"
                    style="font-size: 14px"
                    v-show="item.buttonText"
                    onclick="${obj.buttonAction}"
                  >
                    {{ item.buttonText }}
                  </v-btn>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<script>
export default {
  data: () => ({
    banner: [],
    activeImage: 0,
  }),
  async mounted() {
    let b = await this.$api.call('WEB_SelBanner')
    this.banner = b.data
    // setTimeout(() => {
    //   const plugin = document.createElement('script')
    //   plugin.setAttribute('src', '/web/owl.carousel.min.js')
    //   plugin.async = true
    //   document.head.appendChild(plugin)
    //   setTimeout(this.initSlider, 3000)
    // }, 1000)
    setInterval(this.rotate, 5000)
  },
  methods: {
    async rotate() {
      this.activeImage = (this.activeImage + 1) % this.banner.length
    },
    async initSlider() {
      // var $bar, $elem, isPause, tick, percentTime, $progressBar
      var $ = window.$

      //Init progressBar where elem is $("#owl-demo")
      window.progressBar = function (elem) {
        window.$elem = elem
        //build progress bar elements
        window.buildProgressBar()
        //start counting
        window.start()
      }

      //create div#progressBar and div#bar then append to $(".owl-carousel")
      window.buildProgressBar = function () {
        window.$progressBar = $('<div>', {
          id: 'progressBar',
        })
        window.$bar = $('<div>', {
          id: 'bar',
        })
        window.$progressBar.append(window.$bar).appendTo(window.$elem)
      }

      window.start = function () {
        //reset timer
        window.percentTime = 0
        window.isPause = false
        //run interval every 0.01 second
        window.tick = setInterval(window.interval, 10)
      }

      window.interval = function () {
        if (window.isPause === false) {
          window.percentTime += 1 / 7
          window.$bar.css({
            width: window.percentTime + '%',
          })
          //if percentTime is equal or greater than 100
          if (window.percentTime >= 100) {
            window.percentTime = 0
            //slide to next item
            window.$elem.trigger('owl.next')
          }
        }
      }

      //pause while dragging
      window.pauseOnDragging = function () {
        window.isPause = true
      }

      //moved callback
      window.moved = function () {}

      //Init the carousel
      $('.owl-carousel').owlCarousel({
        slideSpeed: 500,
        paginationSpeed: 500,
        singleItem: true,
        navigation: true,
        navigationText: [
          "<i class='fa fa-angle-left'></i>",
          "<i class='fa fa-angle-right'></i>",
        ],
        afterInit: window.progressBar,
        afterMove: () => {
          console.log(window.tick)
          //clear interval
          clearTimeout(window.tick)
          //start again
          window.start()
        },
        startDragging: window.pauseOnDragging,
        //autoHeight : true,
        transitionStyle: 'fadeUp',
      })
    },
  },
}
</script>
<style lang="scss">
.carousel-content {
  h2 {
    span {
      margin-top: 180px;
      font-size: 36px;
      line-height: 1;
      text-transform: uppercase;
      color: #fff;
      background: rgba(0, 0, 0, 0.7);
      padding: 0 10px;
    }
  }
}
</style>
