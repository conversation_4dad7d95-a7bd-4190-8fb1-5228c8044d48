!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,n,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",o=i.toStringTag||"@@toStringTag";function c(t,i,a,o){var c=i&&i.prototype instanceof l?i:l,d=Object.create(c.prototype);return r(d,"_invoke",function(t,r,i){var a,o,c,l=0,d=i||[],u=!1,p={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,r){return a=t,o=0,c=e,p.n=r,s}};function f(t,r){for(o=t,c=r,n=0;!u&&l&&!i&&n<d.length;n++){var i,a=d[n],f=p.p,h=a[2];t>3?(i=h===r)&&(c=a[(o=a[4])?5:(o=3,3)],a[4]=a[5]=e):a[0]<=f&&((i=t<2&&f<a[1])?(o=0,p.v=r,p.n=a[1]):f<h&&(i=t<3||a[0]>r||r>h)&&(a[4]=t,a[5]=r,p.n=h,o=0))}if(i||t>1)return s;throw u=!0,r}return function(i,d,h){if(l>1)throw TypeError("Generator is already running");for(u&&1===d&&f(d,h),o=d,c=h;(n=o<2?e:c)||!u;){a||(o?o<3?(o>1&&(p.n=-1),f(o,c)):p.n=c:p.v=c);try{if(l=2,a){if(o||(i="next"),n=a[i]){if(!(n=n.call(a,c)))throw TypeError("iterator result is not an object");if(!n.done)return n;c=n.value,o<2&&(o=0)}else 1===o&&(n=a.return)&&n.call(a),o<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),o=1);a=e}else if((n=(u=p.n<0)?c:t.call(r,p))!==s)break}catch(n){a=e,o=1,c=n}finally{l=1}}return{value:n,done:u}}}(t,a,o),!0),d}var s={};function l(){}function d(){}function u(){}n=Object.getPrototypeOf;var p=[][a]?n(n([][a]())):(r(n={},a,function(){return this}),n),f=u.prototype=l.prototype=Object.create(p);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,u):(t.__proto__=u,r(t,o,"GeneratorFunction")),t.prototype=Object.create(f),t}return d.prototype=u,r(f,"constructor",u),r(u,"constructor",d),d.displayName="GeneratorFunction",r(u,o,"GeneratorFunction"),r(f),r(f,o,"Generator"),r(f,a,function(){return this}),r(f,"toString",function(){return"[object Generator]"}),(t=function(){return{w:c,m:h}})()}function r(t,e,n,i){var a=Object.defineProperty;try{a({},"",{})}catch(t){a=0}r=function(t,e,n,i){function o(e,n){r(t,e,function(t){return this._invoke(e,n,t)})}e?a?a(t,e,{value:n,enumerable:!i,configurable:!i,writable:!i}):t[e]=n:(o("next",0),o("throw",1),o("return",2))},r(t,e,n,i)}function e(t,r,e,n,i,a,o){try{var c=t[a](o),s=c.value}catch(t){return void e(t)}c.done?r(s):Promise.resolve(s).then(n,i)}System.register(["./index-legacy-BUdDePUl.js"],function(r,n){"use strict";var i,a,o,c,s;return{setters:[function(t){i=t.n,a=t.a,o=t.l,c=t._,s=t.g}],execute:function(){var n=document.createElement("style");n.textContent=".sidepane{padding:0!important;width:300px;border-right:1px solid #ddd;height:calc(100vh - 64px);overflow:hidden}.sidepane .searchbar{margin-bottom:0}.sidepane .searchbar input{background:transparent!important;border-bottom:0!important}\n/*$vite$:1*/",document.head.appendChild(n);var l={data:function(){return{keyword:""}},props:{statusId:String,rebind:Number,addButton:Boolean},computed:{dbparams:function(){return{IsApproved:0,Keyword:this.keyword||""}}},methods:{ItemClick:function(t){this.$emit("item-click",t)},DaftarBaru:function(){this.$emit("item-click",{PengujianID:0})}}},d={components:{SidePane:i(l,function(){var t=this,r=t._self._c;return r("div",{staticClass:"sidepane"},[r("div",{staticStyle:{padding:"10px",display:"flex"}},[r(a,{staticClass:"searchbar",attrs:{type:"text",value:t.keyword,placeholder:"Cari ..",width:"270px",rightIcon:"mdi-magnify"},on:{"update:value":function(r){t.keyword=r}}})],1),r("div",{staticStyle:{height:"calc(100% - 47px)"}},[r(o,{attrs:{dbref:"UJI_SelTandaTanganList",dbparams:t.dbparams,height:t.addButton?"calc(100% - 60px)":"100%",rebind:t.rebind,selectOnLoad:!0},on:{itemClick:t.ItemClick},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"0 10px"}},[r("div",{staticStyle:{color:"gray",float:"right"}},[t._v(" "+t._s(t._f("format")(n.TglMasuk))+" ")]),r("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(n.NamaPelanggan)+" ")]),r("div",{staticStyle:{color:"gray",float:"right"}},[t._v(" "+t._s(n.StatusID>=8&&1!=n.BayarStatus?"BELUM BAYAR":n.StatusName)+" ")]),r("div",{staticStyle:{color:"gray",display:"flex"}},[r("span",{staticStyle:{"font-size":"10px",padding:"3px 5px 0 5px",background:"#ddd","border-radius":"5px",color:"#333","margin-right":"5px"}},[t._v(" "+t._s(n.NoPengujian)+" ")]),r("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis",width:"160px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(n.NamaLK||"-")+" ")])])])]}}])}),t.addButton?r("div",{staticStyle:{padding:"10px","border-top":"1px solid #ddd"}},[r(c,{staticStyle:{width:"calc(100% - 10px)"},attrs:{outlined:"",color:"primary"},on:{click:t.DaftarBaru}},[r(s,{attrs:{left:""}},[t._v("mdi-plus")]),t._v(" DAFTAR BARU ")],1)],1):t._e()],1)])},[],!1,null,null).exports},data:function(){return{rawUrlOri:"",rawUrl:"",forms:{},passphrase:"",showPassphrase:!1,loading:!1}},methods:{ShowSertifikat:function(t){this.forms=t,this.rawUrlOri=t.RawUrl,this.rawUrl=this.$api.url+t.RawUrl.replace(/(xlsx|docx)$/,"pdf")},Sign:function(){var r,n=this;return(r=t().m(function r(){return t().w(function(t){for(;;)switch(t.n){case 0:confirm("Setujui?")&&n.$api.call("UJI_SavSertifikatApproval",{PermohonanID:n.forms.PermohonanID,FilePath:n.rawUrlOri}).success&&(n.rawUrl="");case 1:return t.a(2)}},r)}),function(){var t=this,n=arguments;return new Promise(function(i,a){var o=r.apply(t,n);function c(t){e(o,i,a,c,s,"next",t)}function s(t){e(o,i,a,c,s,"throw",t)}c(void 0)})})()}}};r("default",i(d,function(){var t=this,r=t._self._c;return r("div",{staticStyle:{display:"flex"}},[r("SidePane",{on:{"item-click":t.ShowSertifikat}}),t.rawUrl?r("div",{staticClass:"right-pane"},[r("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.rawUrl,frameborder:"0"}}),r("div",{staticStyle:{position:"fixed",bottom:"20px",right:"40px",display:"flex"}},[r(c,{directives:[{name:"show",rawName:"v-show",value:!t.loading,expression:"!loading"}],staticClass:"close-right-pane",staticStyle:{"margin-right":"8px"},on:{click:function(r){t.rawUrl=""}}},[t._v(" BATAL ")]),r(c,{attrs:{color:"primary",disabled:t.loading},on:{click:t.Sign}},[t._v(" SETUJUI ")])],1)]):t._e()],1)},[],!1,null,null).exports)}}})}();
