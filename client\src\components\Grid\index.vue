<template>
  <component :is="comtype" v-bind="$props" v-on="$listeners" class="grid-table">
    <!-- Pass on all named slots -->
    <slot v-for="slot in Object.keys($slots)" :name="slot" :slot="slot" />
    <!-- Pass on all named slots -->
    <template
      v-for="(_, name) in $scopedSlots"
      :slot="name"
      slot-scope="slotData"
      ><slot :name="name" v-bind="slotData"
    /></template>
  </component>
</template>
<script>
import GridDefault from './Default.vue'
import GridMobile from './Mobile.vue'
export default {
  components: {
    GridDefault,
    GridMobile,
  },
  props: {
    id: {
      type: String,
      default: () => {
        if (!window.uuid) window.uuid = 0
        return 'tbl-' + window.uuid++
      },
    },
    datagrid: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      default: () => [],
    },
    filter: Function,
    disabled: {
      type: <PERSON>olean,
      default: false,
    },
    editMode: {
      type: Array,
      default: () => [],
    },
    dbref: String,
    dbparams: Object,
    height: String,
    width: String,
    groupBy: String,
    onEdit: Function,
    selectedRow: Number,
    autopaging: {
      type: Boolean,
      default: true,
    },
    preHead: {
      type: Boolean,
      default: false,
    },
    doPrint: {
      type: Number,
      default: 0,
    },
    doRebind: {
      type: Number,
      default: 0,
    },
    hasChild: {
      type: Boolean,
      value: false,
    },
  },
  computed: {
    isMobile() {
      return window.innerWidth < window.innerHeight ? 'is-mobile' : ''
    },
    comtype() {
      return this.isMobile ? 'GridMobile' : 'GridDefault'
    },
  },
}
</script>
