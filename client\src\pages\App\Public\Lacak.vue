<template>
  <section id="cta" class="wow fadeIn">
    <div class="container">
      <div class="row" style="margin: 0">
        <div class="col-sm-6">
          <h2>LACAK PROSES PENGUJIAN</h2>
          <p>Masukkan Kode Pendaftaran Pengujian:</p>
          <div style="display: flex">
            <input
              v-model="forms.ShareCode"
              type="text"
              class="front-input"
              width="100%"
              style="margin-right: 10px"
            />
            <v-btn @click="track" color="info" style="font-size: 14px">
              LACAK !
            </v-btn>
          </div>
          <div id="dvcaptcha" style="display: none">
            <div id="recaptcha"></div>
          </div>
          <div id="trackingerr" style="display: none"></div>
          <div id="trackingresult" style="display: none">
            <h3 style="color: white; margin-bottom: 0">
              {{ forms.NamaPelanggan }}
            </h3>
            <p>
              <span style="float: right">{{ forms.StatusName }}</span>
              <span>{{ forms.NoPengujian }}</span> <i class="fa fa-circle"></i>
              <span>{{ forms.JenisUji }}</span>
            </p>
            <div
              id="tracker"
              style="padding: 5px; background: white; height: 20px"
            ></div>
            <p id="UjiDet"></p>
          </div>
        </div>
        <div class="col-sm-6">
          <img
            src="https://satudja.kemenkeu.go.id/files/images/logo-bsre.png"
            style="height: 120px"
          />
          <!-- <h2>PENGECEKAN SERTIFIKAT</h2>
          <p>
            Masukkan No. Seri Sertifikat:
          </p>
          <input
            placeholder="XXX/BP2-XXXX/XXX"
            v-model="forms.SerialNumber"
            type="text"
            style="background:white; padding:8px 12px; position: relative; top:1px; text-transform: uppercase;"
          />
          <v-btn @click="track" color="info" style="font-size:14px;">
            TAMPILKAN
          </v-btn> -->
        </div>
      </div>
    </div>
  </section>
</template>
<script>
// import { VueRecaptcha } from 'vue-recaptcha'
export default {
  // components: { VueRecaptcha },
  data: () => ({
    forms: {},
  }),
  async mounted() {
    setTimeout(() => {
      const plugin = document.createElement('script')
      plugin.setAttribute('src', 'https://www.google.com/recaptcha/api.js')
      plugin.async = true
      document.head.appendChild(plugin)
    }, 2000)
  },
  methods: {
    track() {
      window.$('#dvcaptcha').html('<div id="recaptcha"></div>')
      window.grecaptcha.render('recaptcha', {
        sitekey: '6LfoHqoUAAAAAIa48CkYHUtD6AUvLFLhQs_oCD0b',
        callback: this.oncaptcha,
      })

      window.$('#dvcaptcha').css('display', 'block')
      window.$('#trackingerr').css('display', 'none')
      window.$('#trackingresult').css('display', 'none')
    },
    async oncaptcha(token) {
      const $ = window.$
      $('#dvcaptcha').css('display', 'none')
      let ret = await this.$api.post(
        this.$api.url + '/api/track/' + this.forms.ShareCode,
        { token: token },
      )
      console.log(ret)
      if (!ret.Success) {
        $('#trackingerr').css('display', 'block')
        $('#trackingerr').html(`<h3> ${ret.Message}<h3>`)
        return
      }

      $('#trackingresult').css('display', 'block')
      var d = ret.Data
      this.forms = d
      $('#_NamaPelanggan').text(d.NamaPelanggan)
      $('#_NoPengujian').text(d.NoPengujian)
      $('#_JenisUji').text(d.JenisUji)
      $('#_StatusName').text(d.StatusName)

      var color = [
        '9C27B0',
        '673AB7',
        '3F51B5',
        '2196F3',
        '03A9F4',
        '00BCD4',
        '009688',
        '4CAF50',
      ]
      $('#tracker').html('')
      for (var i = 0; i < d.StatusID; i++) {
        $('#tracker').append(
          $(
            `<div class= "tracker" style = "background: #${color[i]}; "></div>`,
          ),
        )
      }
      $('#UjiDet').html('')
      d.detail.forEach(function (v) {
        $('#UjiDet').append(
          $(
            `<div><span style="width:50%; display:inline-block;">${
              v.NamaParameter
            }</span> <i class="fa fa-circle"></i> ${v.StatusName} ${
              v.Masalah ? '(' + v.Masalah + ')' : ''
            }</div>`,
          ),
        )
      })
    },
  },
}
</script>
<style>
#trackingresult {
  background: rgb(42, 149, 190);
  color: white;
  display: block;
  padding: 1px 15px;
  margin-left: -15px;
  margin-top: 15px;
  width: calc(100% + 30px);
}
</style>
