import{n as p,S as m,k as s,_ as i,h as d,g as u,f as o}from"./index-DYIZrBBo.js";const c={components:{SidePane:m},data:()=>({datagrid:[],dbparams:{PermohonanID:0},lembarKerja:[],loading:!1,forms:{PermohonanID:0,ParameterID:0,Ma<PERSON><PERSON>:""},rebind:1,rebindSidebar:1,masalah:{show:!1}}),computed:{showSubmitButton(){return this.lembarKerja.filter(a=>!a.Alasan&&!a.ApprovedBy).length>0&&this.forms.LkStatus!="submitted"}},methods:{SideFilter(r){return!r.LkStatus},async ItemClick(r){this.Populate(r.PermohonanID)},OpenPDF(r){window.open(this.$api.url+r.replace(/\.\w{3,4}$/,"pdf"),"_blank")},async Populate(r){this.dbparams={PermohonanID:r};var a=await this.$api.call("UJI.SelPermohonan",{PermohonanID:r});a.data.length?this.forms=a.data[0]:this.forms={},this.PopulateLK(r)},async PopulateLK(r){this.lembarKerja=[];var a=await this.$api.call("UJI.SelLembarKerja",{PermohonanID:r});this.lembarKerja=a.data},async DeleteLK(r){if(confirm("Anda yakin menghapus Lembar Kerja ini?")){var a=await this.$api.call("UJI.DelLembarKerja",{LembarKerjaID:r});a.success&&this.PopulateLK(this.forms.PermohonanID)}},async MenuPDF(r,a){r=="Tampilkan"?window.open(this.$api.url+a.LkUrl.replace(/\.\w{3,4}$/,".pdf"),"_blank"):r=="Perbaiki"&&(this.$api.notify("Silahkan tunggu .."),this.loading=!0,await this.$api.get("/reports/uji/refine-lk/"+a.LembarKerjaID),window.open(this.$api.url+a.LkUrl.replace(/\.\w{3,4}$/,".pdf"),"_blank"),this.loading=!1)},Open(r,a){window.open(this.$api.url+"/reports/uji/lembarkerja/"+a,"_blank")},async SubmitLK(){await this.$api.call("UJI_UpdLkStatus",{PermohonanID:this.forms.PermohonanID,Status:"submitted"}),this.forms.LkStatus="submitted",this.rebindSidebar++},async fileUploaded(r,a){if(this.$api.notify("Upload Sukses"),(await this.$api.call("UJI_SavLembarKerja",{PermohonanID:this.forms.PermohonanID,XmlRawUrl:r.infos,RevisedID:typeof a=="object"?null:a})).success){this.forms.LkStatus=null,await this.PopulateLK(this.forms.PermohonanID);for(let t of this.lembarKerja)this.$api.get("/reports/uji/convert-lk/"+t.LembarKerjaID)}}}};var f=function(){var a=this,e=a._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{attrs:{statusId:",5,",rebind:a.rebindSidebar},on:{"item-click":a.ItemClick}}),e("div",{directives:[{name:"show",rawName:"v-show",value:a.forms.Nama,expression:"forms.Nama"}],staticStyle:{overflow:"auto",height:"calc(100vh - 66px)"}},[e("div",{staticStyle:{background:"#039ae4",color:"white",padding:"15px 15px 10px 15px",display:"flex"}},[e("div",{staticStyle:{display:"flex",width:"calc(100% - 200px)"}},[e("div",{staticStyle:{"max-width":"450px",overflow:"hidden","text-wrap":"nowrap","text-overflow":"ellipsis"}},[a._v(" "+a._s(a.forms.Nama)+" ")]),e("div",{staticStyle:{background:"white",color:"#039ae4","margin-left":"10px",padding:"5px 8px","border-radius":"5px","font-size":"small",position:"relative",top:"-3px"}},[a._v(" "+a._s(a.forms.NoPengujian)+" ")])]),e(s),a.forms.StatusID==6?e(i,{staticStyle:{color:"white !important",position:"relative",top:"-2px"},attrs:{small:"",disabled:""}},[a._v(" SELESAI ")]):a._e()],1),e(d,{staticClass:"table-cekprogress",attrs:{datagrid:a.datagrid,dbref:"UJI_SelCekProgress",doRebind:a.rebind,dbparams:a.dbparams,disabled:!0,columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Jml",value:"JmlContoh",width:"50px"},{name:"Keterangan",value:"Keterangan"},{name:"Waktu",value:"Waktu"}]},on:{"update:datagrid":function(t){a.datagrid=t}},scopedSlots:a._u([{key:"row-NamaParameter",fn:function({row:t}){return[e("div",{class:{"is-masalah":t.Ordr==2,"is-done":t.HasilStatus}},[e("div",[a._v(a._s(t.NamaParameter))]),e("div",[a._v(a._s(t.Ordr==1?t.Metode:""))])])]}},{key:"row-Keterangan",fn:function({row:t}){return[a._v(" "+a._s(t.Keterangan)+" ")]}},{key:"row-Waktu",fn:function({row:t}){return[a._v(" "+a._s(t.Waktu)+" Hari ")]}}])}),a._l(a.lembarKerja,function(t,l){return e("div",{key:l,staticStyle:{"font-size":"12px",background:"#f3f3f3",display:"flex","margin-bottom":"1px",padding:"8px"}},[t.ApprovedBy?a._e():e(i,{directives:[{name:"tooltip",rawName:"v-tooltip",value:"Hapus Lembar Kerja",expression:"'Hapus Lembar Kerja'"}],staticStyle:{"margin-top":"4px"},attrs:{"x-small":"",text:"",color:"error"},on:{click:function(n){return a.DeleteLK(t.LembarKerjaID)}}},[e(u,[a._v(" mdi-close ")])],1),e(i,{attrs:{small:"",text:"",color:"primary"},on:{click:function(n){return a.Open(t.LkUrl,t.LembarKerjaID)}}},[a._v(" "+a._s(t.Nama)+" ")]),e(s),t.Alasan?e(i,{directives:[{name:"tooltip",rawName:"v-tooltip",value:t.Alasan,expression:"lk.Alasan"}],staticStyle:{margin:"4px"},attrs:{"x-small":"",text:"",outlined:"",color:"error"}},[a._v(" DITOLAK ")]):a._e(),t.Alasan?e(o,{attrs:{accept:".pdf"},on:{change:function(n){return a.fileUploaded(n,t.LembarKerjaID)}},scopedSlots:a._u([{key:"default",fn:function({opener:n}){return[e(i,{staticStyle:{margin:"4px"},attrs:{"x-small":"",text:"",outlined:"",color:"primary"},on:{click:n}},[a._v(" REVISI ")])]}}],null,!0)}):t.ApprovedBy?e(i,{staticStyle:{margin:"4px"},attrs:{"x-small":"",text:"",outlined:""}},[a._v(" SUDAH DISETUJUI ")]):a._e()],1)}),e("div",{staticStyle:{display:"flex"}},[e(o,{staticStyle:{"margin-top":"8px"},attrs:{multiple:!0,accept:".pdf"},on:{change:a.fileUploaded},scopedSlots:a._u([{key:"default",fn:function({opener:t}){return[e(i,{attrs:{color:"primary"},on:{click:t}},[a._v(" UPLOAD LEMBAR KERJA ")])]}}])}),a.showSubmitButton?e(i,{staticStyle:{margin:"8px 0 0 8px"},attrs:{color:"success"},on:{click:a.SubmitLK}},[a._v(" SUBMIT ")]):a._e()],1)],2)],1)},h=[],_=p(c,f,h,!1,null,null);const b=_.exports;export{b as default};
