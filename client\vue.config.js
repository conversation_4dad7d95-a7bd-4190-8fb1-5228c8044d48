module.exports = {
  transpileDependencies: ['vuetify'],
  pwa: {
    name: '<PERSON>ILAKO<PERSON>',
    themeColor: '#4DBA87',
    msTileColor: '#000000',
    appleMobileWebAppCapable: 'yes',
    appleMobileWebAppStatusBarStyle: 'black',
    workboxOptions: {
      skipWaiting: true,
    },
    manifestOptions: {
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>',
      short_name: 'SILAKO<PERSON>',
      description: 'SILAKON - Portal resmi <PERSON> da<PERSON> (BP2)',
      theme_color: '#4DBA87',
      background_color: '#ffffff',
      display: 'standalone',
      start_url: '/',
    },
  },
  pages: {
    index: {
      entry: 'src/main.js',
      title: 'SILAKON - <PERSON><PERSON> (BP2)',
    },
  },
}
