
const Excel = require('exceljs')
const shell = require('shelljs')
const moment = require('moment')
const HtmlGenerator = require('./HtmlGenerator')
let db = require('../../common/db')
moment.locale('id')

const report = {
  colswidth: {
    No: 6,
    NIK: 17,
    Nama: 24,
    KRT_Nama: 24,
    Alamat: 50,
    Kabupaten: 15,
    Kecamatan: 15,
    Ke<PERSON><PERSON>an: 15,
    Desa: 15
  },
  async Generic (req, res, type) {
    try {
      res.header(
        'Cache-Control',
        'private, no-cache, no-store, must-revalidate'
      )
      res.header('Expires', '-1')
      res.header('Pragma', 'no-cache')
      const param = {...req.query, ...req.body}
  
      const rptName = param.rptname ? param.rptname : 'Report'
      const dd = moment().format('mmss')
      const filename = rptName.replace(/[^a-z0-9]/gi, '_') + dd
      const options = {
        filename: `tmp/${filename}.${type || 'xlsx'}`,
        useStyles: true,
        useSharedStrings: true
      }
      let workbook = {}
      if (type === 'html') workbook = new HtmlGenerator(options)
      else workbook = new Excel.stream.xlsx.WorkbookWriter(options)
  
      let sets = 1
      let i = 0
      if (param.sets) sets = param.sets
  
      do {
        const postfix = i > 0 ? '_s' + (i + 1) : ''
        await report.GenericRun(param.sp + postfix, param, workbook, {
          name: rptName,
          sheet: i,
          headers: param.headers,
          groupsheet: param.groupsheet
        })
  
        i++
      } while (i < sets)
  
      await workbook.commit()
      if (type === 'pdf' && this.GeneratePDF(`tmp/${filename}.xlsx`)) {
        res.send({
          success: true,
          data: `/get/${filename}.pdf`,
          message: 'Report Generated',
          type: 'url'
        })
      } else {
        res.send({
          success: true,
          data: `/get/${filename}.${type || 'xlsx'}`,
          message: 'Report Generated',
          type: 'url'
        })
      }
    } catch (err) {
      res.send({
        success: false,
        message: err.message
      })
    }
  },
  GeneratePDF (path) {
    console.info(`Converting to PDF: ${path}..`)
    const out = shell.exec(
      `/opt/libreoffice7.0/program/soffice --convert-to pdf "/app/${path}" --outdir /app/tmp`,
      {
        silent: true
      }
    )
    if (out.code !== 0) {
      console.error(`ERR: ${out.stderr}`)
      return false
    } else return true
  },
  GenerateHeader (sheet, keys) {
    const sHeader = {
      border: {
        top: {style: 'thin'},
        left: {style: 'thin'},
        bottom: {style: 'thin'},
        right: {style: 'thin'}
      },
      font: {size: 10, bold: true},
      alignment: {vertical: 'middle', horizontal: 'center'}
    }
    // Generating Dynamic Headers
    for (let i = 0; i < keys.length; i++) {
      if (keys[i].match(/^_/)) continue
      let colspan = 1
      let j = i
      let c1 = ''
      let c2 = ''
      do {
        if (j + 1 >= keys.length) break
        c1 = keys[j].split('_', 2)
        c2 = keys[j + 1].split('_', 2)
        if (c1[0] === c2[0]) colspan++
        j++
      } while (c1[0] === c2[0])
  
      if (colspan === 1) {
        sheet.mergeCells(1, i + 1, 2, i + 1)
        sheet.getCell(1, i + 1).value = keys[i].replace(/_/g, ' ')
        if (!sheet.getColumn(i + 1).width) {
          sheet.getColumn(i + 1).width =
              this.colswidth[keys[i]] || Math.ceil(keys[i].length * 1.5)
        }
        // console.log(keys[i].replace(/_/g, ' '))
        sheet.getCell(1, i + 1).border = sHeader.border
        sheet.getCell(2, i + 1).alignment = sHeader.alignment
      } else {
        // // array_push(hcol, ['value'=>c1[0], 'colspan'=>colspan]);
        sheet.mergeCells(1, i + 1, 1, i + colspan)
        sheet.getCell(1, i + 1).value = c1[0].replace(/_/g, ' ')
        // console.log(c1[0].replace(/_/g, ' '))
        sheet.getCell(1, i + 1).border = sHeader.border
        sheet.getCell(1, i + 1).alignment = sHeader.alignment
        const len = colspan + i
        while (i < len) {
          const c2 = keys[i].split('_', 2)
          sheet.getCell(2, i + 1).value = c2[1].replace(/_/g, ' ')
          // console.log(c2[1].replace(/_/g, ' '))
          sheet.getCell(2, i + 1).border = sHeader.border
          sheet.getCell(2, i + 1).alignment = sHeader.alignment
          i++
        }
        i--
      }
    }
    sheet.getRow(2).font = sHeader.font
    sheet.getRow(1).font = sHeader.font
  },
  async GenericRun (sp, param, workbook, opts, dbres) {
    const sHeader = {
      border: {
        top: {style: 'thin'},
        left: {style: 'thin'},
        bottom: {style: 'thin'},
        right: {style: 'thin'}
      },
      font: {size: 10, bold: true},
      alignment: {vertical: 'middle', horizontal: 'center'}
    }
    const hasPagination = await db.spHasPagination(sp)
    // console.log('GenericRun')
    try {
      // const i = 0
      let ii = 0
      let pageRef = 0
      // Common columns width
      if (opts.groupsheet) {
        // console.log(': with groupsheet')
        const groupCol = opts.groupsheet
        let groupVal = ''
  
        let sheet = null
        if(!dbres) dbres = await db.exec(sp, param)
        let rptsettings = {
          // hide: ["col1", "col2"],
          // groupsheet: "GroupSheet",
          // styles: 
          // multipleResult
        }
        let res = []
        if (dbres.length > 1 && dbres[0].length > 0 && dbres[0][0]._Settings) {
          rptsettings = JSON.parse(dbres[0][0]._Settings)
          if(dbres.length > 3) {
            for(let rx = 1; rx < dbres.length-1; rx++) {
              // console.log(rx, Array.isArray(dbres[rx]), dbres[rx].length)
              if(Array.isArray(dbres[rx])) await report.GenericRun(sp, param, workbook, opts, [dbres[0], dbres[rx]])
            }
            return
          } else
            res = dbres[1]
        } else {
          res = dbres
        }
        let row = res[ii]
  
  
        let keys = Object.keys(res[0])
        if (rptsettings.hide) keys = keys.filter(k => !rptsettings.hide.includes(k))
  
        // COLUMNS SETUP
        if (rptsettings.styles) {
          let i = 1
          for (const key of keys) {
            if (rptsettings.styles[key]) {
              const styles = rptsettings.styles[key]
              for (const style of Object.keys(styles)) {
                sheet.getColumn(i)[style] = styles[style]
              }
            }
            i++
          }
        }
  
        do {
          if (groupVal !== row[groupCol]) {
            groupVal = row[groupCol]
            // if(sheet) sheet.commit()
            sheet = workbook.addWorksheet(groupVal)
            this.GenerateHeader(sheet, keys)
          }
  
          const rcolArr = []
          for (const key of keys) {
            if (key.match(/^_/)) rcolArr.push('')
            else rcolArr.push(row[key])
          }
          sheet.addRow(rcolArr) // body
          if (ii < res.length) {
            ii++
            row = res[ii]
          } else if (hasPagination) {
            ii = 0
            pageRef++
            // console.log(`page ${pageRef}: ${sp}`)
            res = await db.exec(sp, {
              ...param,
              _PageRef: pageRef,
              _CountRef: 10000
            })
            if (res.length === 10000 && hasPagination) row = res[ii]
            else row = null
          } else {
            row = null
          }
        } while (row)
      } else {
        // console.log(': no groupsheet')
        if(!dbres) dbres = await db
          .exec(sp, {...param, _PageRef: pageRef, _CountRef: 50000}, true)
          .catch(err => console.error(err))
        let rptsettings = {}
        // console.log('rptsettings', res.length, res[0].length, res[0][0]._Settings)
        // console.log(res)
        let res = []
        if (dbres.length > 1 && dbres[0].length > 0 && dbres[0][0]._Settings) {
          rptsettings = JSON.parse(dbres[0][0]._Settings)
  
          if(rptsettings.groupsheet) {
            opts.groupsheet = rptsettings.groupsheet
            await report.GenericRun(sp, param, workbook, opts, dbres)
            return
          }
          res = dbres[1]
        } else {
          res = dbres
        }
        if (res.length > 0) {
          // console.log(`generating ${res.length} rows`)
          let keys = Object.keys(res[0])
          if (rptsettings.hide) keys = keys.filter(k => !rptsettings.hide.includes(k))
          const sheet = workbook.addWorksheet('Report')
  
          // PAGE SETUP
          sheet.pageSetup.fitToPage = true
          sheet.pageSetup.fitToWidth = 1
          sheet.pageSetup.fitToHeight = 0
          sheet.pageSetup.paperSize = 9
          sheet.pageSetup.orientation = 'landscape'
          sheet.pageSetup.margins = {
            left: 0.25,
            right: 0.25,
            top: 0.5,
            bottom: 0.5,
            header: 0.3,
            footer: 0.3
          }
          // const i = 0
          let ii = 0
          let pageRef = 0
          let row = res[ii]
  
          // COLUMNS SETUP
          if (rptsettings.styles) {
            let i = 1
            for (const key of keys) {
              if (rptsettings.styles[key]) {
                const styles = rptsettings.styles[key]
                for (const style of Object.keys(styles)) {
                  sheet.getColumn(i)[style] = styles[style]
                }
              }
              i++
            }
          }
  
          if (opts.headers) {
            sheet.addRow(
              opts.headers.map((h, idx) => {
                sheet.getColumn(idx + 1).width = this.colswidth[h.value]
                  ? this.colswidth[h.value]
                  : String(row[h.value]).length * 1.3 < 6
                    ? 6
                    : String(row[h.value]).length * 1.3
                return h.name
              })
            )
            sheet.getRow(1).font = sHeader.font
            sheet.getRow(1).commit()
  
            do {
              const rcolArr = []
              for (const i in opts.headers) {
                rcolArr.push(row[opts.headers[i].value])
              }
              sheet.addRow(rcolArr).commit() // body
              if (ii < res.length) {
                ii++
                row = res[ii]
              } else {
                ii = 0
                pageRef++
                // console.log(`page ${pageRef}`)
                res = await db.exec(sp, {
                  ...param,
                  _PageRef: pageRef,
                  _CountRef: 50000
                })
                if (res.length === 50000) row = res[ii]
                else row = null
              }
            } while (row)
          } else {
            this.GenerateHeader(sheet, keys)
            do {
              const rcolArr = []
              for (const key of keys) {
                rcolArr.push(row[key])
              }
              const r = sheet.addRow(rcolArr)
              if (rptsettings.ordrStyles && row.Ordr && rptsettings.ordrStyles[row.Ordr]) {
                const styles = rptsettings.ordrStyles[row.Ordr]
                for (const style of Object.keys(styles)) {
                  r[style] = styles[style]
                }
              }
              r.commit()
              if (ii < res.length) {
                ii++
                row = res[ii]
              } else if (hasPagination) {
                ii = 0
                pageRef++
                // console.log(`page ${pageRef}: ${sp}`)
                res = await db.exec(sp, {
                  ...param,
                  _PageRef: pageRef,
                  _CountRef: 50000
                })
                if (res.length === 50000 && hasPagination) row = res[ii]
                else row = null
              } else {
                row = null
              }
            } while (row)
          }
          sheet.commit()
          // console.log('done')
        }
      }
    } catch (ex) {
      console.error(ex)
    }
  }
}

module.exports = report