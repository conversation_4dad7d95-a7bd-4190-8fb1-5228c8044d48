var muhammara = require('muhammara')
const Recipe = require("muhammara").Recipe;

const strToByteArray = (str) => {
  const myBuffer = [];
  const buffer = Buffer.from(str, "binary"); // key change 2
  for (let i = 0; i < buffer.length; i++) {
    myBuffer.push(buffer[i]);
  }
  return myBuffer;
};

const modifyPdf = (sourceStream, targetStream, patterns) => {
  const modPdfWriter = muhammara.createWriterToModify(
    new muhammara.PDFRStreamForFile(sourceStream), 
    new muhammara.PDFWStreamForFile(targetStream), 
    {compress: false});
  const numPages = modPdfWriter
    .createPDFCopyingContextForModifiedFile()
    .getSourceDocumentParser()
    .getPagesCount();
  
  for (let page = 0; page < numPages; page++) {
    const copyingContext = modPdfWriter.createPDFCopyingContextForModifiedFile();
    const objectsContext = modPdfWriter.getObjectsContext();
  
    const pageObject = copyingContext.getSourceDocumentParser().parsePage(page);
    const textStream = copyingContext
      .getSourceDocumentParser()
      .queryDictionaryObject(pageObject.getDictionary(), "Contents");
    const textObjectID = pageObject.getDictionary().toJSObject().Contents.getObjectID();
  
    let data = [];
    const readStream = copyingContext.getSourceDocumentParser().startReadingFromStream(textStream);
    while (readStream.notEnded()) {
      const readData = readStream.read(10000);
      data = data.concat(readData);
    }
  
    const pdfPageAsString = Buffer.from(data).toString("utf-8"); // key change 1
  
    console.log(pdfPageAsString)
    let modifiedPdfPageAsString = pdfPageAsString;
    for (const key in patterns) {
      modifiedPdfPageAsString = modifiedPdfPageAsString.replace(`{d.${key}}`, patterns[key]);
    }
  
    // Create what will become our new text object
    objectsContext.startModifiedIndirectObject(textObjectID);
  
    const stream = objectsContext.startUnfilteredPDFStream();
    stream.getWriteStream().write(strToByteArray(modifiedPdfPageAsString));
    objectsContext.endPDFStream(stream);
  
    objectsContext.endIndirectObject();
  }
  
  modPdfWriter.end();
};

function replaceText(sourceFile, targetFile, pageNumber, findText, replaceText) {  
  var writer = muhammara.createWriterToModify(sourceFile, {
    modifiedFilePath: targetFile
  });
  var sourceParser = writer.createPDFCopyingContextForModifiedFile().getSourceDocumentParser();
  var pageObject = sourceParser.parsePage(pageNumber);
  var textObjectId = pageObject.getDictionary().toJSObject().Contents.getObjectID();
  var textStream = sourceParser.queryDictionaryObject(pageObject.getDictionary(), 'Contents');
  //read the original block of text data
  var data = [];
  var readStream = sourceParser.startReadingFromStream(textStream);
  while(readStream.notEnded()){
    Array.prototype.push.apply(data, readStream.read(10000));
  }
  var string = new Buffer(data).toString()
  let m = string.match(/\(.\)-?\d+/g)
  // console.log(m)
  let mapLetter = {}
  for(let l of m) {
    let x = l.split(')')
    mapLetter[x[0]+')'] = l
  }
  let newrt = ""
  for(let l in replaceText) {
    newrt = mapLetter[`(${l})`]
  }
  string = string.replace(findText, newrt);

  //Create and write our new text object
  var objectsContext = writer.getObjectsContext();
  objectsContext.startModifiedIndirectObject(textObjectId);

  var stream = objectsContext.startUnfilteredPDFStream();
  stream.getWriteStream().write(strToByteArray(string));
  objectsContext.endPDFStream(stream);

  objectsContext.endIndirectObject();

  writer.end();
}

// modifyPdf("test.pdf", "test-out.pdf", {
//   SerialNumber: "XXX/YYY/ZZZ",
//   TanggalTTD: "23 Januari 2023"
// })

replaceText("test.pdf", "test-out.pdf", 1, "26({)4(d)-5(.)-2(T)-8(a)-5(n)-5(g)5(g)5(a)-5(lT)-7(T)-8(D})","coba dulu deh")