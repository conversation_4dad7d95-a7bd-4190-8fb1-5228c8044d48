<template>
  <div>
    <List
      :items="datalist"
      :selectOnLoad="true"
      style="height: calc(100vh - 194px)"
    >
      <template v-slot="{ row }">
        <v-list-item>
          <v-list-item-action v-show="!row.BillingID">
            <Checkbox :value.sync="row.checked" @click="ItemClick" />
          </v-list-item-action>

          <v-list-item-content>
            <v-list-item-title>{{ row.NamaPelanggan }}</v-list-item-title>
            <v-list-item-subtitle>
              {{ row.NoPengujian }} | {{ row.JenisUji }}
            </v-list-item-subtitle>
            <v-list-item-subtitle style="display: flex">
              <div style="font-weight: bold">
                Rp. {{ row.TotalBayar | format }}
              </div>
              <v-spacer />
              <div v-if="row.BayarDate" style="color: green">Sudah Dibayar</div>
              <div v-else-if="row.BillingID" style="color: orangered">
                Menunggu Pembayaran
              </div>
              <div v-else style="color: red">Belum Dibayar</div>
            </v-list-item-subtitle>
          </v-list-item-content>
          <v-list-item-icon v-show="row.BillingID">
            <v-menu offset-y>
              <template v-slot:activator="{ on }">
                <v-icon v-on="on"> mdi-dots-vertical</v-icon>
              </template>
              <v-list dense>
                <v-list-item
                  v-for="(item, idx) in itemMenu"
                  :key="idx"
                  @click="ItemMenuClick(item, row)"
                >
                  <v-list-item-content :style="{ color: item.color || '#333' }">
                    <v-list-item-title>{{ item.text }}</v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-menu>
          </v-list-item-icon>
        </v-list-item>
        <!-- <div
          style="font-size: 13px; margin-bottom:10px; padding: 5px 10px 0 10px; display:flex"
        >
          <div style="padding:8px 20px"></div>
          <div style="width:230px">
            <div>
              {{ row.NamaPelanggan }}
            </div>
            <div>{{ row.JenisUji }}</div>
          </div>
          <div
            style="padding: 12px 20px;
                font-weight: bold;
                width: 200px;
                text-align: right;"
          >
            Rp. {{ row.TotalBayar | format }}
          </div>
          <div style="padding:8px 20px">
            <v-btn
              text
              outlined
              small
              color="primary"
              @click="ShowBilling(row.PermohonanID)"
            >
              BILLING
            </v-btn>
          </div>
        </div> -->
      </template>
    </List>
    <div style="height: 40px">
      <v-btn
        style="margin-left: 5px; margin-top: -50px"
        color="primary"
        class="btn-full"
        v-show="billing.TotalBayar"
        @click="billing.show = true"
      >
        BAYAR: Rp {{ this.billing.TotalBayar | format }},-
      </v-btn>
    </div>
    <Bayar :forms.sync="billing" @refresh="$emit('refresh')"> </Bayar>
  </div>
</template>
<script>
import Bayar from './Bayar.vue'
import moment from 'moment'

export default {
  components: {
    Bayar,
  },
  data: () => ({
    billing: {
      show: false,
      PaymentType: 'cash',
      PermohonanID: '',
      TotalBayar: 0,
      step: 0,
    },
    itemMenu: [
      { text: 'Detail Pembayaran' } /*, { text: 'Hapus', color: 'red' }*/,
    ],
    datalist: [],
    statusId: '3',
    rebind: 1,
  }),
  computed: {
    dbparams() {
      return { StatusID: this.statusId }
    },
  },
  watch: {
    'billing.show'(val) {
      if (!val) {
        // this.rebind++
        this.Populate()
        this.billing.TotalBayar = 0
      }
    },
  },
  mounted() {
    this.Populate()
  },
  methods: {
    ItemClick() {
      this.billing.TotalBayar = 0
      let permohonanId = []
      for (let item of this.datalist) {
        if (item.checked) {
          this.billing.TotalBayar += item.TotalBayar
          permohonanId.push(item.PermohonanID)
        }
      }
      this.billing.PermohonanID = permohonanId.join(',')
    },
    async Populate() {
      let ret = await this.$api.call('UJI_SelPermohonanList', {
        StatusID: this.statusId,
      })
      // this.datalist = ret.data
      let list = []
      let billingId = 'xxx'
      for (let i = 0; i < ret.data.length; i++) {
        if (!ret.data[i].BillingID || ret.data[i].BillingID != billingId) {
          list.push(ret.data[i])
        } else {
          let l = list[list.length - 1]
          l.NamaPelanggan += ', ' + ret.data[i].NamaPelanggan
          l.TotalBayar += ret.data[i].TotalBayar
          if (l.JenisUji != ret.data[i].JenisUji)
            l.JenisUji += ret.data[i].JenisUji
        }
        billingId = ret.data[i].BillingID
      }
      this.datalist = list
      // this.datalist = []
      // this.datalist.push(JSON.parse(JSON.stringify(list[0])))
      // this.datalist.push(JSON.parse(JSON.stringify(list[0])))
    },
    ItemMenuClick(val, row) {
      if (val.text == 'Detail Pembayaran') {
        this.ShowBilling(row.PermohonanID)
      }
    },
    async ShowBilling(permohonanId) {
      let ret = await this.$api.call('UJI_SelBilling', {
        PermohonanID: permohonanId,
      })
      if (ret.success) {
        this.billing = {
          show: true,
          step: 1,
          total: ret.data[0].TotalBayar,
          billingId: ret.data[0].BillingID,
          expiredDate: moment(ret.data[0].ExpiredDate)
            .add(7, 'hour')
            .format('DD-MMM-YYYY HH:mm:ss'),
          PaymentType: 'transfer',
        }
      }
    },
  },
}
</script>
