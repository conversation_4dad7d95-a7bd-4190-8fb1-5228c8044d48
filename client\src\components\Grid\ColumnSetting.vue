<template>
  <Modal
    title="Column Setting"
    :show.sync="xshow"
    width="800px"
    @onSubmit="Save"
  >
    <div class="form-inline checkbox-columns">
      <div v-for="(item, idx) in columns" :key="idx" v-show="item.name">
        <Checkbox :text="item.name" :value.sync="isChecked[idx]" />
      </div>
    </div>
  </Modal>
</template>
<script>
import Checkbox from '../Forms/Checkbox.vue'
export default {
  components: { Checkbox },
  data: () => ({
    xshow: false,
    forms: {},
    isChecked: [],
  }),
  props: {
    show: <PERSON><PERSON>an,
    columns: Array,
  },
  watch: {
    show(val) {
      this.xshow = val
      if (val) this.populate()
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    populate() {
      this.isChecked = this.columns.map((c) => !c.hide)
    },
    async Save() {
      // let ret = await this.api.call('', {})
      // if (ret.success)
      this.$emit('update:show', false)
      this.$emit('onUpdate', this.isChecked)
    },
  },
}
</script>
<style lang="scss">
.modal-column-setting {
  .checkbox-columns {
    column-count: 3;
  }
}
.is-mobile {
  .modal-column-setting {
    .checkbox-columns {
      column-count: 2;
    }
  }
}
</style>
