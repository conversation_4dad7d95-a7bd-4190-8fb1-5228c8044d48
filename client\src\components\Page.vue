<template>
  <div
    :class="{
      page: true,
      [classId]: true,
    }"
  >
    <div class="page-header" v-show="title">
      <div class="page-title">{{ title }}</div>
      <div class="page-toolbar">
        <slot name="toolbar"> </slot>
      </div>
    </div>
    <div
      class="page-content"
      :class="{ 'has-sidebar': sidebar, 'is-focused': isPageFocused }"
    >
      <slot></slot>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    title: String,
    sidebar: Boolean,
  },
  computed: {
    ...mapGetters(['isPageFocused']),
    classId() {
      return this.title
        ? 'page-' + this.title.toLowerCase().replace(/\s/g, '-')
        : ''
    },
  },
  mounted() {
    // console.log('mounted')
    // let panes = window.querySelector('.page-content.has-sidebar');
  },
}
</script>
<style lang="scss">
.is-mobile {
  .page {
    height: calc(100vh - 56px);
  }
}
.page {
  overflow: auto;
  height: calc(100vh - 50px);
  background-color: rgba(255, 255, 255, 0.5);
  &-header {
    padding: 15px 15px;
    display: flex;

    .page-title {
      font-family: Raleway;
      line-height: 20px;
      font-size: 30px;
      flex: 1;
    }
    .page-toolbar {
      flex: 1;
      text-align: right;

      & > * {
        margin-left: 10px;
      }
    }
  }
  &-content {
    height: calc(100vh - 108px);
    // min-width: 1380px;
    overflow: hidden;
    &.has-sidebar {
      display: flex;
      & > div:first-child {
        // flex: 0 0 340px;
        transition: 0.5s;
      }
      & > div:nth-child(2) {
        // flex: 0 0 calc(100vw - 360px);
        overflow: auto;
        width: 100%;
        padding: 0 10px 0 10px;
        height: calc(100% + 8px);
      }
    }
  }
}
.is-mobile {
  .page {
    &-content {
      // min-width: 300vw;
      &.has-sidebar {
        display: flex;
        & > div:first-child {
          flex: 0 0 100vw;
        }
        & > div:nth-child(2) {
          flex: 0 0 100vw;
        }
      }
      &.has-sidebar.is-focused {
        & > div:first-child {
          margin-left: calc(-100vw - 10px);
        }
      }
    }
  }
}
</style>
