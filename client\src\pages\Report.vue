<template>
  <div style="display: flex">
    <div class="sidepane">
      <div class="rowlnk">
        <v-btn
          text
          small
          color="primary"
          @click="Generate($event, { sp: 'UJI_RptRekap' })"
        >
          Lapor<PERSON>
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn
          text
          small
          color="primary"
          @click="Generate($event, { sp: 'UJI_RptDetail' })"
        >
          Laporan Detail
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn
          text
          small
          color="primary"
          @click="Generate($event, { sp: 'UJI_RptPermohonan' })"
        >
          Laporan Status Permohonan
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn
          text
          small
          color="primary"
          @click="Generate($event, { sp: 'UJI_RptProgressPengujian' })"
          >Laporan Progress Pengujian</v-btn
        >
      </div>
      <div class="rowlnk">
        <v-btn
          text
          small
          color="primary"
          @click="Generate($event, { sp: 'UJI_RptSertifikat' })"
          >Laporan Sertifikat</v-btn
        >
      </div>
    </div>
    <div>
      <ReportParams
        v-show="showParams"
        :options="reportOptions"
        :reportParams.sync="reportParams"
        :generatedUrl.sync="reportUrl"
      />
      <ReportViewer
        :url="reportUrl"
        :options="reportParams"
        :show.sync="showReport"
      />
    </div>
  </div>
</template>
<script>
import ReportParams from '../components/Report/Params.vue'
import ReportViewer from '../components/ReportViewer.vue'

export default {
  components: {
    ReportParams,
    ReportViewer,
  },
  data: () => ({
    coms: null,
    reportOptions: null,
    reportUrl: 'about:blank',
    showReport: false,
    showParams: false,
    reportParams: {},
  }),
  watch: {
    // reportUrl(val) {
    //   if (val) this.showReport = true
    // },
    reportParams(val) {
      if (val) this.showReport = true
    },
  },
  created() {
    let coms = sessionStorage.getItem('coms-access')
    if (coms) this.coms = JSON.parse(coms)
  },
  methods: {
    Generate(evt, opts) {
      this.showReport = false
      this.showParams = true
      opts.rptname = evt.target.innerText
      this.reportOptions = opts
    },
  },
}
</script>
<style lang="scss">
.rowlnk {
  border-bottom: 1px solid #ddd;
}
</style>
