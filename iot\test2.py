#!/usr/bin/env python3
"""Bluetooth device discovery using PyBluez and Windows BLE scanning.

This script performs both classic Bluetooth and BLE device discovery.
"""

import asyncio
import sys
import bluetooth

try:
    from bleak import BleakClient, BleakScanner
    HAS_BLEAK = True
except ImportError:
    print("For better Windows Bluetooth support, install bleak:")
    print("pip install bleak")
    HAS_BLEAK = False

async def scan_ble():
    """Scan for BLE devices."""
    print("\nScanning for BLE devices...")
    devices = await BleakScanner.discover()
    print(f"\nFound {len(devices)} BLE devices:")
    for d in devices:
        print(f"   {d.address} - {d.name or 'Unknown'}")
        device = await BleakScanner.find_device_by_address(d.address)
        if not device:
            print(f"Device with address {d.address} not found")
        else:
            print(f"Device with address {d.address} found")

if HAS_BLEAK:
    # Run BLE scanner
    asyncio.run(scan_ble())