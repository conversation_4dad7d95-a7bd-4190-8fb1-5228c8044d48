import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import legacy from '@vitejs/plugin-legacy'
import vue2 from '@vitejs/plugin-vue2'
import Components from 'unplugin-vue-components/vite'
import { VuetifyResolver } from 'unplugin-vue-components/resolvers'
import { VitePWA } from 'vite-plugin-pwa'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue2(),
    legacy({
      targets: ['ie >= 11'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
    }),
    Components({
      resolvers: [VuetifyResolver()],
    }),
    VitePWA({
      registerType: 'prompt',
      injectRegister: 'auto',
      // devOptions: {
      //   enabled: true,
      // },
      manifest: {
        name: 'Simperum',
        short_name: 'Simperum',
        description: 'Sistem Informasi Perumahan Disperakim Jawa Tengah',
        // theme_color: '#262626',
        background_color: '#ffffff',
        icons: [
          {
            src: 'imgs/pwa144.png',
            sizes: '144x144',
            type: 'image/png',
            purpose: 'maskable',
          },
          {
            src: 'imgs/pwa512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'maskable',
          },
        ],
      },
      injectManifest: {
        maximumFileSizeToCacheInBytes: 3000000
      },
      workbox: {
        maximumFileSizeToCacheInBytes: 3000000,
        clientsClaim: true,
        skipWaiting: false, // We'll handle this manually for better UX
        navigateFallbackDenylist: [/\/uploads/, /\/reports?/],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'google-fonts-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
              },
              cacheKeyWillBeUsed: async ({ request }) => {
                return `${request.url}?${Date.now()}`
              },
            },
          },
        ],
      },
      // Use custom service worker with SKIP_WAITING message handling
      strategies: 'injectManifest',
      srcDir: 'src',
      filename: 'sw.js',
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})
