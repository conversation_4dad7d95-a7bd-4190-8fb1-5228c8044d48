/*
    OFE Variable Library
*/

// Font
$fontFamily: "Montserrat", sans-serif;

// Blue
$ofe-darkest-blue: #2a5370;
$ofe-darker-blue: #367ead;
$ofe-primary-blue: #3facef;
$ofe-light-blue: #afd5f8;
$ofe-lighter-blue: #e5f1fd;
$ofe-lightest-blue: #f2f8fe;

// Cool
$ofe-darkest-cool: #3f4c54;
$ofe-darker-cool: #5c717f;
$ofe-secondary-cool: #7999ae;
$ofe-light-cool: #c3cfd9;
$ofe-lighter-cool: #dae4ea;
$ofe-lightest-cool: #f0f4f7;

// Warm
$ofe-darkest-warm: #4d4945;
$ofe-darker-warm: #868079;
$ofe-secondary-warm: #9a948b;
$ofe-light-warm: #bfbbb5;
$ofe-lighter-warm: #e5e2de;
$ofe-lightest-warm: #f2f0ed;
$ofe-light-secondary-warm: #e5e4e1;

// Black
$ofe-black: #1a1a1a;
$ofe-black-10: rgba(0, 0, 0, 0.1);
$ofe-black-50: rgba(0, 0, 0, 0.5);
$ofe-black-75: rgba(0, 0, 0, 0.75);

// Gray
$ofe-dark-gray: #404040;
$ofe-mid-gray: #808080;
$ofe-light-gray: #b3b3b3;
$ofe-lighter-gray: #d9d9d9;
$ofe-lightest-gray: #fafafa;

// White Transparent
$ofe-white-10: rgba(255, 255, 255, 0.1);
$ofe-white-50: rgba(255, 255, 255, 0.5);
$ofe-white-75: rgba(255, 255, 255, 0.75);
$ofe-white-95: rgba(255, 255, 255, 0.95);

// Red
$ofe-darker-red: #ce4142;
$ofe-error-red: #f25758;
$ofe-light-red: #f58081;
$ofe-lighter-red: #fcddde;
$ofe-lightest-red: #ffeded;

// Notification Color
$ofe-success-green: #4ce64c;
$ofe-warning-orange: #f78b48;
