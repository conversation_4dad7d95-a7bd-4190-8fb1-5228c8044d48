<template>
  <v-row>
    <v-dialog v-model="isShow" max-width="400">
      <v-card>
        <v-card-title class="headline">Ganti Password</v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <!-- <Input
            label="Password Lama"
            type="password"
            :value.sync="forms.OldPassword"
          />
          <br /> -->
          <Input
            label="Password Baru"
            type="password"
            :value.sync="forms.NewPassword"
          />
          <Input
            label="Ulangi Password"
            type="password"
            :value.sync="forms.RptPassword"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="info darken-1" text @click="handleCancel">
            Batal
          </v-btn>
          <v-btn color="success" text @click="handleSave"> Simpan </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-row>
</template>

<script>
export default {
  data() {
    return {
      forms: {},
    }
  },
  props: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  },
  computed: {
    isShow: {
      get() {
        return this.show || false
      },
      set(val) {
        this.$emit('update:show', val)
      },
    },
  },
  methods: {
    async handleSave() {
      let d = await this.$api.call('Arch_SavPassword', this.forms)
      if (d.success) this.isShow = false
    },
    handleCancel() {
      this.isShow = false
    },
  },
}
</script>
