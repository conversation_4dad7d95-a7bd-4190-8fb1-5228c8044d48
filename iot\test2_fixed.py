#!/usr/bin/env python3
"""Fixed Bluetooth device discovery using PyBluez and Windows BLE scanning."""

import asyncio
import sys
from bleak import BleakClient, BleakScanner

async def scan_ble_fixed():
    """Scan for BLE devices with proper device handling."""
    print("\nScanning for BLE devices...")
    
    # Use modern discovery with callback
    devices_with_data = []
    
    def detection_callback(device, advertisement_data):
        devices_with_data.append((device, advertisement_data))
    
    scanner = BleakScanner(detection_callback=detection_callback)
    await scanner.start()
    await asyncio.sleep(10.0)
    await scanner.stop()
    
    print(f"\nFound {len(devices_with_data)} BLE devices:")
    
    for device, adv_data in devices_with_data:
        print(f"   {device.address} - {device.name or 'Unknown'}")
        print(f"   RSSI: {adv_data.rssi if adv_data else 'N/A'} dBm")
        
        # Use the device object directly from discovery
        try:
            print(f"   ✓ Device object available: {device}")
            
            # You can now use this device object directly with BleakClient
            # async with BleakClient(device) as client:
            #     # Your connection code here
            #     pass
            
        except Exception as e:
            print(f"   Error accessing device: {e}")

async def find_and_connect_device(target_address):
    """Find a specific device by address and attempt connection."""
    print(f"\nLooking for device: {target_address}")
    
    # Method 1: Try direct discovery with filtering
    devices = await BleakScanner.discover(timeout=10.0)
    
    target_device = None
    for device in devices:
        if device.address.lower() == target_address.lower():
            target_device = device
            break
    
    if target_device:
        print(f"✓ Found device: {target_device}")
        return target_device
    else:
        print(f"✗ Device {target_address} not found")
        
        # Method 2: Try find_device_by_address as fallback
        print("Trying find_device_by_address as fallback...")
        try:
            device = await BleakScanner.find_device_by_address(
                target_address, 
                timeout=10.0
            )
            if device:
                print(f"✓ Found via fallback: {device}")
                return device
            else:
                print("✗ Still not found via fallback")
        except Exception as e:
            print(f"✗ Error in fallback: {e}")
    
    return None


async def main():
    """Main function."""
    if len(sys.argv) > 1:
        # Test specific device address
        target_address = sys.argv[1]
        device = await find_and_connect_device(target_address)
        
        if device:
            print(f"\nReady to connect to: {device.address}")
            # You can add connection code here
            # async with BleakClient(device) as client:
            #     services = await client.get_services()
            #     print("Connected and retrieved services")
    else:
        # Run general scan
        await scan_ble_fixed()

if __name__ == "__main__":
    asyncio.run(main())