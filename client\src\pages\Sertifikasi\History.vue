<template>
  <Modal title="SERTIFIKAT" :show.sync="xshow" width="400px" :disabled="true">
    <div>
      <Grid
        :datagrid.sync="datagrid"
        dbref="UJI_SelSertifikatHist"
        :dbparams="dbparams"
        :disabled="true"
        :columns="[
          {
            name: 'No',
            value: 'SerialNumber',
          },
          {
            name: '<PERSON><PERSON><PERSON>',
            value: 'LkSignedUrl',
          },
          {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            value: 'SignedUrl',
          },
          {
            name: 'Tgl. Dihapus',
            value: 'DeletedAt',
          },
        ]"
      >
        <template v-slot:row-LkSignedUrl="{ row }">
          <a
            :href="$api.url + '/' + row.LkSignedUrl"
            target="_blank"
            v-show="row.LkSignedUrl"
            >Download</a
          >
        </template>
        <template v-slot:row-SignedUrl="{ row }">
          <a
            :href="$api.url + '/' + row.SignedUrl"
            target="_blank"
            v-show="row.SignedUrl"
            >Download</a
          >
        </template>
        <template v-slot:row-DeletedAt="{ row }">
          {{ row.DeletedAt | format }}
        </template>
      </Grid>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    datagrid: [],
  }),
  props: {
    show: Boolean,
    permohonanId: [Number, String],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  computed: {
    dbparams() {
      return { PermohonanID: this.permohonanId }
    },
  },
  methods: {
    async Save() {
      let ret = await this.$api.call('', {})
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
