<template>
  <div>
    <BarChart
      class="xbar-chart"
      :chart-data="datacollection"
      :options="chartOptions"
    />
    <div style="font-size: 14px">
      <div style="display: flex; padding: 5px 0; font-weight: bold">
        PAD Tahun {{ tahun }}:
        <v-spacer />
        {{ totalPAD | format }}
      </div>
      <div
        v-for="(key, idx) in Object.keys(lastData)"
        :key="idx"
        style="display: flex"
      >
        <v-icon small :color="colors[key]" style="margin-right: 5px"
          >mdi-circle
        </v-icon>
        {{ key.replace(/_/g, ' ') }}:
        <v-spacer />
        {{ lastData[key] | format }}
      </div>
    </div>
  </div>
</template>
<script>
import BarChart from '../../components/Charts/Bar.vue'
export default {
  components: {
    BarChart,
  },
  data: () => ({
    progress: {},
    datacollection: {},
    lastData: {},
    totalPAD: 0,
    chartVal: 0,
    bulan: [
      'Januari',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      'April',
      '<PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON>gus<PERSON>',
      'September',
      '<PERSON><PERSON><PERSON>',
      'November',
      '<PERSON>ember',
    ],
    colors: {
      APBN: '#E91E63',
      APBD_1_BMCK: '#b71c1c',
      APBD_1_NON: '#3F51B5',
      APBD_2: '#1A237E',
      Swasta: '#2196F3',
      Perorangan: '#0D47A1',
      Lainnya: '#009688',
      CSR_ALL: '#006064',
    },
    chartOptions: {
      title: {
        text: 'Sumber per Bulan',
      },
      legend: false,
      scales: {
        yAxes: [
          {
            ticks: {
              callback: function (value) {
                if (value / 1000000 > 1) return value / 1000000 + 'M'
                else if (value / 1000 > 1) return value / 1000 + 'K'
                else return value
              },
            },
          },
        ],
      },
      tooltips: {
        callbacks: {
          label: function (tooltipItem) {
            let label = [
              'APBN',
              'APBD BMCK',
              'APBD NON',
              'APBD KAB',
              'SWASTA',
              'PERORANGAN',
              'LAINNYA',
            ]
            return (
              label[tooltipItem.datasetIndex] +
              ' : ' +
              tooltipItem.yLabel
                .toFixed(2)
                .replace(/\d(?=(\d{3})+\.)/g, '$&.')
                .replace(/.00$/, '')
            )
          },
        },
      },
      responsive: true,
      maintainAspectRatio: false,
    },
  }),
  props: {
    tahun: {
      type: [String, Number],
      default: () => {
        return new Date().getFullYear()
      },
    },
  },
  // created() {
  //   this.populateChart()
  // },
  watch: {
    tahun: {
      immediate: true,
      handler() {
        this.populateChart()
      },
    },
  },
  methods: {
    async populateChart() {
      this.totalPAD = 0
      let { data } = await this.$api.call('UJI.ChartSumberBulanan', {
        nocache: true,
        TipeData: this.tipeData,
        Tahun: this.tahun,
      })
      let labels = []
      let ds = []
      this.lastData = {}
      data.forEach((d) => {
        labels.push(d.Tahun)
        let i = 0
        let subtotal = 0
        Object.keys(d).forEach((k) => {
          if (this.colors[k]) {
            ds[i] = ds[i] || {
              data: [],
              baseColor: this.colors[k],
              pointStyle: 'rectRot',
              pointRadius: 3,
            }
            ds[i].label = k.replace('_', ' ')
            ds[i].stack = k.match(/ALL$/) ? 2 : 1
            ds[i].data.push(d[k])
            // this.lastData[k] = (this.lastData[k] || 0) + d[k]
            this.totalPAD += d[k]
            subtotal += d[k]
            i++
          }
        })
        this.lastData[this.bulan[d.Bulan - 1]] = subtotal
      })
      this.datacollection = {
        labels: labels,
        datasets: ds,
      }
    },
  },
}
</script>
