!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}var e=["aria-atomic","aria-label","aria-live","role","title"];function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",o=i.toStringTag||"@@toStringTag";function s(n,i,r,o){var s=i&&i.prototype instanceof u?i:u,c=Object.create(s.prototype);return a(c,"_invoke",function(n,a,i){var r,o,s,u=0,c=i||[],d=!1,h={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,n){return r=e,o=0,s=t,h.n=n,l}};function f(n,a){for(o=n,s=a,e=0;!d&&u&&!i&&e<c.length;e++){var i,r=c[e],f=h.p,m=r[2];n>3?(i=m===a)&&(s=r[(o=r[4])?5:(o=3,3)],r[4]=r[5]=t):r[0]<=f&&((i=n<2&&f<r[1])?(o=0,h.v=a,h.n=r[1]):f<m&&(i=n<3||r[0]>a||a>m)&&(r[4]=n,r[5]=a,h.n=m,o=0))}if(i||n>1)return l;throw d=!0,a}return function(i,c,m){if(u>1)throw TypeError("Generator is already running");for(d&&1===c&&f(c,m),o=c,s=m;(e=o<2?t:s)||!d;){r||(o?o<3?(o>1&&(h.n=-1),f(o,s)):h.n=s:h.v=s);try{if(u=2,r){if(o||(i="next"),e=r[i]){if(!(e=e.call(r,s)))throw TypeError("iterator result is not an object");if(!e.done)return e;s=e.value,o<2&&(o=0)}else 1===o&&(e=r.return)&&e.call(r),o<2&&(s=TypeError("The iterator does not provide a '"+i+"' method"),o=1);r=t}else if((e=(d=h.n<0)?s:n.call(a,h))!==l)break}catch(e){r=t,o=1,s=e}finally{u=1}}return{value:e,done:d}}}(n,r,o),!0),c}var l={};function u(){}function c(){}function d(){}e=Object.getPrototypeOf;var h=[][r]?e(e([][r]())):(a(e={},r,function(){return this}),e),f=d.prototype=u.prototype=Object.create(h);function m(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,a(t,o,"GeneratorFunction")),t.prototype=Object.create(f),t}return c.prototype=d,a(f,"constructor",d),a(d,"constructor",c),c.displayName="GeneratorFunction",a(d,o,"GeneratorFunction"),a(f),a(f,o,"Generator"),a(f,r,function(){return this}),a(f,"toString",function(){return"[object Generator]"}),(n=function(){return{w:s,m:m}})()}function a(t,e,n,i){var r=Object.defineProperty;try{r({},"",{})}catch(t){r=0}a=function(t,e,n,i){function o(e,n){a(t,e,function(t){return this._invoke(e,n,t)})}e?r?r(t,e,{value:n,enumerable:!i,configurable:!i,writable:!i}):t[e]=n:(o("next",0),o("throw",1),o("return",2))},a(t,e,n,i)}function i(t,e,n,a,i,r,o){try{var s=t[r](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(a,i)}function r(t){return function(){var e=this,n=arguments;return new Promise(function(a,r){var o=t.apply(e,n);function s(t){i(o,a,r,s,l,"next",t)}function l(t){i(o,a,r,s,l,"throw",t)}s(void 0)})}}function o(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return s(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var a=0,i=function(){};return{s:i,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,o=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return o=t.done,t},e:function(t){l=!0,r=t},f:function(){try{o||null==n.return||n.return()}finally{if(l)throw r}}}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=Array(e);n<e;n++)a[n]=t[n];return a}function l(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,a)}return n}function u(e,n,a){return(n=function(e){var n=function(e,n){if("object"!=t(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var i=a.call(e,n||"default");if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"==t(n)?n:n+""}(n))in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a,e}System.register(["./index-legacy-BUdDePUl.js","./VListItemAction-legacy-B1rbhhFI.js"],function(t,a){"use strict";var i,s,c,d,h,f,m,p,g,v,b,y,w,_,P,I,S,x,B,D,j,k,T,O,C,N,M;return{setters:[function(t){i=t.x,s=t.H,c=t.J,d=t.T,h=t.K,f=t.C,m=t.y,p=t.g,g=t.D,v=t.n,b=t.L,y=t.N,w=t.r,_=t.l,P=t.s,I=t.E,S=t.u,x=t.F,B=t.t,D=t.q,j=t._,k=t.j,T=t.k,O=t.v,C=t.i,N=t.h},function(t){M=t._}],execute:function(){var a=document.createElement("style");a.textContent='.theme--light.v-badge .v-badge__badge:after{border-color:#fff}.theme--dark.v-badge .v-badge__badge:after{border-color:#1e1e1e}.v-badge{display:inline-block;line-height:1;position:relative}.v-badge__badge{border-radius:10px;color:#fff;display:inline-block;font-size:12px;height:20px;letter-spacing:0;line-height:1;min-width:20px;padding:4px 6px;pointer-events:auto;position:absolute;text-align:center;text-indent:0;top:auto;transition:.3s cubic-bezier(.25,.8,.5,1);white-space:nowrap}.v-application--is-ltr .v-badge__badge{right:auto}.v-application--is-rtl .v-badge__badge{left:auto}.v-badge__badge .v-icon{color:inherit;font-size:12px;height:12px;margin:0 -2px;width:12px}.v-badge__badge .v-img{height:12px;width:12px}.v-badge__wrapper{flex:0 1;height:100%;left:0;pointer-events:none;position:absolute;top:0;width:100%}.v-badge--avatar .v-badge__badge{padding:0}.v-badge--avatar .v-badge__badge .v-avatar{height:20px!important;min-width:0!important;max-width:20px!important}.v-badge--bordered .v-badge__badge:after{border-radius:inherit;border-width:2px;border-style:solid;bottom:0;content:"";left:0;position:absolute;right:0;top:0;transform:scale(1.15)}.v-badge--dot .v-badge__badge{border-radius:4.5px;height:9px;min-width:0;padding:0;width:9px}.v-badge--dot .v-badge__badge:after{border-width:1.5px}.v-badge--icon .v-badge__badge{padding:4px 6px}.v-badge--inline{align-items:center;display:inline-flex;justify-content:center}.v-badge--inline .v-badge__badge,.v-badge--inline .v-badge__wrapper{position:relative}.v-badge--inline .v-badge__wrapper{margin:0 4px}.v-badge--tile .v-badge__badge{border-radius:0}.is-mobile .btn-full{width:100%;margin-top:10px}.is-mobile .panel-pengajuan{width:100vw!important;top:60px!important;height:calc(100vh - 60px)!important;left:0!important}.main-panel{max-width:1170px;margin:auto;padding:12px 0}.bottom-panel{position:fixed;bottom:0;left:0;padding:20px;width:100%;text-align:center}@media screen and (min-width: 320px){.main-panel{width:calc(100vw - 40px)}}@media screen and (min-width: 768px){.main-panel{width:calc(100vw - 100px)}}\n/*$vite$:1*/',document.head.appendChild(a);var $=i(f,h(["left","bottom"]),d,c,s).extend({name:"v-badge",props:{avatar:Boolean,bordered:Boolean,color:{type:String,default:"primary"},content:{required:!1},dot:Boolean,label:{type:String,default:"$vuetify.badge"},icon:String,inline:Boolean,offsetX:[Number,String],offsetY:[Number,String],overlap:Boolean,tile:Boolean,transition:{type:String,default:"scale-rotate-transition"},value:{default:!0}},computed:{classes:function(){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?l(Object(n),!0).forEach(function(e){u(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({"v-badge--avatar":this.avatar,"v-badge--bordered":this.bordered,"v-badge--bottom":this.bottom,"v-badge--dot":this.dot,"v-badge--icon":null!=this.icon,"v-badge--inline":this.inline,"v-badge--left":this.left,"v-badge--overlap":this.overlap,"v-badge--tile":this.tile},this.themeClasses)},computedBottom:function(){return this.bottom?"auto":this.computedYOffset},computedLeft:function(){return this.isRtl?this.left?this.computedXOffset:"auto":this.left?"auto":this.computedXOffset},computedRight:function(){return this.isRtl?this.left?"auto":this.computedXOffset:this.left?this.computedXOffset:"auto"},computedTop:function(){return this.bottom?this.computedYOffset:"auto"},computedXOffset:function(){return this.calcPosition(this.offsetX)},computedYOffset:function(){return this.calcPosition(this.offsetY)},isRtl:function(){return this.$vuetify.rtl},offset:function(){return this.overlap?this.dot?8:12:this.dot?2:4},styles:function(){return this.inline?{}:{bottom:this.computedBottom,left:this.computedLeft,right:this.computedRight,top:this.computedTop}}},methods:{calcPosition:function(t){return"calc(100% - ".concat(g(t||this.offset),")")},genBadge:function(){var t=this.$vuetify.lang,e=this.$attrs["aria-label"]||t.t(this.label),n=this.setBackgroundColor(this.color,{staticClass:"v-badge__badge",style:this.styles,attrs:{"aria-atomic":this.$attrs["aria-atomic"]||"true","aria-label":e,"aria-live":this.$attrs["aria-live"]||"polite",title:this.$attrs.title,role:this.$attrs.role||"status"},directives:[{name:"show",value:this.isActive}]}),a=this.$createElement("span",n,[this.genBadgeContent()]);return this.transition?this.$createElement("transition",{props:{name:this.transition,origin:this.origin,mode:this.mode}},[a]):a},genBadgeContent:function(){if(!this.dot){var t=m(this,"badge");return t||(this.content?String(this.content):this.icon?this.$createElement(p,this.icon):void 0)}},genBadgeWrapper:function(){return this.$createElement("span",{staticClass:"v-badge__wrapper"},[this.genBadge()])}},render:function(t){var n=[this.genBadgeWrapper()],a=[m(this)],i=this.$attrs,r=(i["aria-atomic"],i["aria-label"],i["aria-live"],i.role,i.title,function(t,e){if(null==t)return{};var n,a,i=function(t,e){if(null==t)return{};var n={};for(var a in t)if({}.hasOwnProperty.call(t,a)){if(-1!==e.indexOf(a))continue;n[a]=t[a]}return n}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(a=0;a<r.length;a++)n=r[a],-1===e.indexOf(n)&&{}.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}(i,e));return this.inline&&this.left?a.unshift(n):a.push(n),t("span",{staticClass:"v-badge",attrs:r,class:this.classes},a)}}),J=v({components:{Bayar:y,Pengajuan:b},data:function(){return{showPengajuan:!1,billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0,step:0},permohonanId:0,itemMenu:[{text:"Detail"}],datalist:[],statusId:"1",rebind:1}},computed:{dbparams:function(){return{StatusID:this.statusId}}},watch:{"billing.show":function(t){t||(this.rebind++,this.billing.TotalBayar=0)}},mounted:function(){this.statusId="1"},methods:{ItemClick:function(){this.billing.TotalBayar=0;var t,e=[],n=o(this.datalist);try{for(n.s();!(t=n.n()).done;){var a=t.value;a.checked&&(this.billing.TotalBayar+=a.TotalBayar,e.push(a.PermohonanID))}}catch(i){n.e(i)}finally{n.f()}this.billing.PermohonanID=e.join(",")},ChangeStatus:function(t){this.statusId=t},ShowBilling:function(t){var e=this;return r(n().m(function a(){var i;return n().w(function(n){for(;;)switch(n.n){case 0:return n.n=1,e.$api.call("UJI_SelBilling",{PermohonanID:t});case 1:(i=n.v).success&&(e.billing={show:!0,step:1,total:i.data[0].TotalBayar,billingId:i.data[0].BillingID,PaymentType:"transfer"});case 2:return n.a(2)}},a)}))()},ShowDetail:function(t){this.permohonanId=t,this.showPengajuan=!0},Delete:function(t){var e=this;return r(n().m(function a(){return n().w(function(n){for(;;)switch(n.n){case 0:if(!confirm("Hapus Pengujian?")){n.n=2;break}return n.n=1,e.$api.call("UJI_DelPermohonan",{PermohonanID:t,Keterangan:"Dihapus Pelanggan"});case 1:e.rebind++,e.billing.TotalBayar=0;case 2:return n.a(2)}},a)}))()},ItemMenuClick:function(t,e){"Detail"==t.text?this.ShowDetail(e.PermohonanID):"Hapus"==t.text&&this.Delete(e.PermohonanID)},ClosePengajuan:function(){this.showPengajuan=!1,this.rebind++,this.billing.TotalBayar=0}}},function(){var t=this,e=t._self._c;return e("div",[e("div",{directives:[{name:"show",rawName:"v-show",value:!t.datalist.length,expression:"!datalist.length"}],staticStyle:{padding:"50px",height:"calc(100vh - 156px)","text-align":"center"}},[t._v(" TIDAK ADA DATA PERMOHONAN BARU ")]),e(w,[e(_,{directives:[{name:"show",rawName:"v-show",value:t.datalist.length,expression:"datalist.length"}],staticStyle:{height:"calc(100vh - 136px)"},attrs:{dbref:"UJI_SelPermohonanList",items:t.datalist,dbparams:t.dbparams,rebind:t.rebind,selectOnLoad:!0},on:{"update:items":function(e){t.datalist=e}},scopedSlots:t._u([{key:"default",fn:function(n){var a=n.row;return[e(P,[e(I,[e(S,[t._v(t._s(a.NamaPelanggan))]),e(x,[t._v(" "+t._s(a.NoPengujian)+" | "+t._s(a.JenisUji)+" ")]),e(x,{staticStyle:{"font-weight":"bold"}},[t._v(" Rp. "+t._s(t._f("format")(a.TotalBayar))+" ")])],1),e(B,[e(D,{attrs:{"offset-y":""},scopedSlots:t._u([{key:"activator",fn:function(n){var a=n.on;return[e(p,t._g({},a),[t._v(" mdi-dots-vertical")])]}}],null,!0)},[e(w,{attrs:{dense:""}},t._l(t.itemMenu,function(n,i){return e(P,{key:i,on:{click:function(e){return t.ItemMenuClick(n,a)}}},[e(I,{style:{color:n.color||"#333"}},[e(S,[t._v(t._s(n.text))])],1)],1)}),1)],1)],1)],1)]}}])})],1),e("div",{staticClass:"bottom-panel"},[e(j,{staticClass:"btn-full",staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:function(e){t.showPengajuan=!0}}},[e(p,{attrs:{left:""}},[t._v("mdi-plus")]),t._v(" PERMOHONAN BARU ")],1)],1),e("Bayar",{attrs:{forms:t.billing},on:{"update:forms":function(e){t.billing=e},refresh:function(e){return t.$emit("refresh")}}}),t.showPengajuan?e("Pengajuan",{attrs:{permohonanId:t.permohonanId},on:{close:t.ClosePengajuan}}):t._e()],1)},[],!1,null,null).exports,U=v({components:{Bayar:y,Pengajuan:b},data:function(){return{showPengajuan:!1,billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0,step:0},permohonanId:0,itemMenu:[{text:"Detail"}],datalist:[],statusId:"2",rebind:1}},computed:{dbparams:function(){return{StatusID:this.statusId}}},watch:{"billing.show":function(t){t||(this.rebind++,this.billing.TotalBayar=0)}},mounted:function(){this.statusId="2"},methods:{ItemClick:function(){this.billing.TotalBayar=0;var t,e=[],n=o(this.datalist);try{for(n.s();!(t=n.n()).done;){var a=t.value;a.checked&&(this.billing.TotalBayar+=a.TotalBayar,e.push(a.PermohonanID))}}catch(i){n.e(i)}finally{n.f()}this.billing.PermohonanID=e.join(",")},ChangeStatus:function(t){this.statusId=t},ShowBilling:function(t){var e=this;return r(n().m(function a(){var i;return n().w(function(n){for(;;)switch(n.n){case 0:return n.n=1,e.$api.call("UJI_SelBilling",{PermohonanID:t});case 1:(i=n.v).success&&(e.billing={show:!0,step:1,total:i.data[0].TotalBayar,billingId:i.data[0].BillingID,PaymentType:"transfer"});case 2:return n.a(2)}},a)}))()},ShowDetail:function(t){this.permohonanId=t,this.showPengajuan=!0},Delete:function(t){var e=this;return r(n().m(function a(){return n().w(function(n){for(;;)switch(n.n){case 0:if(!confirm("Hapus Pengujian?")){n.n=2;break}return n.n=1,e.$api.call("UJI_DelPermohonan",{PermohonanID:t,Keterangan:"Dihapus Pelanggan"});case 1:e.rebind++,e.billing.TotalBayar=0;case 2:return n.a(2)}},a)}))()},ItemMenuClick:function(t,e){"Detail"==t.text?this.ShowDetail(e.PermohonanID):"Hapus"==t.text&&this.Delete(e.PermohonanID)},ClosePengajuan:function(){this.showPengajuan=!1,this.rebind++,this.billing.TotalBayar=0}}},function(){var t=this,e=t._self._c;return e("div",[t.datalist.length?e("div",{staticStyle:{padding:"20px 50px","text-align":"center","background-color":"#f3f3f3"}},[t._v(" Anda memiliki waktu "),e("b",[t._v("14 hari")]),t._v(" untuk menyerahkan contoh uji ")]):e("div",{staticStyle:{padding:"50px",height:"calc(100vh - 156px)","text-align":"center"}},[t._v(" TIDAK ADA PERMOHONAN YANG BUTUH DIKIRIMKAN ")]),e(w,[e(_,{directives:[{name:"show",rawName:"v-show",value:t.datalist.length,expression:"datalist.length"}],staticStyle:{height:"calc(100vh - 199px)"},attrs:{dbref:"UJI_SelPermohonanList",items:t.datalist,dbparams:t.dbparams,rebind:t.rebind,selectOnLoad:!0},on:{"update:items":function(e){t.datalist=e}},scopedSlots:t._u([{key:"default",fn:function(n){var a=n.row;return[e(P,[e(I,[e(S,[t._v(t._s(a.NamaPelanggan))]),e(x,[t._v(" "+t._s(a.NoPengujian)+" | "+t._s(a.JenisUji)+" ")]),e(x,{staticStyle:{"font-weight":"bold"}},[t._v(" Rp. "+t._s(t._f("format")(a.TotalBayar))+" ")])],1),e(B,[e(D,{attrs:{"offset-y":""},scopedSlots:t._u([{key:"activator",fn:function(n){var a=n.on;return[e(p,t._g({},a),[t._v(" mdi-dots-vertical")])]}}],null,!0)},[e(w,{attrs:{dense:""}},t._l(t.itemMenu,function(n,i){return e(P,{key:i,on:{click:function(e){return t.ItemMenuClick(n,a)}}},[e(I,{style:{color:n.color||"#333"}},[e(S,[t._v(t._s(n.text))])],1)],1)}),1)],1)],1)],1)]}}])})],1),e("div",{staticStyle:{position:"fixed",bottom:"0px",padding:"10px 0",width:"calc(100vw - 33px)"}},[e(j,{directives:[{name:"show",rawName:"v-show",value:this.billing.TotalBayar,expression:"this.billing.TotalBayar"}],staticClass:"btn-full",staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:function(e){t.billing.show=!0}}},[t._v(" BAYAR: Rp "+t._s(t._f("format")(this.billing.TotalBayar))+",- ")])],1),e("Bayar",{attrs:{forms:t.billing},on:{"update:forms":function(e){t.billing=e},refresh:function(e){return t.$emit("refresh")}}}),t.showPengajuan?e("Pengajuan",{attrs:{permohonanId:t.permohonanId},on:{close:t.ClosePengajuan}}):t._e()],1)},[],!1,null,null).exports,A=v({components:{Bayar:y},data:function(){return{billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0,step:0},itemMenu:[{text:"Detail Pembayaran"}],datalist:[],statusId:"3",rebind:1}},computed:{dbparams:function(){return{StatusID:this.statusId}}},watch:{"billing.show":function(t){t||(this.Populate(),this.billing.TotalBayar=0)}},mounted:function(){this.Populate()},methods:{ItemClick:function(){this.billing.TotalBayar=0;var t,e=[],n=o(this.datalist);try{for(n.s();!(t=n.n()).done;){var a=t.value;a.checked&&(this.billing.TotalBayar+=a.TotalBayar,e.push(a.PermohonanID))}}catch(i){n.e(i)}finally{n.f()}this.billing.PermohonanID=e.join(",")},Populate:function(){var t=this;return r(n().m(function e(){var a,i,r,o,s;return n().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("UJI_SelPermohonanList",{StatusID:t.statusId});case 1:for(a=e.v,i=[],r="xxx",o=0;o<a.data.length;o++)a.data[o].BillingID&&a.data[o].BillingID==r?((s=i[i.length-1]).NamaPelanggan+=", "+a.data[o].NamaPelanggan,s.TotalBayar+=a.data[o].TotalBayar,s.JenisUji!=a.data[o].JenisUji&&(s.JenisUji+=a.data[o].JenisUji)):i.push(a.data[o]),r=a.data[o].BillingID;t.datalist=i;case 2:return e.a(2)}},e)}))()},ItemMenuClick:function(t,e){"Detail Pembayaran"==t.text&&this.ShowBilling(e.PermohonanID)},ShowBilling:function(t){var e=this;return r(n().m(function a(){var i;return n().w(function(n){for(;;)switch(n.n){case 0:return n.n=1,e.$api.call("UJI_SelBilling",{PermohonanID:t});case 1:(i=n.v).success&&(e.billing={show:!0,step:1,total:i.data[0].TotalBayar,billingId:i.data[0].BillingID,expiredDate:O(i.data[0].ExpiredDate).add(7,"hour").format("DD-MMM-YYYY HH:mm:ss"),PaymentType:"transfer"});case 2:return n.a(2)}},a)}))()}}},function(){var t=this,e=t._self._c;return e("div",[e(_,{staticStyle:{height:"calc(100vh - 194px)"},attrs:{items:t.datalist,selectOnLoad:!0},scopedSlots:t._u([{key:"default",fn:function(n){var a=n.row;return[e(P,[e(M,{directives:[{name:"show",rawName:"v-show",value:!a.BillingID,expression:"!row.BillingID"}]},[e(k,{attrs:{value:a.checked},on:{"update:value":function(e){return t.$set(a,"checked",e)},click:t.ItemClick}})],1),e(I,[e(S,[t._v(t._s(a.NamaPelanggan))]),e(x,[t._v(" "+t._s(a.NoPengujian)+" | "+t._s(a.JenisUji)+" ")]),e(x,{staticStyle:{display:"flex"}},[e("div",{staticStyle:{"font-weight":"bold"}},[t._v(" Rp. "+t._s(t._f("format")(a.TotalBayar))+" ")]),e(T),a.BayarDate?e("div",{staticStyle:{color:"green"}},[t._v("Sudah Dibayar")]):a.BillingID?e("div",{staticStyle:{color:"orangered"}},[t._v(" Menunggu Pembayaran ")]):e("div",{staticStyle:{color:"red"}},[t._v("Belum Dibayar")])],1)],1),e(B,{directives:[{name:"show",rawName:"v-show",value:a.BillingID,expression:"row.BillingID"}]},[e(D,{attrs:{"offset-y":""},scopedSlots:t._u([{key:"activator",fn:function(n){var a=n.on;return[e(p,t._g({},a),[t._v(" mdi-dots-vertical")])]}}],null,!0)},[e(w,{attrs:{dense:""}},t._l(t.itemMenu,function(n,i){return e(P,{key:i,on:{click:function(e){return t.ItemMenuClick(n,a)}}},[e(I,{style:{color:n.color||"#333"}},[e(S,[t._v(t._s(n.text))])],1)],1)}),1)],1)],1)],1)]}}])}),e("div",{staticStyle:{height:"40px"}},[e(j,{directives:[{name:"show",rawName:"v-show",value:t.billing.TotalBayar,expression:"billing.TotalBayar"}],staticClass:"btn-full",staticStyle:{"margin-left":"5px","margin-top":"-50px"},attrs:{color:"primary"},on:{click:function(e){t.billing.show=!0}}},[t._v(" BAYAR: Rp "+t._s(t._f("format")(this.billing.TotalBayar))+",- ")])],1),e("Bayar",{attrs:{forms:t.billing},on:{"update:forms":function(e){t.billing=e},refresh:function(e){return t.$emit("refresh")}}})],1)},[],!1,null,null).exports,L=v({data:function(){return{datagrid:[],rebind:0,dbparams:{PermohonanID:0}}},props:{data:Object},watch:{"data.PermohonanID":function(t){this.dbparams.PermohonanID=t,this.rebind++}}},function(){var t=this,e=t._self._c;return e(C,{attrs:{title:"Detail Pengujian",show:t.data.show},on:{"update:show":function(e){return t.$set(t.data,"show",e)}}},[e(N,{attrs:{datagrid:t.datagrid,dbref:"UJI.PermohonanDet",dbparams:t.dbparams,disabled:!0,doRebind:t.rebind,columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Nama Contoh",value:"NamaContoh"},{name:"Metode",value:"Metode"},{name:"Masalah",value:"Masalah"},{name:"Status",value:"StatusName"}]},on:{"update:datagrid":function(e){t.datagrid=e}}})],1)},[],!1,null,null).exports,E=v({components:{ProsesDetail:L},data:function(){return{detail:{show:!1,PermohonanID:""},datalist:[],statusId:"4,5,6,7,8",rebind:1}},computed:{dbparams:function(){return{StatusID:this.statusId}}},watch:{"billing.show":function(t){t||(this.rebind++,this.billing.TotalBayar=0)}},methods:{ShowDetail:function(t){var e=this;return r(n().m(function a(){return n().w(function(n){for(;;)switch(n.n){case 0:e.detail.show=!0,e.detail.PermohonanID=t;case 1:return n.a(2)}},a)}))()}}},function(){var t=this,e=t._self._c;return e("div",[e(w,[e(_,{directives:[{name:"show",rawName:"v-show",value:t.datalist.length,expression:"datalist.length"}],staticStyle:{height:"calc(100vh - 136px)"},attrs:{dbref:"UJI_SelPermohonanList",items:t.datalist,dbparams:t.dbparams,rebind:t.rebind,selectOnLoad:!0},on:{"update:items":function(e){t.datalist=e}},scopedSlots:t._u([{key:"default",fn:function(n){var a=n.row;return[e(P,[e(I,[e(S,[t._v(t._s(a.NamaPelanggan))]),e(x,[t._v(" "+t._s(a.NoPengujian)+" | "+t._s(a.JenisUji)+" ")]),e(x,{staticStyle:{"font-weight":"bold"}},[t._v(" Rp. "+t._s(t._f("format")(a.TotalBayar))+" ")])],1),e(M,[e(j,{attrs:{small:"",outlined:"",color:"primary"},on:{click:function(e){return t.ShowDetail(a.PermohonanID)}}},[t._v("DETAIL")])],1)],1)]}}])})],1),e("ProsesDetail",{attrs:{data:t.detail}})],1)},[],!1,null,null).exports,R=v({components:{Bayar:y},data:function(){return{billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0,step:0},datalist:[],statusId:"4,5,6,7,8",rebind:1}},computed:{dbparams:function(){return{StatusID:this.statusId}}},watch:{"billing.show":function(t){t||(this.rebind++,this.billing.TotalBayar=0)}},methods:{ShowLembarKerja:function(t){var e=this;return r(n().m(function a(){return n().w(function(n){for(;;)switch(n.n){case 0:window.open(e.$api.url+"/"+t.LkSignedUrl,"_blank");case 1:return n.a(2)}},a)}))()},ShowSertifikat:function(t){var e=this;return r(n().m(function a(){return n().w(function(n){for(;;)switch(n.n){case 0:window.open(e.$api.url+t.SignedUrl.replace(/xlsx/,"pdf"),"_blank");case 1:return n.a(2)}},a)}))()}}},function(){var t=this,e=t._self._c;return e("div",[e(w,[e(_,{directives:[{name:"show",rawName:"v-show",value:t.datalist.length,expression:"datalist.length"}],staticStyle:{height:"calc(100vh - 136px)"},attrs:{dbref:"UJI_SelPermohonanList",items:t.datalist,dbparams:t.dbparams,rebind:t.rebind,selectOnLoad:!0},on:{"update:items":function(e){t.datalist=e}},scopedSlots:t._u([{key:"default",fn:function(n){var a=n.row;return[e(P,[e(I,[e(S,[t._v(t._s(a.NamaPelanggan))]),e(x,[a.LkNama?e("span",[t._v(t._s(a.LkNama.replace(/\.pdf$/,"")))]):e("span",[t._v(t._s(a.NoPengujian))]),t._v(" | "+t._s(a.JenisUji)+" ")]),e(x,{staticStyle:{"font-weight":"bold"}},[t._v(" SELESAI ")])],1),e(M,[a.LkSignedUrl?e(j,{attrs:{small:"",outlined:"",color:"primary"},on:{click:function(e){return t.ShowLembarKerja(a)}}},[e(p,[t._v("mdi-file-cad")])],1):t._e()],1),e(M,[a.SignedUrl?e(j,{attrs:{small:"",outlined:"",color:"primary"},on:{click:function(e){return t.ShowSertifikat(a)}}},[e(p,[t._v("mdi-certificate")])],1):t._e()],1)],1)]}}])})],1),e("Bayar",{attrs:{forms:t.billing},on:{"update:forms":function(e){t.billing=e}}})],1)},[],!1,null,null).exports;t("default",v({components:{PermohonanBaru:J,PenyerahanSample:U,MenungguPembayaran:A,DalamProses:E,Selesai:R},data:function(){return{billing:{show:!1,PaymentType:"cash",PermohonanID:"",TotalBayar:0},datalist:[],statusId:"1",rebind:1,counter:{}}},computed:{dbparams:function(){return{StatusID:this.statusId}}},watch:{"billing.show":function(t){t||(this.rebind++,this.billing.TotalBayar=0)}},mounted:function(){this.statusId="1",this.GetCounter()},methods:{ItemClick:function(){this.billing.TotalBayar=0;var t,e=[],n=o(this.datalist);try{for(n.s();!(t=n.n()).done;){var a=t.value;a.checked&&(this.billing.TotalBayar+=a.TotalBayar,e.push(a.PermohonanID))}}catch(i){n.e(i)}finally{n.f()}this.billing.PermohonanID=e.join(",")},ChangeStatus:function(t){this.statusId=t},GetCounter:function(){var t=this;return r(n().m(function e(){var a;return n().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("UJI_SelPermohonanCount",{NoCache:!0});case 1:a=e.v,t.counter=a.data[0];case 2:return e.a(2)}},e)}))()},ShowBilling:function(t){var e=this;return r(n().m(function a(){var i;return n().w(function(n){for(;;)switch(n.n){case 0:return n.n=1,e.$api.call("UJI_SelBilling",{PermohonanID:t});case 1:(i=n.v).success&&(e.billing={show:!0,step:1,total:i.data[0].TotalBayar,billingId:i.data[0].BillingID,PaymentType:"transfer"});case 2:return n.a(2)}},a)}))()}}},function(){var t=this,e=t._self._c;return e("div",{staticClass:"main-panel"},[e("div",{staticStyle:{display:"flex",background:"white"}},[e(j,{staticStyle:{width:"20%"},attrs:{text:"1"!=t.statusId,outlined:"1"!=t.statusId,color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("1")}}},[e($,{attrs:{color:"error",content:t.counter.JmlPermohonan,value:0}},[t.$api.isMobile()?e(p,[t._v("mdi-file-outline")]):e("span",[t._v("Permohonan Baru")])],1)],1),e(j,{staticStyle:{width:"20%"},attrs:{text:"2"!=t.statusId,outlined:"2"!=t.statusId,color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("2")}}},[e($,{attrs:{color:"error",content:t.counter.JmlMenungguSample,value:t.counter.JmlMenungguSample>0}},[t.$api.isMobile()?e(p,[t._v("mdi-paperclip")]):e("span",[t._v("Penyerahan Sample")])],1)],1),e(j,{staticStyle:{width:"20%"},attrs:{text:"3"!=t.statusId,outlined:"3"!=t.statusId,color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("3")}}},[e($,{attrs:{color:"error",content:t.counter.JmlMenungguBayar,value:t.counter.JmlMenungguBayar>0}},[t.$api.isMobile()?e(p,[t._v("mdi-hand-coin-outline")]):e("span",[t._v("Menunggu Pembayaran")])],1)],1),e(j,{staticStyle:{width:"20%"},attrs:{text:"4,5,6,7,8"!=t.statusId,outlined:"4,5,6,7,8"!=t.statusId,color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("4,5,6,7,8")}}},[e($,{attrs:{color:"error",content:t.counter.JmlDalamProses,value:t.counter.JmlDalamProses>0}},[t.$api.isMobile()?e(p,[t._v("mdi-flask-outline")]):e("span",[t._v("Dalam Proses")])],1)],1),e(j,{staticStyle:{width:"20%"},attrs:{text:"9"!=t.statusId,outlined:"9"!=t.statusId,color:"primary",elevation:"0",tile:""},on:{click:function(e){return t.ChangeStatus("9")}}},[e($,{attrs:{color:"error",content:t.counter.JmlSelesai,value:t.counter.JmlSelesai>0}},[t.$api.isMobile()?e(p,[t._v("mdi-check-decagram")]):e("span",[t._v("Sudah Selesai")])],1)],1)],1),"1"==t.statusId?e("PermohonanBaru",{on:{refresh:t.GetCounter}}):t._e(),"2"==t.statusId?e("PenyerahanSample"):t._e(),"3"==t.statusId?e("MenungguPembayaran"):t._e(),"4,5,6,7,8"==t.statusId?e("DalamProses"):t._e(),"9"==t.statusId?e("Selesai"):t._e()],1)},[],!1,null,null).exports)}}})}();
