<template>
  <div>
    <br />
    <div
      style="
        background-color: silver;
        width: 80vw;
        height: 80vw;
        border-radius: 10px;
        margin: auto;
        background-position: center;
        background-size: cover;
      "
      :style="{
        backgroundImage:
          'url(https://silakon.dpubinmarcipka.jatengprov.go.id' +
          forms.Foto +
          ')',
      }"
    ></div>
    <br />
    <div style="width: 100vw; padding: 15px">
      <div style="font-size: x-large; font-family: raleway">
        {{ forms.NamaAlat }}
      </div>
      <div style="color: gray">
        {{ forms.Merk }} / {{ forms.Tipe }} ({{ forms.Tahun }})
      </div>

      <br />
      <!-- <div v-for="act in activities" :key="act.AlatId">
              <div style="text-transform: capitalize;">
                  {{ act.Tanggal | format }} - {{ act.Aktifitas }}
              </div>
              <div style="color:gray">
                  {{ act.Keterangan }}
              </div>
            </div> -->
      <v-list subheader two-line>
        <v-list-item v-for="act in activities" :key="act.AlatId">
          <v-list-item-avatar>
            <v-img
              :src="
                'https://silakon.dpubinmarcipka.jatengprov.go.id' + act.Bukti
              "
            ></v-img>
          </v-list-item-avatar>

          <v-list-item-content>
            <v-list-item-title
              >{{ act.Tanggal | format }} -
              {{ act.Aktifitas }}</v-list-item-title
            >

            <v-list-item-subtitle>{{ act.Keterangan }}</v-list-item-subtitle>
          </v-list-item-content>

          <v-list-item-action>
            <v-btn icon @click="imageDet = act.Bukti">
              <v-icon color="grey lighten-1">mdi-information</v-icon>
            </v-btn>
          </v-list-item-action>
        </v-list-item>
      </v-list>

      <br />
      <v-btn
        v-if="user"
        style="width: 100%"
        color="primary"
        @click="showDet = true"
        >TAMBAH DATA</v-btn
      >
    </div>
    <div
      v-show="imageDet != ''"
      @click="imageDet = ''"
      style="
        width: 100vw;
        height: 100vw;
        position: absolute;
        bottom: 0;
        background: white;
        text-align: center;
        padding-top: 10vw;
        border-radius: 20px 20px 0 0;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      "
    >
      <div
        style="
          background-color: silver;
          width: 80vw;
          height: 80vw;
          border-radius: 10px;
          margin: auto;
          background-position: center;
          background-size: cover;
        "
        :style="{
          backgroundImage:
            'url(https://silakon.dpubinmarcipka.jatengprov.go.id' +
            imageDet +
            ')',
        }"
      ></div>
      <v-btn color="primary" text style="margin: auto">TUTUP</v-btn>
    </div>
    <alat-det :show.sync="showDet" :alatId="alatId" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import AlatDet from './AlatDet.vue'
export default {
  components: { AlatDet },
  data: () => ({
    alatId: null,
    forms: {},
    showDet: false,
    imageDet: '',
    activities: [],
  }),
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
  },
  created() {
    this.alatId = this.$route.params.id
    this.populate()
  },
  methods: {
    async populate() {
      let d = await this.$api.call('WEB.SelAlat', { AlatId: this.alatId })
      this.forms = d.data?.[0] || {}

      let a = await this.$api.call('WEB.SelAlatActivity', {
        AlatId: this.alatId,
      })
      this.activities = a.data || []
    },
  },
}
</script>
