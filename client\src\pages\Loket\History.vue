<template>
  <Modal
    title="HISTORY PENDAFTARAN"
    :show.sync="xshow"
    width="400px"
    :disabled="true"
  >
    <div>
      <Grid
        :datagrid.sync="datagrid"
        dbref="UJI_SelPermohonanHist"
        :dbparams="dbparams"
        :disabled="true"
        :columns="[
          {
            name: 'Tgl Perubahan',
            value: 'CreatedAt',
          },
          {
            name: 'Keterangan',
            value: 'Keterangan',
          },
          {
            name: '',
            value: 'Action',
          },
        ]"
      >
        <template v-slot:row-CreatedAt="{ row }">
          {{ row.CreatedAt | format('DD MMM YYYY HH:mm') }}
        </template>
        <template v-slot:row-Keterangan="{ row }">
          {{ row.Keterangan }}
        </template>
        <template v-slot:row-Action="{ row }">
          <v-btn rounded color="primary" small @click="applyData(row)"
            >APPLY</v-btn
          >
        </template>
      </Grid>
    </div>
  </Modal>
</template>
<script>
import moment from 'moment'
export default {
  data: () => ({
    xshow: false,
    datagrid: [],
  }),
  props: {
    show: <PERSON>olean,
    permohonanId: [Number, String],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  computed: {
    dbparams() {
      return { PermohonanID: this.permohonanId }
    },
  },
  methods: {
    async applyData(row) {
      let ret = await this.$api.call('UJI_SelPermohonanHist', {
        PermohonanID: this.permohonanId,
        CreatedAt: moment(row.CreatedAt).format('YYYY-MM-DD HH:mm:ss'),
      })
      if (ret.success) {
        this.$emit('apply', ret.data[0])
        this.$emit('update:show', false)
      }
    },
  },
}
</script>
