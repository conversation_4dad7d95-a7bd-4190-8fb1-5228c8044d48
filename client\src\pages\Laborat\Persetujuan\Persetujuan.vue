<template>
  <div style="display: flex">
    <SidePane @item-click="ShowSertifikat" :rebind="rebind" />
    <div v-if="rawUrl" class="right-pane">
      <iframe
        :src="rawUrl"
        style="width: 100%; height: 100%"
        frameborder="0"
      ></iframe>
      <div
        style="
          position: fixed;
          bottom: 0;
          right: 20px;
          left: calc(25% - 5px);
          display: flex;
          background: rgba(255, 255, 255, 0.7);
          padding: 20px;
        "
      >
        <v-btn color="error" @click="Reject" :disabled="loading"> TOLAK </v-btn>
        <v-spacer />
        <v-btn
          text
          @click="Refine"
          :disabled="loading"
          v-show="!rawUrl.match(/\?123/)"
        >
          REFINE
        </v-btn>
        <v-btn text color="success" @click="Download" :disabled="loading">
          EXCEL
        </v-btn>
        <v-btn @click="rawUrl = ''" style="margin-right: 8px" v-show="!loading">
          BATAL
        </v-btn>
        <v-btn color="primary" @click="Sign" :disabled="loading">
          SETUJUI
        </v-btn>
      </div>
    </div>
  </div>
</template>
<script>
import SidePane from './SidePane.vue'
export default {
  components: {
    SidePane,
  },
  data: () => ({
    rawUrlOri: '',
    rawUrl: '',
    forms: {},
    passphrase: '',
    showPassphrase: false,
    loading: false,
    rebind: 1,
  }),
  methods: {
    ShowSertifikat(val) {
      this.forms = val
      this.rawUrlOri = val.LkUrl
      this.rawUrl = this.$api.url + val.LkUrl.replace(/(xlsx|docx)$/, 'pdf')
    },
    async Reject() {
      let alasan = prompt('Alasan?')
      if (alasan) {
        let ret = await this.$api.call('UJI_SavLembarKerjaRejection', {
          LembarKerjaID: this.forms.LembarKerjaID,
          Alasan: alasan,
        })
        if (ret.success) {
          this.rawUrl = ''
          this.rebind++
        }
      }
    },
    async Refine() {
      this.loading = true
      await this.$api.get('/reports/uji/refine-lk/' + this.forms.LembarKerjaID)
      this.rawUrl = this.rawUrl + '?123'
      this.loading = false
    },
    Download() {
      window.open(this.$api.url + this.rawUrlOri, '_blank')
    },
    async Sign() {
      if (confirm('Setujui?')) {
        let ret = await this.$api.call('UJI_SavLembarKerjaApproval', {
          LembarKerjaID: this.forms.LembarKerjaID,
          FilePath: this.rawUrlOri,
        })
        if (ret.success) {
          this.rawUrl = ''
          this.rebind++
        }
      }
    },
  },
}
</script>
<style lang="scss"></style>
