const nodemailer = require("nodemailer")

const transporter = nodemailer.createTransport({
  service: "gmail",
  host: "smtp.gmail.com",
  port: 587,
  secure: false,
  auth: {
    user: "<EMAIL>",
    pass: "wvxwqvoztcymrarn",
  },
});

const sendMail = async (mailDetails) => {
  try {
    return await transporter.sendMail(mailDetails)
  } catch (error) {
    console.log(error);
  } 
  return null
};

module.exports = {
  sendMail
}