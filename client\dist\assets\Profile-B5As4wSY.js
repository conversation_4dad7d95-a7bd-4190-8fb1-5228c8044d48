import{n as o,p as n,a as t,_ as l,G as d}from"./index-DYIZrBBo.js";const i={data:()=>({forms:{}}),methods:{md5(e){return d.createHash("md5").update(e).digest("hex")},async Save(){await this.$api.call("Arch_SavPassword",this.forms)}}};var u=function(){var s=this,a=s._self._c;return a(n,{attrs:{title:"Ganti Password"}},[a("div",{staticClass:"form-inline padding",staticStyle:{background:"white"}},[a(t,{attrs:{label:"Password Lama",type:"password",value:s.forms.OldPassword},on:{"update:value":function(r){return s.$set(s.forms,"OldPassword",r)}}}),a("br"),a(t,{attrs:{label:"Password Baru",type:"password",value:s.forms.NewPassword},on:{"update:value":function(r){return s.$set(s.forms,"NewPassword",r)}}}),a(t,{attrs:{label:"Ulangi Password",type:"password",value:s.forms.RptPassword},on:{"update:value":function(r){return s.$set(s.forms,"RptPassword",r)}}}),a(l,{attrs:{color:"primary"},on:{click:s.Save}},[s._v("SIMPAN")])],1)])},_=[],p=o(i,u,_,!1,null,null);const m=p.exports;export{m as default};
