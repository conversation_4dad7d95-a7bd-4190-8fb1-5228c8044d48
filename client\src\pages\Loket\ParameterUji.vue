<template>
  <Modal
    title="Parameter Uji"
    :show.sync="forms.show"
    :submit-text="datagrid.length ? 'TAMBAH' : ''"
    @submit="Tambah"
  >
    <div v-if="!jenisId"><PERSON><PERSON><PERSON>/<PERSON><PERSON></div>
    <Grid
      v-else
      :datagrid.sync="datagrid"
      dbref="WEB.Parameter"
      :dbparams="dbparams"
      :disabled="true"
      :doRebind="rebind"
      height="calc(100vh - 300px)"
      :columns="[
        {
          name: '<PERSON><PERSON>',
          value: '<PERSON>a',
          filter: {
            type: 'search',
          },
        },
        {
          name: 'Metode',
          value: 'Metode',
        },
        {
          name: 'Wak<PERSON>',
          value: 'Waktu',
          class: 'align-right',
        },
        {
          name: 'Harga',
          value: 'Harga',
          class: 'align-right',
        },
      ]"
    >
      <template v-slot:row-Nama="{ row }">
        <div style="display: flex">
          <Checkbox
            :value.sync="row.Checked"
            style="position: relative; top: -2px"
            :text="row.Nama"
          />
          <v-chip
            small
            v-if="row.PaketID"
            color="success"
            style="margin-left: 10px; margin-top: 3px"
          >
            PAKET
          </v-chip>
        </div>
      </template>
      <template v-slot:row-Waktu="{ row }">
        <div v-if="row.Waktu">{{ row.Waktu }} Hari</div>
      </template>
      <template v-slot:row-Harga="{ row }"> {{ row.Harga | format }} </template>
    </Grid>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    rebind: 0,
    datagrid: [],
  }),
  props: {
    forms: Object,
    jenisId: String,
  },
  computed: {
    dbparams() {
      return { JenisID: this.jenisId, IncPaket: 1 }
    },
  },
  watch: {
    'forms.show'(val) {
      if (!val) {
        this.rebind++
      }
    },
  },
  methods: {
    Tambah() {
      this.$emit(
        'submit',
        this.datagrid
          .filter((d) => {
            return d.Checked
          })
          .map((d) => {
            return d.json_detail
              ? JSON.parse(d.json_detail).flat()
              : {
                  ParameterID: d.ParameterID,
                  NamaParameter: d.Nama,
                  JmlContoh: 1,
                  Metode: d.Metode,
                  Harga: d.Harga,
                  NamaContoh: '',
                  Keterangan: '',
                  IsVerified: 0,
                }
          })
          .sort((a, b) => a.NamaPaket < b.NamaPaket)
          .flat()
      )
      this.forms.show = false
    },
  },
}
</script>
<style lang="scss">
.align-right {
  text-align: right;
}
</style>
