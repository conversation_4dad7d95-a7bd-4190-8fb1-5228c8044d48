<template>
  <div style="height: 100vh">
    <div style="height: calc(100vh - 80px); overflow: auto">
      <div style="display: flex; width: 1150px">
        <Select width="100px" :items="yearOptions" :value.sync="tahun" />
        <v-spacer />
        <v-btn
          small
          outlined
          color="primary"
          style="margin-top: 3px"
          @click="PopulateRencana"
        >
          ISI RENCANA
        </v-btn>
        <v-icon @click="Print" style="margin-left: 8px">mdi-printer</v-icon>
      </div>
      <table id="tbl-jadwal" class="tbl-jadwal">
        <thead>
          <tr>
            <th style="width: 350px">Nama Alat</th>
            <th v-for="m in months" :key="m">{{ m }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, idx) in items" :key="idx">
            <td style="width: 350px">
              <div style="font-size: 14px; font-weight: bold">
                {{ item.NamaAlat }} {{ item.Merk ? `(${item.Merk})` : '' }}
              </div>
              <div style="font-size: small; color: gray">
                {{ item.NomorAlat || '-' }}
              </div>
            </td>
            <td
              v-for="(m, idx) in months"
              :key="m"
              @contextmenu="openMenu($event, item, idx + 1)"
            >
              <div>
                <v-icon v-show="item[m].includes('MP')" color="#ddd"
                  >mdi-circle</v-icon
                >
                <v-icon v-show="item[m].includes('MR')" color="primary">
                  mdi-circle
                </v-icon>
              </div>
              <div>
                <v-icon v-show="item[m].includes('KP')" color="#FFE0B2"
                  >mdi-square</v-icon
                >
                <v-icon v-show="item[m].includes('KR')" color="#EF6C00">
                  mdi-square
                </v-icon>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <v-menu
        v-model="popover.show"
        :position-x="popover.x"
        :position-y="popover.y"
        absolute
        offset-y
      >
        <v-list dense>
          <v-list-item
            v-for="(m, idx) in menus"
            :key="idx"
            @click="menuClick(m.title)"
          >
            <v-list-item-icon>
              <v-icon :color="m.iconColor">{{ m.icon }}</v-icon>
            </v-list-item-icon>
            <v-list-item-title>{{ m.title }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
      <alat-det
        :show.sync="showDet"
        :alatId="popover.AlatId"
        :aktifitas="popover.Aktifitas"
        :tanggal="popover.Tanggal"
      />
    </div>
  </div>
</template>
<script>
import AlatDet from '../../Alat/AlatDet.vue'
import moment from 'moment'
// import 'moment/locale/id'
// import Popover from '@/components/Forms/Popover'
export default {
  components: {
    // Popover,
    AlatDet,
  },
  data: () => ({
    tahun: new Date().getFullYear(),
    items: [],
    showDet: false,
    popover: {
      x: 0,
      y: 0,
      show: false,
      item: null,
      month: null,
    },
    menus: [
      {
        title: 'Rencana Pemeliharaan',
        cmd: 'MP',
        icon: 'mdi-circle',
        iconColor: '#ddd',
      },
      {
        title: 'Realisasi Pemeliharaan',
        cmd: 'MR',
        icon: 'mdi-circle',
        iconColor: 'primary',
      },
      {
        title: 'Rencana Kalibrasi',
        cmd: 'KP',
        icon: 'mdi-square',
        iconColor: '#FFE0B2',
      },
      {
        title: 'Realisasi Kalibrasi',
        cmd: 'KR',
        icon: 'mdi-square',
        iconColor: '#EF6C00',
      },
      {
        title: 'Clear',
        cmd: 'X',
        icon: 'mdi-close',
        iconColor: 'danger',
      },
    ],
    months: [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Agu',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ],
  }),
  computed: {
    yearOptions() {
      const years = []
      for (let i = -1; i < 3; i++) {
        years.push({
          val: new Date().getFullYear() - i,
          txt: new Date().getFullYear() - i,
        })
      }
      return years
    },
  },
  watch: {
    tahun() {
      this.populate()
    },
    showDet(val) {
      if (!val) this.populate()
    },
  },
  mounted() {
    this.populate()
  },
  methods: {
    async populate() {
      let td = document.querySelector('#tbl-jadwal td.active')
      if (td) td.classList.remove('active')

      let d = await this.$api.call('PML_SelJadwal', { Tahun: this.tahun })
      this.items = d.data
    },
    async PopulateRencana() {
      await this.$api.call('PML_SavJadwalPlan', { Tahun: this.tahun })
      this.populate()
    },
    openMenu(e, item, month) {
      this.popover.item = item
      this.popover.AlatId = item.AlatId
      this.popover.Tanggal = moment(month + ' 01 ' + this.tahun).format(
        'YYYY-MM-DD'
      )

      e.preventDefault()
      let td = document.querySelector('#tbl-jadwal td.active')
      if (td) td.classList.remove('active')
      // Add a class to the element
      e.target.classList.add('active')
      this.popover.show = false
      this.popover.x = e.clientX
      this.popover.y = e.clientY
      this.$nextTick(() => {
        this.popover.show = true
      })
    },
    async menuClick(t) {
      const m = this.menus.find((m) => m.title == t)
      if (t.match(/Realisasi/)) {
        this.popover.Aktifitas = t.match(/kalibrasi/i)
          ? 'kalibrasi'
          : 'pemeliharaan'
        this.showDet = true
      } else if (m) {
        let month = moment(this.popover.Tanggal).format('MMM')
        this.popover.item[month] += m.cmd
        await this.$api.call('PML_SavJadwal', {
          ...this.popover,
          Tipe: m.cmd,
        })
        this.populate()
      }
    },
    async Print() {
      let ret = await this.$api.post(
        '/reports/template/KalibrasiPemeliharaan.docx',
        {
          sp: 'PML_RptJadwal',
        }
      )
      if (ret.success) this.$api.download('/report/' + ret.data)
    },
  },
}
</script>
<style lang="scss">
.tbl-jadwal {
  border-collapse: collapse;
  td {
    padding: 5px 8px;
    border: 1px solid black;
    width: 67px;

    &.active {
      background: #e3f2fd;
    }
  }
  thead {
    display: block;
    background: #333;
    color: #ddd;
    font-weight: bold;
    th {
      padding: 5px 8px;
      text-align: center;
      position: sticky;
      width: 67px;
    }
  }
  tbody {
    display: block;
    height: calc(100vh - 150px);
    overflow: auto;
  }
}
</style>
