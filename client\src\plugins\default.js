import Vue from 'vue'
import {
  Input,
  Select,
  Search,
  TextArea,
  Checkbox,
  List,
  Label,
  Map,
  MenuButton,
  DatePicker,
  Radio,
} from '../components/Forms'
import Page from '../components/Page.vue'
import Grid from '../components/Grid/index.vue'
import Modal from '../components/Modal.vue'
import Uploader from '../components/Uploader/index.vue'
import Panel from '../components/Panel.vue'
import DropDown from '../components/DropDown.vue'

export default {
  register() {
    Vue.component('DropDown', DropDown)
    Vue.component('Input', Input)
    Vue.component('Select', Select)
    Vue.component('Search', Search)
    Vue.component('TextArea', TextArea)
    Vue.component('Checkbox', Checkbox)
    Vue.component('Page', Page)
    Vue.component('Grid', Grid)
    Vue.component('Modal', Modal)
    Vue.component('List', List)
    Vue.component('Label', Label)
    Vue.component('Uploader', Uploader)
    Vue.component('Map', Map)
    Vue.component('MenuButton', MenuButton)
    Vue.component('Panel', Panel)
    Vue.component('DatePicker', DatePicker)
    Vue.component('Radio', Radio)
  },
}
