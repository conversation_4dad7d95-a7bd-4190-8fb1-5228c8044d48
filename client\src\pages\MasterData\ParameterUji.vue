<template>
  <Page title="Data Parameter Pengujian">
    <Select
      dbref="UJI.SelJenisUji"
      width="250px"
      @change="JenisUjiChanged"
    ></Select>
    <Grid
      :datagrid.sync="datagrid"
      dbref="UJI.Parameter"
      :dbparams="params"
      style="height: calc(100vh - 170px)"
      class="dense"
      :doRebind="rebind"
      :columns="[
        {
          name: 'Parameter',
          value: 'Nama',
          width: '250px',
          editable: {
            com: 'Input',
          },
        },
        {
          name: 'Metode',
          value: 'Metode',
          width: '150px',
          editable: {
            com: 'Input',
          },
        },
        {
          name: '<PERSON>ak<PERSON>',
          value: 'Waktu',
          class: 'right',
          width: '80px',
          editable: {
            com: 'Input',
          },
        },
        {
          name: 'Harga',
          value: 'Harga',
          width: '120px',
          class: 'right',
          editable: {
            com: 'Input',
            type: 'number',
          },
        },
        {
          name: '<PERSON>. Sample',
          value: 'MinSample',
          class: 'right',
          width: '100px',
          editable: {
            com: 'Input',
          },
        },
        {
          name: 'Keterangan',
          value: 'Keterangan',
          width: '200px',
          editable: {
            com: 'Input',
          },
        },
      ]"
    >
      <template v-slot:row-Waktu="{ row }"> {{ row.Waktu }} hari </template>
      <template v-slot:row-Harga="{ row }">
        Rp. {{ row.Harga | format }}
      </template>
    </Grid>
  </Page>
</template>
<script>
export default {
  data: () => ({
    datagrid: [],
    rebind: 0,
    params: {
      JenisUji: '',
    },
  }),
  methods: {
    JenisUjiChanged(val) {
      this.params.JenisID = val
      this.rebind++
    },
  },
}
</script>
