<template>
  <div style="display: flex">
    <SidePane
      @item-click="ItemClick"
      statusId=",1,2,3,4,5,6,7,8,9,"
      :rebind="rebindSidebar"
      :addButton="false"
    />
    <div
      v-show="forms.PermohonanID || forms.PermohonanID === 0"
      class="right-pane"
      :class="$api.isMobile() ? '' : 'form-inline'"
      style="
        padding: 20px 20px;
        width: calc(100% - 300px);
        height: calc(100vh - 66px);
        overflow: auto;
        width: ;
      "
    >
      <div v-show="forms.BayarStatus == 2" class="dvWarn">
        {{ forms.CatatanKhusus }}
      </div>
      <div
        id="dvRightBox"
        style="
          float: right;
          font-family: raleway;
          background: white;
          margin-top: -20px;
        "
      >
        <div style="padding: 10px 20px; text-align: center">
          <div style="font-size: xx-large">{{ forms.<PERSON><PERSON>engujian }}</div>
          <div>{{ forms.ShareCode }}</div>
        </div>
        <!-- <v-btn
          text
          small
          color="primary"
          style="border-top:1px solid #e3e3e3; text-align: center;"
        >
          {{ forms.StatusName }}
        </v-btn> -->
        <v-btn
          text
          small
          color="primary"
          style="border-top: 1px solid #e3e3e3; text-align: center"
        >
          {{ forms.StatusName }}
        </v-btn>
      </div>
      <Input
        type="text"
        label="Nama Pelanggan"
        :value.sync="forms.Nama"
        :disabled="true"
        width="600px"
      />
      <TextArea
        label="Alamat"
        :value.sync="forms.Alamat"
        width="600px"
        :disabled="true"
      />
      <Input
        type="text"
        label="No. Ponsel"
        :value.sync="forms.Phone"
        :disabled="true"
        width="600px"
      />
      <Input
        type="text"
        label="Email"
        :value.sync="forms.Email"
        :disabled="true"
        width="600px"
      />
      <Select
        :items="jenis"
        label="Nama/Jenis Contoh"
        :value.sync="forms.JenisID"
        :disabled="true"
        width="600px"
      />
      <DatePicker
        label="Tanggal Masuk"
        :value.sync="forms.TglMasuk"
        :disabled="true"
        style="padding-top: 4px"
      />
      <TextArea
        label="Kegiatan/Paket Pekerjaan"
        :value.sync="forms.NamaKegiatan"
        :disabled="true"
        width="600px"
      />
      <Label label="Sumber/SatKer">
        <Select
          :items="satker"
          :value.sync="forms.SumberDana"
          :disabled="true"
          width="300px"
        />
        <Input
          type="text"
          placeholder="Satker / Kab / Kota"
          :value.sync="forms.SatKer"
          :disabled="true"
          style="margin-left: 5px"
          v-show="forms.SumberDana < '5'"
          width="300px"
        />
      </Label>
      <Label label="Surat Permohonan/Tgl.">
        <Input
          type="text"
          :value.sync="forms.SuratNo"
          placeholder="No. Surat"
          width="300px"
        />
        <DatePicker
          :value.sync="forms.SuratTgl"
          style="margin-left: 5px"
          width="300px"
        />
      </Label>
      <Label label="Surat Permohonan">
        <Uploader :value.sync="forms.SuratUrl" :key="rebindUpload">
          <template v-slot="{ opener, fileName }">
            <v-btn
              small
              text
              outlined
              v-show="fileName"
              style="margin-right: 8px"
              @click="Download(forms.SuratUrl)"
              >{{ fileName || forms.SuratUrl }}</v-btn
            >
            <v-btn small @click="opener">
              <v-icon>mdi-upload</v-icon>
            </v-btn>
          </template>
        </Uploader>
      </Label>
      <Grid
        :datagrid.sync="datagrid"
        dbref="UJI_SelPermohonanDet"
        :dbparams="dbparams"
        :disabled="true"
        :columns="[
          {
            name: 'Parameter Uji',
            value: 'NamaParameter',
          },
          {
            name: 'Nama Contoh',
            value: 'NamaContoh',
          },
          {
            name: 'Jml',
            value: 'JmlContoh',
          },
          {
            name: 'Metode',
            value: 'Metode',
          },
          {
            name: 'Harga',
            value: 'Harga',
            class: 'align-right',
          },
          {
            name: '',
            value: 'Delete',
          },
        ]"
      >
        <template v-slot:row-NamaContoh="{ row }">
          <Input
            :value.sync="row.NamaContoh"
            :disabled="true"
            placeholder="(asal/ukuran contoh)"
          />
        </template>
        <template v-slot:row-JmlContoh="{ row }">
          <Input
            type="number"
            :value.sync="row.JmlContoh"
            :disabled="true"
            placeholder="Jml"
            width="60px"
          />
        </template>
        <template v-slot:row-Harga="{ row }">
          {{ (row.Harga * row.JmlContoh) | format }}
        </template>
      </Grid>
    </div>
    <ReportPopup
      v-show="showReport"
      :reportUrl="reportUrl"
      v-click-outside="CloseReport"
    />
    <ParameterUji :forms="paramUji" :jenisId="forms.JenisID" />
    <Modal title="Pembatalan" :show.sync="deletion.show">
      <TextArea
        label="Alasan Pembatalan"
        :value.sync="forms.Keterangan"
        width="300px"
      />
    </Modal>
  </div>
</template>
<script>
import ReportPopup from '../ReportPopup.vue'
import SidePane from './SidePane.vue'
import ParameterUji from './ParameterUji.vue'
export default {
  components: {
    SidePane,
    ParameterUji,
    ReportPopup,
  },
  data: () => ({
    datagrid: [],
    rebindSidebar: 0,
    rebindUpload: 0,
    dbparams: { PermohonanID: 0 },
    forms: {},
    deletion: { show: false },
    paramUji: {
      show: false,
    },
    showReport: false,
    tahapan: [],
    jenis: [
      { val: 'A', txt: 'Mutu Air & Lingkungan' },
      { val: 'B', txt: 'Bahan Bangunan' },
      { val: 'Ba', txt: 'Aspal' },
      { val: 'T', txt: 'Tanah (Geoteknik)' },
    ],
    satker: [
      { val: '1', txt: 'APBN' },
      { val: '2', txt: 'APBD I - BMCK' },
      { val: '3', txt: 'APBD I - NON BMCK' },
      { val: '4', txt: 'APBD II' },
      { val: '5', txt: 'Swasta' },
      { val: '6', txt: 'Perorangan' },
      { val: '7', txt: 'Lainnya' },
    ],
  }),
  computed: {
    reportUrl() {
      if (this.showReport) {
        if (this.forms.StatusID >= 3) {
          return '/reports/uji/permohonan-paid/' + this.forms.PermohonanID
        }
        return '/reports/uji/permohonan/' + this.forms.PermohonanID
      } else {
        return '/tunggu-sebentar'
      }
    },
  },
  mounted() {
    this.PopulateTahapan()
  },
  methods: {
    async ItemClick(val) {
      this.Populate(val.PermohonanID)
    },
    async Download(val) {
      this.$api.download(this.$api.url + val, true)
    },
    async Populate(id) {
      this.rebindUpload++
      this.dbparams = { PermohonanID: id }
      var ret = await this.$api.call('UJI.SelPermohonan', {
        PermohonanID: id,
      })
      if (ret.data.length) {
        this.forms = ret.data[0]
      } else {
        this.forms = { PermohonanID: 0 }
      }
    },
    async PopulateTahapan() {
      let res = await this.$api.call('UJI.SelTahapan')
      this.tahapan = res.data
    },
    async Save() {
      var res = await this.$api.call('UJI.SavPermohonan', {
        ...this.forms,
        XmlPermohonanDet: this.datagrid,
      })
      if (res.success) this.rebindSidebar++
    },
    async CatatanKhusus() {
      await this.$api.call('UJI_UpdCatatanKhusus', {
        PermohonanID: this.forms.PermohonanID,
        CatatanKhsusus: 'Belum Dibayarkan',
      })
      this.Populate(this.forms.PermohonanID)
    },
    async UpdBayar() {
      let ret = await this.$api.call('UJI_UpdBayar', {
        PermohonanID: this.forms.PermohonanID,
        BayarStatus: 1,
      })
      if (ret.success) this.Populate(this.forms.PermohonanID)
    },
    async UpdPengambilan() {
      // $A.Page.Redirect("#/Main/Loket/Feedback");
    },
    async Delete() {
      var res = await this.$api.call('UJI_DelPermohonan', this.forms)
      if (res.success) {
        this.deletion.show = false
        this.forms.PermohonanID = 0
        this.Populate(this.forms.PermohonanID)
        this.rebindSidebar++
      }
    },
    async ResendNotif() {
      let res = await this.$api.post('/reports/uji/resend', {
        PermohonanID: this.forms.PermohonanID,
      })
      if (res.success) {
        this.$api.notify(res.message, 'success')
      } else {
        this.$api.notify(res.message, 'error')
      }
    },
    async DownloadKwitansi() {
      // this.$api.download(this.$api.url + '/report/uji/kwitansi/' + this.forms.PermohonanID)
      window.open(
        this.$api.url + '/reports/uji/kwitansi/' + this.forms.PermohonanID,
        '_blank'
      )
    },
    AddParameter(params) {
      this.datagrid.push(...params)
      this.paramUji.show = false
    },
    DelParameter(idx) {
      this.datagrid.splice(idx, 1)
    },
    OpenReport() {
      setTimeout(() => {
        this.showReport = true
      }, 100)
    },
    CloseReport() {
      this.showReport = false
    },
  },
}
</script>
<style lang="scss" scoped>
.dvWarn {
  position: absolute;
  top: -48px;
  color: white;
  background: red;
  padding: 5px 10px;
  border-radius: 5px;
}
</style>
