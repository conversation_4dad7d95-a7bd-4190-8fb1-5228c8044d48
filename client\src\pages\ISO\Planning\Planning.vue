<template>
  <div style="height: calc(100vh - 68px); overflow: hidden; position: relative">
    <iframe
      :src="currentUrl"
      width="100%"
      style="margin-top: -55px; height: calc(100% + 55px)"
    >
    </iframe>
    <div class="sidebar-container">
      <div class="sidebar-opener">
        <v-icon large>mdi-chevron-left</v-icon>
      </div>
      <div class="sidebar-content">
        <div style="padding: 10px; display: flex">
          <Input
            type="text"
            :value.sync="keyword"
            placeholder="Cari .."
            width="266px"
            rightIcon="mdi-magnify"
            class="searchbar"
            style="margin-left: -8px"
          />
        </div>
        <List
          dbref="ISO_SelPenjaminMutu"
          :dbparams="{ Keyword: keyword }"
          @itemClick="ItemClick"
          height="calc(100% - 85px)"
          :rebind="rebind"
          :selectOnLoad="true"
          @change="ItemsChange"
        >
          <template v-slot="{ row }">
            <div style="padding: 10px">
              <div
                style="
                  font-weight: bold;
                  font-family: Raleway;
                  text-transform: uppercase;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  width: 180px;
                  height: 20px;
                  white-space: nowrap;
                "
              >
                {{ row.DocName }}
              </div>
              <div style="color: gray; display: flex; font-size: 13px">
                {{ row.CreatedAt | format }}
              </div>
            </div>
          </template>
        </List>
        <v-btn
          outlined
          color="primary"
          style="width: 100%"
          @click="showAddDoc = true"
        >
          <v-icon left>mdi-plus</v-icon>
          TAMBAH BARU
        </v-btn>
      </div>
    </div>
    <AddDoc :show.sync="showAddDoc" @save="rebind++"></AddDoc>
  </div>
</template>
<script>
import AddDoc from './AddDoc.vue'
export default {
  components: {
    AddDoc,
  },
  data: () => ({
    keyword: '',
    rebind: 1,
    showAddDoc: false,
    currentUrl: '',
  }),
  methods: {
    ItemClick(val) {
      this.currentUrl = val.DocUrl
    },
    ItemsChange(items) {
      if (!this.currentUrl && items.length) this.currentUrl = items[0].DocUrl
    },
  },
}
</script>
<style lang="scss">
.sidebar-container {
  position: absolute;
  top: 0;
  right: -300px;
  height: 100%;
  display: flex;
  transition: right 0.3s ease-in-out;

  &:hover {
    right: 0;
  }
}

.sidebar-opener {
  position: absolute;
  top: 12px;
  left: -50px;
  width: 50px;
  height: 40px;
  padding: 5px;
  background: white;
  border-radius: 20px 0 0 20px;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-content {
  width: 300px;
  background: white;
  height: 100%;
  padding: 10px;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  z-index: 1;

  .searchbar {
    margin-bottom: 0;

    input {
      background: transparent !important;
      border-bottom: 0 !important;
    }
  }

  .--item {
    border-radius: 10px;
    margin-right: 10px;

    .no-uji {
      font-size: 10px;
      padding: 3px 5px 0 5px;
      background: #ddd;
      border-radius: 5px;
      color: #333;
      margin-right: 5px;
    }
  }

  .--item.selected {
    .no-uji {
      background: white;
    }
  }
}
</style>
