<template>
  <div style="display: flex">
    <div class="sidepane">
      <div style="padding: 10px">APPLIKASI</div>
      <div class="rowlnk">
        <v-btn text small color="primary" @click="tab = 'data-dinas'">
          Data Balai
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn text small color="primary" @click="tab = 'data-pengguna'">
          Data Pengguna
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn text small color="primary" @click="tab = 'data-penguji'">
          Data Penguji
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn text small color="primary" @click="tab = 'data-parameter-uji'">
          Data parameter Pengujian
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn text small color="primary" @click="tab = 'data-paket-parameter'">
          Data Paket Parameter
        </v-btn>
      </div>
      <!-- <div class="rowlnk">
        <v-btn text small color="primary">
          Data Syarat Pengujian
        </v-btn>
      </div> -->
      <div style="padding: 10px">WEB</div>
      <div class="rowlnk">
        <v-btn text small color="primary" @click="tab = 'banner'">
          Banner
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn text small color="primary" @click="tab = 'media-partner'">
          Media Partner
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn text small color="primary" @click="tab = 'whatsapp'">
          Whatsapp Connect
        </v-btn>
      </div>
    </div>
    <div>
      <DataDinas v-if="tab === 'data-dinas'"></DataDinas>
      <DataPengguna v-else-if="tab === 'data-pengguna'"></DataPengguna>
      <DataPenguji v-else-if="tab === 'data-penguji'"></DataPenguji>
      <ParameterUji v-else-if="tab === 'data-parameter-uji'"></ParameterUji>
      <PaketParameter
        v-else-if="tab === 'data-paket-parameter'"
      ></PaketParameter>
      <DataBanner v-else-if="tab === 'banner'"></DataBanner>
      <Whatsapp v-else-if="tab === 'whatsapp'"></Whatsapp>
    </div>
  </div>
</template>
<script>
import DataDinas from './Company.vue'
import DataPengguna from './User/User.vue'
import DataPenguji from './Penguji.vue'
import ParameterUji from './ParameterUji.vue'
import PaketParameter from './Paket/PaketParameter.vue'
import DataBanner from './Banner.vue'
import Whatsapp from './Whatsapp.vue'

export default {
  components: {
    DataDinas,
    DataPengguna,
    DataPenguji,
    ParameterUji,
    PaketParameter,
    DataBanner,
    Whatsapp,
  },
  data: () => ({
    tab: '',
    reportOptions: null,
    reportUrl: 'about:blank',
    showReport: false,
    showParams: false,
    reportParams: {},

    //dummmy
    qrcode: '',
  }),
  watch: {
    // reportUrl(val) {
    //   if (val) this.showReport = true
    // },
    reportParams(val) {
      if (val) this.showReport = true
    },
  },
  created() {
    let coms = sessionStorage.getItem('coms-access')
    if (coms) this.coms = JSON.parse(coms)
  },
  methods: {
    Generate(evt, opts) {
      this.showReport = false
      this.showParams = true
      opts.rptname = evt.target.innerText
      this.reportOptions = opts
    },
  },
}
</script>
<style lang="scss">
.rowlnk {
  border-bottom: 1px solid #ddd;
}
//.page-content {
// height: calc(100vh - 115px) !important;
// height: calc(100vh - 56px) !important;
//}
</style>
