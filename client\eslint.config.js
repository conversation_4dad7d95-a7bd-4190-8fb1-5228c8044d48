import js from '@eslint/js'
import vue from 'eslint-plugin-vue'
import prettier from 'eslint-plugin-prettier'

export default [
  { files: ['**/*.{js,mjs,cjs,ts,mts,cts,vue}'] },
  js.configs.recommended,
  ...vue.configs['flat/essential'],
  {
    plugins: {
      prettier,
    },
    rules: {
      'prettier/prettier': [
        'error',
        {
          singleQuote: true,
          semi: false,
        },
      ],
      'vue/multi-word-component-names': 'off',
      'vue/no-deprecated-filter': 'off',
    },
  },
]
