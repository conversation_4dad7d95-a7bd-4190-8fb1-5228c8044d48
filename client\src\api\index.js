/* eslint-disable no-undef */
import axios from 'axios'
import CryptoJS from 'crypto-js'
import Vue from 'vue'
import { mapActions } from 'vuex'

const Vuex = {
  actions: { ...mapActions(['createPrompt']) },
}

const API_URL = window.location.origin.replace(':8000', ':8001')
const api = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  timeout: 9000000,
})
api.interceptors.request.use(
  (config) => {
    const twj = localStorage.getItem('twj')
    if (twj) {
      config.headers.authorization = `Bearer ${twj}`
    }
    return config
  },
  (err) => Promise.reject(err)
)
const notify = function (message, type) {
  if (!type) type = 'success'
  Vue.$toast.open({ message: message, type: type, position: 'top' })
}
const toDropdown = function () {
  if (!this.length) return []
  var obj = this[0]
  var text, value
  for (var x in obj) {
    if (!value) value = x
    else if (!text) text = x
    else break
  }
  return this.map((d) => {
    return {
      ...d,
      text: d[text],
      value: d[value],
    }
  })
}
const toXML = function (array, spacey) {
  if (!array) return ''

  var sxml = ''
  for (var i = 0; i < array.length; i++) {
    sxml += '{row '
    if (typeof array[i] == 'string') {
      sxml += 'col="' + array[i] + '" '
    } else {
      for (let x in array[i]) {
        if (array[i][x] !== null) {
          if (spacey && array[i][x] && array[i][x].replace)
            sxml +=
              x +
              '="' +
              (array[i][x].replace
                ? array[i][x].replace(/\s/g, spacey)
                : array[i][x]) +
              '" '
          else
            sxml +=
              x +
              '="' +
              (typeof array[i][x] == 'boolean'
                ? array[i][x]
                  ? 1
                  : 0
                : array[i][x]) +
              '" '
        }
      }
    }
    sxml += '/}'
  }
  return sxml
}
export default {
  url: API_URL,
  setToken: (token) => {
    // let clientId = token.substr(0, 8)
    localStorage.setItem('is-auth', token.substr(-8))
    if (window.location.hostname == 'localhost')
      localStorage.setItem('twj', token)
    // startListener(clientId)
  },
  isMobile() {
    return window.innerWidth < window.innerHeight
  },
  prompt(self, opt) {
    Vuex.actions.createPrompt.apply(self, [opt])
  },
  toUUID(val) {
    if (typeof val == 'string') {
      if (val.match(/-/)) return val
      else {
        return (
          val.substr(0, 8) +
          '-' +
          val.substr(8, 4) +
          '-' +
          val.substr(12, 4) +
          '-' +
          val.substr(16, 4) +
          '-' +
          val.substr(20, 12)
        )
      }
    } else {
      val = Buffer.from(val).toString('hex')
      return (
        val.substr(0, 8) +
        '-' +
        val.substr(8, 4) +
        '-' +
        val.substr(12, 4) +
        '-' +
        val.substr(16, 4) +
        '-' +
        val.substr(20, 12)
      )
    }
  },
  async login(params) {
    let { username, password } = params
    if (password) password = CryptoJS.MD5(params.password).toString()
    let { data } = await api.post('/api/login', {
      _Username: username,
      _Password: password,
    })
    if (data.message) notify(data.message, data.success ? 'success' : 'error')
    if (data.token) {
      localStorage.setItem('is-auth', data.token.substr(0, 8))
    }
    return data.data
  },
  logout() {
    localStorage.removeItem('is-auth')
  },
  async getOne(sp, params) {
    let d = await this.call(sp, params)
    if (d.data.length) return d.data[0]
    else return {}
  },
  async call(sp, params) {
    sp = sp.replace('.', '_')
    for (var x in params) {
      if (typeof params[x] == 'object') {
        if (params[x] && params[x].type == 'Buffer')
          params[x] = params[x][0] === 1
        else if (params[x] && params[x].length !== undefined) {
          params[x] = toXML(params[x], '~')
        }
      } else if (x.match(/Password/) && params[x].length < 32) {
        params[x] = CryptoJS.MD5(params[x]).toString()
      }
    }

    if (!params || JSON.stringify(params) == '{}') {
      let data = sessionStorage.getItem(sp)
      if (data) {
        data = JSON.parse(data)
        if (data.message) {
          notify(data.message, data.success ? 'success' : 'error')
          if (data.message.match(/Not authorized/))
            window.location = '/Main/App/Logout'
        }
        return data
      }
    }

    let { data } = await api.post(`/api/call/${sp}`, params)
    if ((!params || JSON.stringify(params) == '{}') && data.success) {
      sessionStorage.setItem(sp, JSON.stringify(data))
    }
    if (data.message) {
      notify(data.message, data.success ? 'success' : 'error')
      if (data.message.match(/Not authorized/))
        window.location = '/Main/App/Logout'
    }

    return data
  },
  async notify(message, mode) {
    notify(message, mode)
  },
  async select(obj, params) {
    let { data } = await api.post(`/api/select/${obj}`, params)
    if (data.message) notify(data.message, data.success ? 'success' : 'error')
    data.toDropdown = toDropdown.bind(data.data)
    return data
  },
  async upload(params, opt) {
    let url = ''
    if (opt) {
      if (opt.multiple) {
        url += '-multi'
      }
      if (opt.convert) {
        url += '/' + opt.convert
      }
    }
    let res = await api
      .post(`/api/upload` + url, params, {
        headers: {
          // ...params.getHeaders(),
          'Content-Type': 'multipart/form-data',
        },
      })
      .catch((err) => {
        console.error(err)
        notify('Upload Error', 'error')
      })
    if (res && res.data) return res.data
    else return {}
  },
  async download(url, newTab) {
    if (newTab) {
      window.open(url, '_blank')
    } else {
      var iframe = document.createElement('IFRAME')
      iframe.className = 'hide'
      iframe.src = url.match(/^\//)
        ? API_URL.replace(':8000', ':8001') + url
        : url
      document.body.appendChild(iframe)
      setTimeout(function () {
        iframe.remove()
      }, 10000)
    }
  },
  async post(url, params, opts) {
    let { data } = await api.post(url, params, opts)
    if (opts) {
      if (opts.notification || opts.notify)
        notify(data.message, data.success ? 'success' : 'error')
    }
    return data
  },
  async get(url, opts) {
    let { data } = await api.get(url, opts)
    return data
  },
  async fromOutside(opts) {
    return axios(opts)
  },
}
