<template>
  <div class="main-panel">
    <div style="display: flex; background: white">
      <v-btn
        :text="statusId != '1'"
        :outlined="statusId != '1'"
        color="primary"
        elevation="0"
        style="width: 20%"
        tile
        @click="ChangeStatus('1')"
      >
        <v-badge color="error" :content="counter.JmlPermohonan" :value="0">
          <v-icon v-if="$api.isMobile()">mdi-file-outline</v-icon>
          <span v-else>Permohonan Baru</span>
        </v-badge>
      </v-btn>
      <v-btn
        :text="statusId != '2'"
        :outlined="statusId != '2'"
        color="primary"
        elevation="0"
        tile
        style="width: 20%"
        @click="ChangeStatus('2')"
      >
        <v-badge
          color="error"
          :content="counter.JmlMenungguSample"
          :value="counter.JmlMenungguSample > 0"
        >
          <v-icon v-if="$api.isMobile()">mdi-paperclip</v-icon>
          <span v-else>Penyerahan Sample</span>
        </v-badge>
      </v-btn>
      <v-btn
        :text="statusId != '3'"
        :outlined="statusId != '3'"
        color="primary"
        elevation="0"
        tile
        style="width: 20%"
        @click="ChangeStatus('3')"
      >
        <v-badge
          color="error"
          :content="counter.JmlMenungguBayar"
          :value="counter.JmlMenungguBayar > 0"
        >
          <v-icon v-if="$api.isMobile()">mdi-hand-coin-outline</v-icon>
          <span v-else>Menunggu Pembayaran</span>
        </v-badge>
      </v-btn>
      <v-btn
        :text="statusId != '4,5,6,7,8'"
        :outlined="statusId != '4,5,6,7,8'"
        color="primary"
        elevation="0"
        tile
        style="width: 20%"
        @click="ChangeStatus('4,5,6,7,8')"
      >
        <v-badge
          color="error"
          :content="counter.JmlDalamProses"
          :value="counter.JmlDalamProses > 0"
        >
          <v-icon v-if="$api.isMobile()">mdi-flask-outline</v-icon>
          <span v-else>Dalam Proses</span>
        </v-badge>
      </v-btn>
      <v-btn
        :text="statusId != '9'"
        :outlined="statusId != '9'"
        color="primary"
        elevation="0"
        tile
        style="width: 20%"
        @click="ChangeStatus('9')"
      >
        <v-badge
          color="error"
          :content="counter.JmlSelesai"
          :value="counter.JmlSelesai > 0"
        >
          <v-icon v-if="$api.isMobile()">mdi-check-decagram</v-icon>
          <span v-else>Sudah Selesai</span>
        </v-badge>
      </v-btn>
    </div>
    <PermohonanBaru v-if="statusId == '1'" @refresh="GetCounter" />
    <PenyerahanSample v-if="statusId == '2'" />
    <MenungguPembayaran v-if="statusId == '3'" />
    <DalamProses v-if="statusId == '4,5,6,7,8'" />
    <Selesai v-if="statusId == '9'" />
  </div>
</template>
<script>
import PermohonanBaru from './PermohonanBaru.vue'
import PenyerahanSample from './PenyerahanSample.vue'
import MenungguPembayaran from './MenungguPembayaran.vue'
import DalamProses from './DalamProses.vue'
import Selesai from './Selesai.vue'
export default {
  components: {
    PermohonanBaru,
    PenyerahanSample,
    MenungguPembayaran,
    DalamProses,
    Selesai,
  },
  data: () => ({
    billing: {
      show: false,
      PaymentType: 'cash',
      PermohonanID: '',
      TotalBayar: 0,
    },
    datalist: [],
    statusId: '1',
    rebind: 1,
    counter: {},
  }),
  computed: {
    dbparams() {
      return { StatusID: this.statusId }
    },
  },
  watch: {
    'billing.show'(val) {
      if (!val) {
        this.rebind++
        this.billing.TotalBayar = 0
      }
    },
  },
  mounted() {
    this.statusId = '1'
    this.GetCounter()
  },
  methods: {
    ItemClick() {
      this.billing.TotalBayar = 0
      let permohonanId = []
      for (let item of this.datalist) {
        if (item.checked) {
          this.billing.TotalBayar += item.TotalBayar
          permohonanId.push(item.PermohonanID)
        }
      }
      this.billing.PermohonanID = permohonanId.join(',')
    },
    ChangeStatus(status) {
      this.statusId = status
    },
    async GetCounter() {
      let ret = await this.$api.call('UJI_SelPermohonanCount', {
        NoCache: true,
      })
      this.counter = ret.data[0]
    },
    async ShowBilling(permohonanId) {
      let ret = await this.$api.call('UJI_SelBilling', {
        PermohonanID: permohonanId,
      })
      if (ret.success) {
        this.billing = {
          show: true,
          step: 1,
          total: ret.data[0].TotalBayar,
          billingId: ret.data[0].BillingID,
          PaymentType: 'transfer',
        }
      }
    },
  },
}
</script>
<style lang="scss">
.main-panel {
  max-width: 1170px;
  margin: auto;
  padding: 12px 0;
}
.bottom-panel {
  position: fixed;
  bottom: 0px;
  left: 0;
  padding: 20px;
  width: 100%;
  text-align: center;
}
@media screen and (min-width: 320px) {
  .main-panel {
    width: calc(100vw - 40px);
  }
  // .bottom-panel {
  //   width: calc(100vw - 45px);
  // }
}
@media screen and (min-width: 768px) {
  .main-panel {
    width: calc(100vw - 100px);
  }
  // .bottom-panel {
  //   width: calc(100vw - 100px);
  // }
}
</style>
