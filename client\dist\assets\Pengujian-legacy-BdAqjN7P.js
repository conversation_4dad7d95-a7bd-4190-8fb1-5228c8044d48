!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,a)}return r}function r(t){for(var r=1;r<arguments.length;r++){var n=null!=arguments[r]?arguments[r]:{};r%2?e(Object(n),!0).forEach(function(e){a(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):e(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function a(e,r,a){return(r=function(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var n=a.call(e,r||"default");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==t(r)?r:r+""}(r))in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",i=r.toStringTag||"@@toStringTag";function s(r,a,n,i){var s=a&&a.prototype instanceof l?a:l,c=Object.create(s.prototype);return o(c,"_invoke",function(r,a,n){var o,i,s,l=0,c=n||[],f=!1,m={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,r){return o=e,i=0,s=t,m.n=r,u}};function p(r,a){for(i=r,s=a,e=0;!f&&l&&!n&&e<c.length;e++){var n,o=c[e],p=m.p,d=o[2];r>3?(n=d===a)&&(s=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=t):o[0]<=p&&((n=r<2&&p<o[1])?(i=0,m.v=a,m.n=o[1]):p<d&&(n=r<3||o[0]>a||a>d)&&(o[4]=r,o[5]=a,m.n=d,i=0))}if(n||r>1)return u;throw f=!0,a}return function(n,c,d){if(l>1)throw TypeError("Generator is already running");for(f&&1===c&&p(c,d),i=c,s=d;(e=i<2?t:s)||!f;){o||(i?i<3?(i>1&&(m.n=-1),p(i,s)):m.n=s:m.v=s);try{if(l=2,o){if(i||(n="next"),e=o[n]){if(!(e=e.call(o,s)))throw TypeError("iterator result is not an object");if(!e.done)return e;s=e.value,i<2&&(i=0)}else 1===i&&(e=o.return)&&e.call(o),i<2&&(s=TypeError("The iterator does not provide a '"+n+"' method"),i=1);o=t}else if((e=(f=m.n<0)?s:r.call(a,m))!==u)break}catch(e){o=t,i=1,s=e}finally{l=1}}return{value:e,done:f}}}(r,n,i),!0),c}var u={};function l(){}function c(){}function f(){}e=Object.getPrototypeOf;var m=[][a]?e(e([][a]())):(o(e={},a,function(){return this}),e),p=f.prototype=l.prototype=Object.create(m);function d(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,o(t,i,"GeneratorFunction")),t.prototype=Object.create(p),t}return c.prototype=f,o(p,"constructor",f),o(f,"constructor",c),c.displayName="GeneratorFunction",o(f,i,"GeneratorFunction"),o(p),o(p,i,"Generator"),o(p,a,function(){return this}),o(p,"toString",function(){return"[object Generator]"}),(n=function(){return{w:s,m:d}})()}function o(t,e,r,a){var n=Object.defineProperty;try{n({},"",{})}catch(t){n=0}o=function(t,e,r,a){function i(e,r){o(t,e,function(t){return this._invoke(e,r,t)})}e?n?n(t,e,{value:r,enumerable:!a,configurable:!a,writable:!a}):t[e]=r:(i("next",0),i("throw",1),i("return",2))},o(t,e,r,a)}function i(t,e,r,a,n,o,i){try{var s=t[o](i),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(a,n)}function s(t){return function(){var e=this,r=arguments;return new Promise(function(a,n){var o=t.apply(e,r);function s(t){i(o,a,n,s,u,"next",t)}function u(t){i(o,a,n,s,u,"throw",t)}s(void 0)})}}System.register(["./index-legacy-BUdDePUl.js"],function(t,e){"use strict";var a,o,i,u,l,c,f,m,p,d,v,h;return{setters:[function(t){a=t.n,o=t.S,i=t.a,u=t.b,l=t.c,c=t.d,f=t.e,m=t.f,p=t._,d=t.g,v=t.h,h=t.k}],execute:function(){t("default",a({components:{SidePane:o},data:function(){return{datagrid:[],rebindSidebar:0,rebindUpload:0,dbparams:{PermohonanID:0},forms:{},pay:{show:!1},paramUji:{show:!1},showReport:!1,jenis:[{val:"A",txt:"Mutu Air & Lingkungan"},{val:"B",txt:"Bahan Bangunan"},{val:"Ba",txt:"Aspal"},{val:"T",txt:"Tanah (Geoteknik)"}],satker:[{val:"1",txt:"APBN"},{val:"2",txt:"APBD I - BMCK"},{val:"3",txt:"APBD I - NON BMCK"},{val:"4",txt:"APBD II"},{val:"5",txt:"Swasta"},{val:"6",txt:"Perorangan"},{val:"7",txt:"Lainnya"}]}},methods:{ItemClick:function(t){var e=this;return s(n().m(function r(){return n().w(function(r){for(;;)switch(r.n){case 0:e.Populate(t.PermohonanID);case 1:return r.a(2)}},r)}))()},Download:function(t){var e=this;return s(n().m(function r(){return n().w(function(r){for(;;)switch(r.n){case 0:e.$api.download(e.$api.url+t,!0);case 1:return r.a(2)}},r)}))()},Populate:function(t){var e=this;return s(n().m(function r(){var a;return n().w(function(r){for(;;)switch(r.n){case 0:return e.rebindUpload++,e.dbparams={PermohonanID:t},r.n=1,e.$api.call("UJI.SelPermohonan",{PermohonanID:t});case 1:(a=r.v).data.length?e.forms=a.data[0]:e.forms={};case 2:return r.a(2)}},r)}))()},Save:function(){var t=this;return s(n().m(function e(){return n().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("UJI.SavPermohonan",r(r({},t.forms),{},{XmlPermohonanDet:t.datagrid}));case 1:e.v.success&&t.rebindSidebar++;case 2:return e.a(2)}},e)}))()},CloseReport:function(){},AddParameter:function(){}}},function(){var t=this,e=t._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{attrs:{statusId:",2,3,4,",rebind:t.rebindSidebar,addButton:!0},on:{"item-click":t.ItemClick}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.PermohonanID,expression:"forms.PermohonanID"}],staticClass:"form-inline",staticStyle:{padding:"20px 20px",width:"calc(100vw - 500px)",overflow:"auto",height:"calc(100vh - 66px)"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:2==t.forms.BayarStatus,expression:"forms.BayarStatus == 2"}],staticClass:"dvWarn"},[t._v(" "+t._s(t.forms.CatatanKhusus)+" ")]),e("div",{staticStyle:{float:"right","font-family":"raleway",background:"white","margin-top":"-20px"},attrs:{id:"dvRightBox"}},[e("div",{staticStyle:{padding:"10px 20px","text-align":"center"}},[e("div",{staticStyle:{"font-size":"xx-large"}},[t._v(t._s(t.forms.NoPengujian))]),e("div",[t._v(t._s(t.forms.ShareCode))])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.StatusID>=4&&1==t.forms.BayarStatus,expression:"forms.StatusID >= 4 && forms.BayarStatus == 1"}],staticStyle:{padding:"10px 20px","border-top":"1px solid #e3e3e3",background:"#4caf50",color:"white","text-align":"center"}},[e("span",[t._v("Sudah Dibayarkan")])]),e("div",{directives:[{name:"show",rawName:"v-show",value:9==t.forms.StatusID,expression:"forms.StatusID == 9"}],staticStyle:{padding:"10px 20px","border-top":"1px solid #e3e3e3",background:"#8bc34a",color:"white","text-align":"center"}},[e("span",[t._v("Sudah Diserahkan")])])]),e(i,{attrs:{type:"text",label:"Nama Pelanggan",value:t.forms.Nama,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"Nama",e)}}}),e(u,{attrs:{label:"Alamat",value:t.forms.Alamat,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"Alamat",e)}}}),t._v(" "),e(i,{attrs:{type:"text",label:"No. Ponsel",value:t.forms.Phone,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"Phone",e)}}}),e(l,{attrs:{items:t.jenis,label:"Nama/Jenis Contoh",value:t.forms.JenisID,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"JenisID",e)}}}),e(c,{staticStyle:{"padding-top":"4px"},attrs:{label:"Tanggal Masuk",value:t.forms.TglMasuk},on:{"update:value":function(e){return t.$set(t.forms,"TglMasuk",e)}}}),e(u,{attrs:{label:"Kegiatan/Paket Pekerjaan",value:t.forms.NamaKegiatan,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"NamaKegiatan",e)}}}),t._v(" "),e(f,{attrs:{label:"Sumber/SatKer"}},[e(l,{attrs:{items:t.satker,value:t.forms.SumberDana},on:{"update:value":function(e){return t.$set(t.forms,"SumberDana",e)}}}),e(i,{directives:[{name:"show",rawName:"v-show",value:t.forms.SumberDana<"5",expression:"forms.SumberDana < '5'"}],staticStyle:{"margin-left":"5px"},attrs:{type:"text",placeholder:"Satker / Kab / Kota",value:t.forms.SatKer},on:{"update:value":function(e){return t.$set(t.forms,"SatKer",e)}}})],1),e(f,{attrs:{label:"Surat Permohonan/Tgl."}},[e(i,{attrs:{type:"text",value:t.forms.SuratNo,placeholder:"No. Surat"},on:{"update:value":function(e){return t.$set(t.forms,"SuratNo",e)}}}),e(c,{staticStyle:{"margin-left":"5px"},attrs:{value:t.forms.SuratTgl},on:{"update:value":function(e){return t.$set(t.forms,"SuratTgl",e)}}})],1),e(f,{attrs:{label:"Surat Permohonan"}},[e(m,{key:t.rebindUpload,attrs:{value:t.forms.SuratUrl},on:{"update:value":function(e){return t.$set(t.forms,"SuratUrl",e)}},scopedSlots:t._u([{key:"default",fn:function(r){var a=r.opener,n=r.fileName;return[e(p,{directives:[{name:"show",rawName:"v-show",value:n,expression:"fileName"}],staticStyle:{"margin-right":"8px"},attrs:{small:"",text:"",outlined:""},on:{click:function(e){return t.Download(t.forms.SuratUrl)}}},[t._v(t._s(n||t.forms.SuratUrl))]),e(p,{attrs:{small:""},on:{click:a}},[e(d,[t._v("mdi-upload")])],1)]}}])})],1),e(v,{attrs:{datagrid:t.datagrid,dbref:"UJI_SelPermohonanDet",dbparams:t.dbparams,disabled:!0,columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Nama Contoh",value:"NamaContoh"},{name:"Jml",value:"JmlContoh"},{name:"Metode",value:"Metode"},{name:"Harga",value:"Harga",class:"align-right"},{name:"",value:"Delete"}]},on:{"update:datagrid":function(e){t.datagrid=e}},scopedSlots:t._u([{key:"row-NamaContoh",fn:function(r){var a=r.row;return[e(i,{attrs:{value:a.NamaContoh,placeholder:"(asal/ukuran contoh)"},on:{"update:value":function(e){return t.$set(a,"NamaContoh",e)}}})]}},{key:"row-JmlContoh",fn:function(r){var a=r.row;return[e(i,{attrs:{type:"number",value:a.JmlContoh,placeholder:"Jml",width:"60px"},on:{"update:value":function(e){return t.$set(a,"JmlContoh",e)}}})]}},{key:"row-Harga",fn:function(e){var r=e.row;return[t._v(" "+t._s(t._f("format")(r.Harga*r.JmlContoh))+" ")]}},{key:"row-Delete",fn:function(r){var a=r.idx;return[e(d,{attrs:{color:"error"},on:{click:function(e){return t.DelParameter(a)}}},[t._v("mdi-trash-can")])]}},{key:"footer",fn:function(){return[e("tr",[e("td",{attrs:{colspan:"4"}},[e(p,{attrs:{text:"",small:""},on:{click:function(e){t.paramUji.show=!0}}},[e(d,{attrs:{left:""}},[t._v("mdi-plus-circle")]),t._v(" Tambah Parameter Uji ")],1)],1),e("td",{staticClass:"align-right",staticStyle:{"font-weight":"bold",padding:"8px 12px"}},[t._v(" "+t._s(t._f("format")(t.datagrid.reduce(function(t,e){return t+e.Harga*e.JmlContoh},0)))+" ")]),e("td")])]},proxy:!0}])}),e("br"),e("br"),e("div",{staticStyle:{display:"flex"}},[e(p,{attrs:{color:"primary"},on:{click:function(e){return t.OpenReport(e)}}},[e(d,[t._v("mdi-printer")])],1),1==t.forms.StatusID?e(p,{staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:t.Save}},[t._v(" SIMPAN ")]):t._e(),e(h),1==t.forms.StatusID?e(p,{staticStyle:{"margin-left":"5px"},attrs:{color:"success"},on:{click:function(e){t.pay.show=!0}}},[t._v(" BAYAR ")]):t._e()],1)],1),e("ReportPopup",{directives:[{name:"show",rawName:"v-show",value:t.showReport,expression:"showReport"},{name:"click-outside",rawName:"v-click-outside",value:t.CloseReport,expression:"CloseReport"}],attrs:{reportUrl:t.reportUrl}}),e("ParameterUji",{attrs:{forms:t.paramUji,jenisId:t.forms.JenisID},on:{submit:t.AddParameter}})],1)},[],!1,null,null).exports)}}})}();
