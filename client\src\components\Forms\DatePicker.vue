<template>
  <div class="form-coms ui-datepicker">
    <v-menu
      v-model="showPopup"
      :close-on-content-click="false"
      transition="scale-transition"
      offset-y
      min-width="290px"
      min-height="290px"
      class="ui-datepicker-menu"
    >
      <template v-slot:activator="{ on }">
        <Input
          type="text"
          :label="label"
          :disabled="disabled"
          right-icon="mdi-calendar"
          :value.sync="formattedValue"
          v-on="on"
          :width="width"
        />
      </template>
      <v-date-picker
        v-model="vmodel"
        @input="showPopup = false"
      ></v-date-picker>
    </v-menu>
  </div>
</template>
<script>
import moment from 'moment'

export default {
  data: () => ({
    showPopup: false,
    vmodel: null,
  }),
  computed: {
    formattedValue() {
      if (this.vmodel) return moment(this.vmodel).format('DD-MMM-YYYY')
      else return ''
    },
  },
  mounted() {
    this.vmodel = moment(this.value).format('YYYY-MM-DD')
  },
  watch: {
    value(val) {
      if (val) this.vmodel = moment(val).format('YYYY-MM-DD')
    },
    vmodel(val) {
      this.$emit('update:value', val)
    },
  },
  props: {
    label: String,
    disabled: Boolean,
    value: [String, Object, Date],
    width: {
      type: String,
      default: '200px',
    },
  },
}
</script>
<style lang="scss">
.form-inline {
  .ui-datepicker {
    .v-menu__content {
      margin-left: 160px;
    }
    .ui-input {
      display: flex;
      width: 100%;
    }
  }
}
.ui-datepicker {
  margin-bottom: 8px;
}
.v-picker__title {
  display: none;
}
.v-date-picker-table {
  height: 222px;
}

.rounded-forms {
  .ui-datepicker {
    .ui-input {
      border-radius: 8px;
    }
  }
}
</style>
