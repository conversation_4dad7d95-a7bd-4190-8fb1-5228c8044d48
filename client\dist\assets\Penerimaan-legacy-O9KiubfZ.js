!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,r)}return a}function a(t){for(var a=1;a<arguments.length;a++){var n=null!=arguments[a]?arguments[a]:{};a%2?e(Object(n),!0).forEach(function(e){r(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):e(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function r(e,a,r){return(a=function(e){var a=function(e,a){if("object"!=t(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,a||"default");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(e,"string");return"symbol"==t(a)?a:a+""}(a))in e?Object.defineProperty(e,a,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[a]=r,e}function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,a="function"==typeof Symbol?Symbol:{},r=a.iterator||"@@iterator",i=a.toStringTag||"@@toStringTag";function s(a,r,n,i){var s=r&&r.prototype instanceof l?r:l,c=Object.create(s.prototype);return o(c,"_invoke",function(a,r,n){var o,i,s,l=0,c=n||[],d=!1,p={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,a){return o=e,i=0,s=t,p.n=a,u}};function f(a,r){for(i=a,s=r,e=0;!d&&l&&!n&&e<c.length;e++){var n,o=c[e],f=p.p,m=o[2];a>3?(n=m===r)&&(s=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=t):o[0]<=f&&((n=a<2&&f<o[1])?(i=0,p.v=r,p.n=o[1]):f<m&&(n=a<3||o[0]>r||r>m)&&(o[4]=a,o[5]=r,p.n=m,i=0))}if(n||a>1)return u;throw d=!0,r}return function(n,c,m){if(l>1)throw TypeError("Generator is already running");for(d&&1===c&&f(c,m),i=c,s=m;(e=i<2?t:s)||!d;){o||(i?i<3?(i>1&&(p.n=-1),f(i,s)):p.n=s:p.v=s);try{if(l=2,o){if(i||(n="next"),e=o[n]){if(!(e=e.call(o,s)))throw TypeError("iterator result is not an object");if(!e.done)return e;s=e.value,i<2&&(i=0)}else 1===i&&(e=o.return)&&e.call(o),i<2&&(s=TypeError("The iterator does not provide a '"+n+"' method"),i=1);o=t}else if((e=(d=p.n<0)?s:a.call(r,p))!==u)break}catch(e){o=t,i=1,s=e}finally{l=1}}return{value:e,done:d}}}(a,n,i),!0),c}var u={};function l(){}function c(){}function d(){}e=Object.getPrototypeOf;var p=[][r]?e(e([][r]())):(o(e={},r,function(){return this}),e),f=d.prototype=l.prototype=Object.create(p);function m(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,o(t,i,"GeneratorFunction")),t.prototype=Object.create(f),t}return c.prototype=d,o(f,"constructor",d),o(d,"constructor",c),c.displayName="GeneratorFunction",o(d,i,"GeneratorFunction"),o(f),o(f,i,"Generator"),o(f,r,function(){return this}),o(f,"toString",function(){return"[object Generator]"}),(n=function(){return{w:s,m:m}})()}function o(t,e,a,r){var n=Object.defineProperty;try{n({},"",{})}catch(t){n=0}o=function(t,e,a,r){function i(e,a){o(t,e,function(t){return this._invoke(e,a,t)})}e?n?n(t,e,{value:a,enumerable:!r,configurable:!r,writable:!r}):t[e]=a:(i("next",0),i("throw",1),i("return",2))},o(t,e,a,r)}function i(t,e,a,r,n,o,i){try{var s=t[o](i),u=s.value}catch(t){return void a(t)}s.done?e(u):Promise.resolve(u).then(r,n)}function s(t){return function(){var e=this,a=arguments;return new Promise(function(r,n){var o=t.apply(e,a);function s(t){i(o,r,n,s,u,"next",t)}function u(t){i(o,r,n,s,u,"throw",t)}s(void 0)})}}System.register(["./index-legacy-BUdDePUl.js"],function(t,e){"use strict";var r,o,i,u,l,c,d,p,f,m,v,h,y,b,x;return{setters:[function(t){r=t.n,o=t.R,i=t.S,u=t.a,l=t.b,c=t.c,d=t.d,p=t.e,f=t.f,m=t._,v=t.g,h=t.h,y=t.j,b=t.k,x=t.i}],execute:function(){var e=document.createElement("style");e.textContent=".coret{text-decoration:line-through}td .ui-checkbox .--base .--text{margin-top:-2px!important}.dvWarn{position:absolute;top:-48px;color:#fff;background:red;padding:5px 10px;border-radius:5px}\n/*$vite$:1*/",document.head.appendChild(e);t("default",r({components:{SidePane:i,ReportPopup:o},data:function(){return{datagrid:[],dbparams:{PermohonanID:0},spudatagrid:[],rebindSidebar:0,rebindUpload:0,forms:{},reportUrl:"",showReport:!1,spu:{show:!1},jenis:[{val:"A",txt:"Mutu Air & Lingkungan"},{val:"B",txt:"Bahan Bangunan"},{val:"Ba",txt:"Aspal"},{val:"T",txt:"Tanah (Geoteknik)"}],satker:[{val:"1",txt:"APBN"},{val:"2",txt:"APBD I - BMCK"},{val:"3",txt:"APBD I - NON BMCK"},{val:"4",txt:"APBD II"},{val:"5",txt:"Swasta"},{val:"6",txt:"Perorangan"},{val:"7",txt:"Lainnya"}]}},methods:{ItemClick:function(t){var e=this;return s(n().m(function a(){var r;return n().w(function(a){for(;;)switch(a.n){case 0:return e.rebindUpload++,e.dbparams={PermohonanID:t.PermohonanID},a.n=1,e.$api.call("UJI.SelPermohonan",{PermohonanID:t.PermohonanID});case 1:(r=a.v).data.length?e.forms=r.data[0]:e.forms={};case 2:return a.a(2)}},a)}))()},Download:function(t){var e=this;return s(n().m(function a(){return n().w(function(a){for(;;)switch(a.n){case 0:e.$api.download(e.$api.url+t,!0);case 1:return a.a(2)}},a)}))()},Save:function(){var t=this;return s(n().m(function e(){return n().w(function(e){for(;;)switch(e.n){case 0:if(confirm("Sudah yakin pada hasilnya?")){e.n=1;break}return e.a(2);case 1:t.$api.call("UJI_SavPermohonan",a(a({},t.forms),{},{XmlPermohonanDet:t.datagrid})).success&&t.rebindSidebar++;case 2:return e.a(2)}},e)}))()},CetakSPU:function(){var t=this;return s(n().m(function e(){return n().w(function(e){for(;;)switch(e.n){case 0:t.spu.show=!1,t.reportUrl="/reports/uji/spu/"+t.forms.PermohonanID,t.showReport=!0;case 1:return e.a(2)}},e)}))()},CloseReport:function(){var t=this;return s(n().m(function e(){return n().w(function(e){for(;;)switch(e.n){case 0:t.showReport=!1;case 1:return e.a(2)}},e)}))()}}},function(){var t=this,e=t._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{attrs:{statusId:",1,2,3,4,",rebind:t.rebindSidebar},on:{"item-click":t.ItemClick}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.PermohonanID,expression:"forms.PermohonanID"}],staticClass:"right-pane col-12 col-lg-10 col-md-9 col-sm-12",class:t.$api.isMobile()?"":"form-inline",staticStyle:{padding:"20px 20px",width:"calc(100vw - 368px)",overflow:"auto",height:"calc(100vh - 66px)"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:2==t.forms.BayarStatus,expression:"forms.BayarStatus == 2"}],staticClass:"dvWarn"},[t._v(" "+t._s(t.forms.CatatanKhusus)+" ")]),e("div",{staticStyle:{float:"right","font-family":"raleway",background:"white","margin-top":"-20px"},attrs:{id:"dvRightBox"}},[e("div",{staticStyle:{padding:"10px 20px","text-align":"center"}},[e("div",{staticStyle:{"font-size":"xx-large"}},[t._v(t._s(t.forms.NoPengujian))]),e("div",[t._v(t._s(t.forms.ShareCode))])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.StatusID>=4&&1==t.forms.BayarStatus,expression:"forms.StatusID >= 4 && forms.BayarStatus == 1"}],staticStyle:{padding:"10px 20px","border-top":"1px solid #e3e3e3",background:"#4caf50",color:"white","text-align":"center"}},[e("span",[t._v("Sudah Dibayarkan")])]),e("div",{directives:[{name:"show",rawName:"v-show",value:9==t.forms.StatusID,expression:"forms.StatusID == 9"}],staticStyle:{padding:"10px 20px","border-top":"1px solid #e3e3e3",background:"#8bc34a",color:"white","text-align":"center"}},[e("span",[t._v("Sudah Diserahkan")])])]),e(u,{attrs:{type:"text",label:"Nama Pelanggan",value:t.forms.Nama,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"Nama",e)}}}),e(l,{attrs:{label:"Alamat",value:t.forms.Alamat,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"Alamat",e)}}}),t._v(" "),e(u,{attrs:{type:"text",label:"No. Ponsel",value:t.forms.Phone,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"Phone",e)}}}),e(c,{attrs:{items:t.jenis,label:"Nama/Jenis Contoh",value:t.forms.JenisID,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"JenisID",e)}}}),e(d,{staticStyle:{"padding-top":"4px"},attrs:{label:"Tanggal Masuk",value:t.forms.TglMasuk},on:{"update:value":function(e){return t.$set(t.forms,"TglMasuk",e)}}}),e(l,{attrs:{label:"Kegiatan/Paket Pekerjaan",value:t.forms.NamaKegiatan,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"NamaKegiatan",e)}}}),t._v(" "),e(p,{attrs:{label:"Sumber/SatKer"}},[e(c,{attrs:{items:t.satker,value:t.forms.SumberDana,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"SumberDana",e)}}}),e(u,{directives:[{name:"show",rawName:"v-show",value:t.forms.SumberDana<5,expression:"forms.SumberDana < 5"}],staticStyle:{"margin-left":"5px"},attrs:{type:"text",placeholder:"Satker / Kab / Kota",value:t.forms.SatKer,width:"295px"},on:{"update:value":function(e){return t.$set(t.forms,"SatKer",e)}}})],1),e(p,{attrs:{label:"Surat Permohonan/Tgl."}},[e(u,{attrs:{type:"text",value:t.forms.SuratNo,placeholder:"No. Surat",width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"SuratNo",e)}}}),e(d,{staticStyle:{"margin-left":"5px"},attrs:{value:t.forms.SuratTgl,width:"295px"},on:{"update:value":function(e){return t.$set(t.forms,"SuratTgl",e)}}})],1),e(p,{attrs:{label:"Surat Permohonan"}},[e(f,{key:t.rebindUpload,attrs:{value:t.forms.SuratUrl},on:{"update:value":function(e){return t.$set(t.forms,"SuratUrl",e)}},scopedSlots:t._u([{key:"default",fn:function(a){var r=a.opener,n=a.fileName;return[e(m,{directives:[{name:"show",rawName:"v-show",value:n,expression:"fileName"}],staticStyle:{"margin-right":"8px"},attrs:{small:"",text:"",outlined:""},on:{click:function(e){return t.Download(t.forms.SuratUrl)}}},[t._v(t._s(n||t.forms.SuratUrl))]),e(m,{attrs:{small:""},on:{click:r}},[e(v,[t._v("mdi-upload")])],1)]}}])})],1),e(h,{attrs:{datagrid:t.datagrid,dbref:"UJI_SelPermohonanDet",dbparams:t.dbparams,disabled:!0,groupBy:"NamaPaket",columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Jml",value:"JmlContoh"},{name:"Metode",value:"Metode"},{name:"Waktu",value:"Waktu"},{name:"Keterangan",value:"Keterangan"}]},on:{"update:datagrid":function(e){t.datagrid=e}},scopedSlots:t._u([{key:"group-row",fn:function(a){var r=a.row,n=a.columns;return[e("td",{staticStyle:{background:"#f3f3f3"},attrs:{colspan:n.length}},[e("div",{staticStyle:{padding:"0",display:"flex"}},[e("div",{staticStyle:{"font-weight":"bold"}},[t._v(" "+t._s(r.NamaPaket||"(NON PAKET)")+" ")])])])]}},{key:"row-NamaParameter",fn:function(a){var r=a.row;return[e("div",{staticStyle:{display:"flex"}},[e(y,{staticStyle:{position:"relative",top:"-2px"},attrs:{value:r.IsVerified},on:{"update:value":function(e){return t.$set(r,"IsVerified",e)}}}),e("div",{staticStyle:{"margin-left":"10px",position:"relative",top:"2px"}},[e("div",{class:r.IsVerified?"":"coret",staticStyle:{"font-size":"14px"}},[t._v(" "+t._s(r.NamaParameter)+" ")]),e("div",{directives:[{name:"show",rawName:"v-show",value:r.NamaContoh,expression:"row.NamaContoh"}],staticStyle:{color:"gray"}},[t._v(" "+t._s(r.NamaContoh)+" ")])])],1)]}},{key:"row-Keterangan",fn:function(a){var r=a.row;return[e(u,{attrs:{type:"text",value:r.Keterangan,width:"300px"},on:{"update:value":function(e){return t.$set(r,"Keterangan",e)}}})]}}])}),e("div",{staticClass:"form-inline",attrs:{id:"dvBP2"}},[e("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","margin-bottom":"5px","margin-top":"10px"}},[t._v(" PENERIMAAN SAMPLE / CONTOH UJI ")]),e(p,{attrs:{label:"Jumlah"}},[e(y,{staticStyle:{width:"250px"},attrs:{text:t.forms.KondisiJumlah?"Cukup":"Tidak Cukup",value:t.forms.KondisiJumlah},on:{"update:value":function(e){return t.$set(t.forms,"KondisiJumlah",e)}}}),e(u,{staticStyle:{position:"relative",top:"-2px"},attrs:{placeholder:"Keterangan",width:"370px",value:t.forms.KetJumlah},on:{"update:value":function(e){return t.$set(t.forms,"KetJumlah",e)}}})],1),e(p,{attrs:{label:"Kondisi"}},[e(y,{staticStyle:{width:"250px"},attrs:{text:t.forms.KondisiKondisi?"Cukup":"Tidak Cukup",value:t.forms.KondisiKondisi},on:{"update:value":function(e){return t.$set(t.forms,"KondisiKondisi",e)}}}),e(u,{staticStyle:{position:"relative",top:"-2px"},attrs:{placeholder:"Keterangan",width:"370px",value:t.forms.KetKondisi},on:{"update:value":function(e){return t.$set(t.forms,"KetKondisi",e)}}})],1),e(p,{attrs:{label:"Tempat Contoh / Wadah"}},[e(y,{staticStyle:{width:"250px"},attrs:{text:t.forms.KondisiWadah?"Cukup":"Tidak Cukup",value:t.forms.KondisiWadah},on:{"update:value":function(e){return t.$set(t.forms,"KondisiWadah",e)}}}),e(u,{staticStyle:{position:"relative",top:"-2px"},attrs:{placeholder:"Keterangan",width:"370px",value:t.forms.KetWadah},on:{"update:value":function(e){return t.$set(t.forms,"KetWadah",e)}}})],1),e(l,{attrs:{placeholder:"Catatan Penerimaan",value:t.forms.KetPenerimaan,width:"820px"},on:{"update:value":function(e){return t.$set(t.forms,"KetPenerimaan",e)}}})],1),e("br"),e("br"),e("div",{staticStyle:{display:"flex"}},[e(m,{staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:t.Save}},[t._v(" SIMPAN ")]),e(b),1!=t.forms.BayarStatus?e(m,{attrs:{text:"",outlined:"",color:"warning"}},[e(v,{attrs:{left:""}},[t._v("mdi-alert")]),t._v(" BELUM DIBAYAR ")],1):t._e()],1)],1),t.showReport?e("ReportPopup",{directives:[{name:"click-outside",rawName:"v-click-outside",value:t.CloseReport,expression:"CloseReport"}],attrs:{reportUrl:t.reportUrl}}):t._e(),e(x,{attrs:{show:t.spu.show,title:"Surat Perintah Uji",submitText:"CETAK"},on:{"update:show":function(e){return t.$set(t.spu,"show",e)},submit:t.CetakSPU}},[e(h,{attrs:{datagrid:t.spudatagrid,dbref:"UJI.SpuPenguji",dbparams:t.dbparams,columns:[{name:"Nama Penguji",value:"NamaPenguji",width:"200px !important",editable:{com:"Select",dbref:"UJI_SelPenguji",value:"PengujiID",text:"NamaPenguji",width:"200px !important"}},{name:"Jabatan",value:"Jabatan",width:"130px !important",editable:{com:"Select",items:[{val:"Penyelia",txt:"Penyelia"},{val:"Teknisi",txt:"Teknisi"}],width:"130px !important"}}]},on:{"update:datagrid":function(e){t.spudatagrid=e}}})],1)],1)},[],!1,null,null).exports)}}})}();
