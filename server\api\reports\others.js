var express = require('express')
var router = express.Router()
var Reporter = require('./generator')
const path = require('path')
const fs = require('fs')
const PDFMerger = require('pdf-merger-js')
const carbone = require('carbone')
var db = require('../../common/db')
var moment = require('moment')
const archiver = require('archiver')
const pdfmerge = require('easy-pdf-merge');
const {resolve} = require('path')
moment.locale('id')

router.get('/', async function(req, res) {
    
  res.send({
    success: true,
    data: `/get/kwitansi.pdf`,
    message: 'Report Generated',
    type: 'url',
  })
  
})

const mergePdf = (source_files, dest_file_path) => {
  return new Promise((resolve, reject) => {
    pdfmerge(source_files, dest_file_path, function (err) {
      if (err) {
        console.error(err)
      }
      resolve()
    });
  })
}



router.post('/berkas-penyaluran', async function(req, res) {
    
  let data = await db
    .exec('PRM_RptBerkasPenyaluran', req.body, true)
    .catch(err => console.error(err))
  // const data = [{Kwitansi:'/uploads/2134/27020102522.pdf'}, {Kwitansi:'/uploads/2134/27020102523.pdf'}, {Kwitansi:'/uploads/2134/14021241658.pdf'}]
  let kwitansi = []; //new PDFMerger();
  let ktpKades = []; //new PDFMerger();
  let ktpBendahara = []; //new PDFMerger();
  let bukuRekening = []; //new PDFMerger();
  let csv = `Kabupaten,Kecamatan,Desa,KTP Kades, KTP Bendahara, Kwitansi, Buku Rekening\r\n`
  for(const d of data){
    let line = [d.Kabupaten, d.Kecamatan, d.Kelurahan]
    if(d.KtpKades && d.KtpKades.match(/\.pdf$/)) {
      let stat = fs.statSync(d.KtpKades.replace(/^\//,''))
      if(!stat.size) console.log(d.KtpKades)
      // try { ktpKades.add(d.KtpKades.replace(/^\//,'')) } 
      // catch(err) { console.log(d.KtpKades);console.error(err) }
      ktpKades.push(d.KtpKades.replace(/^\//,''))
      line.push(d.KtpKades)
    } else {
      line.push('')
    }
    if(d.KtpBendahara && d.KtpBendahara.match(/\.pdf$/)) {
      // try { ktpBendahara.add(d.KtpBendahara.replace(/^\//,'')) } 
      // catch(err) { console.log(d.KtpBendahara);console.error(err) }
      ktpBendahara.push(d.KtpBendahara.replace(/^\//,''))
      line.push(d.KtpBendahara)
    } else {
      line.push('')
    }
    if(d.Kwitansi && d.Kwitansi.match(/\.pdf$/)) {
      // try { kwitansi.add(d.Kwitansi.replace(/^\//,'')) } 
      // catch(err) { console.log(d.Kwitansi); console.error(err) }
      kwitansi.push(d.Kwitansi.replace(/^\//,''))
      line.push(d.Kwitansi)
    } else {
      line.push('')
    }
    if(d.BukuRekening && d.BukuRekening.match(/\.pdf$/)) {
      // try { bukuRekening.add(d.BukuRekening.replace(/^\//,'')) } 
      // catch(err) { console.log(d.BukuRekening);console.error(err) }
      bukuRekening.push(d.BukuRekening.replace(/^\//,''))
      line.push(d.BukuRekening)
    } else {
      line.push('')
    }
    csv += line.join(",")+'\r\n'
  }
  // await kwitansi.save('tmp/kwitansi.pdf');
  // await ktpKades.save('tmp/ktp_kades.pdf');
  // await ktpBendahara.save('tmp/ktp_bendahara.pdf');
  // await bukuRekening.save('tmp/buku_rekening.pdf');
  await Promise.all([
    mergePdf(kwitansi, 'tmp/kwitansi.pdf'),
    mergePdf(ktpKades, 'tmp/ktp_kades.pdf'),
    mergePdf(ktpBendahara, 'tmp/ktp_bendahara.pdf'),
    mergePdf(bukuRekening, 'tmp/buku_rekening.pdf')
  ])
  fs.writeFileSync('tmp/daftar_berkas.csv', csv);

  const archive = archiver('zip', {
    zlib: {level: 9} // Sets the compression level.
  });
  archive.pipe(fs.createWriteStream('tmp/berkas-penyaluran.zip'));
  archive.file('tmp/kwitansi.pdf', {name: 'kwitansi.pdf'});
  archive.file('tmp/ktp_kades.pdf', {name: 'ktp_kades.pdf'});
  archive.file('tmp/ktp_bendahara.pdf', {name: 'ktp_bendahara.pdf'});
  archive.file('tmp/buku_rekening.pdf', {name: 'buku_rekening.pdf'});
  archive.file('tmp/daftar_berkas.csv', {name: 'daftar_berkas.csv'});
  await archive.finalize();
    
  res.send({
    success: true,
    data: `/get/berkas-penyaluran.zip`,
    message: 'Report Generated',
    type: 'url',
  })
  
})

module.exports = router
