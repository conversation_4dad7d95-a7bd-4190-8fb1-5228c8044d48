<template>
  <v-container :class="isMobile ? 'is-mobile' : ''">
    <Header />
    <NavBar />
    <router-view />
  </v-container>
</template>
<script>
import Header from '../Header/Header'
import NavBar from '../Header/NavBar'
export default {
  components: {
    Header,
    NavBar,
  },
  computed: {
    isMobile() {
      return window.innerWidth < window.innerHeight
    },
  },
}
</script>
<style lang="css">
/*========== GOOGLE FONTS ==========*/
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap');
@import url('https://cdn.jsdelivr.net/npm/boxicons@latest/css/boxicons.min.css');

/*========== VARIABLES CSS ==========*/
:root {
  --header-height: 3.5rem;
  --nav-width: 280px;

  /*========== Colors ==========*/
  --first-color: #6923d0;
  --first-color-light: #f4f0fa;
  --title-color: #19181b;
  --text-color: #58555e;
  --text-color-light: #a5a1aa;
  /*--body-color: #f9f6fd;
  --container-color: #ffffff; */

  /*========== Font and typography ==========*/
  --body-font: 'Poppins', sans-serif;
  --normal-font-size: 0.938rem;
  --small-font-size: 0.75rem;
  --smaller-font-size: 0.75rem;

  /*========== Font weight ==========*/
  --font-medium: 500;
  --font-semi-bold: 600;

  /*========== z index ==========*/
  --z-fixed: 100;
}

@media screen and (min-width: 1024px) {
  :root {
    --normal-font-size: 1rem;
    --small-font-size: 0.875rem;
    --smaller-font-size: 0.813rem;
  }
}

@media (min-width: 1264px) {
  .container {
    max-width: 100vw;
    padding: 0;
    margin: 0;
  }
}
@media (min-width: 960px) {
  .container {
    max-width: 100vw;
    padding: 0;
    margin: 0;
  }
}
.container {
  min-height: 100vh;
}
.v-application--wrap {
  /* min-height: calc(100vh - 3.5rem - 8px); */
  min-height: 100%;
}

/*========== BASE ==========*/
*,
::before,
::after {
  box-sizing: border-box;
}

body {
  margin: var(--header-height) 0 0 0;
  /*padding: 1rem 1rem 0;*/
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
  background-color: var(--body-color);
  color: var(--text-color);
}

h3 {
  margin: 0;
}

a {
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
}

/*========== HEADER ==========*/
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: var(--container-color);
  box-shadow: 0 1px 0 rgba(22, 8, 43, 0.1);
  padding: 0 1rem;
  z-index: var(--z-fixed);
}

.header__container {
  display: flex;
  align-items: center;
  height: var(--header-height);
  justify-content: space-between;
}

.header__img {
  width: 35px;
  height: 35px;
  border-radius: 50%;
}

.header__logo {
  color: var(--title-color);
  font-weight: var(--font-medium);
  display: none;
}

.header__search {
  display: flex;
  padding: 0.4rem 0.75rem;
  background-color: var(--first-color-light);
  border-radius: 0.25rem;
}

.header__input {
  width: 100%;
  border: none;
  outline: none;
  background-color: var(--first-color-light);
}

.header__input::placeholder {
  font-family: var(--body-font);
  color: var(--text-color);
}

.header__icon,
.header__toggle {
  font-size: 1.2rem;
}

.header__toggle {
  color: var(--title-color);
  cursor: pointer;
}

/*========== NAV ==========*/
#navbar {
  background: white;
}
.nav {
  position: fixed;
  top: 0;
  left: -100%;
  height: 100vh;
  padding: 1rem 1rem 0;
  background-color: var(--container-color);
  box-shadow: 1px 0 0 rgba(22, 8, 43, 0.1);
  z-index: var(--z-fixed);
  transition: 0.4s;
}

.nav__container {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-bottom: 3rem;
  overflow: auto;
  scrollbar-width: none; /* For mozilla */
}

/* For Google Chrome and others */
.nav__container::-webkit-scrollbar {
  display: none;
}

.nav__logo {
  font-weight: var(--font-semi-bold);
  margin-bottom: 2.5rem;
}

.nav__list,
.nav__items {
  display: grid;
}

.nav__list {
  row-gap: 2.5rem;
}

.nav__items {
  row-gap: 1.5rem;
}

.nav__subtitle {
  font-size: var(--normal-font-size);
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  color: var(--text-color-light);
}

.nav__link {
  display: flex;
  align-items: center;
  color: var(--text-color);
}

.nav__link:hover {
  color: var(--first-color);
}

.nav__icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.nav__name {
  font-size: var(--small-font-size);
  font-weight: var(--font-medium);
  white-space: nowrap;
}

.nav__logout {
  margin-top: 5rem;
}

/* Dropdown */
.nav__dropdown {
  overflow: hidden;
  max-height: 21px;
  transition: 0.4s ease-in-out;
}

.nav__dropdown-collapse {
  background-color: var(--first-color-light);
  border-radius: 0.25rem;
  margin-top: 1rem;
}

.nav__dropdown-content {
  display: grid;
  row-gap: 0.5rem;
  padding: 0.75rem 2.5rem 0.75rem 1.8rem;
}

.nav__dropdown-item {
  font-size: var(--smaller-font-size);
  font-weight: var(--font-medium);
  color: var(--text-color);
}

.nav__dropdown-item:hover {
  color: var(--first-color);
}

.nav__dropdown-icon {
  margin-left: auto;
  transition: 0.4s;
}

/* Show dropdown collapse */
.nav__dropdown:hover {
  max-height: 100rem;
}

/* Rotate icon arrow */
.nav__dropdown:hover .nav__dropdown-icon {
  transform: rotate(180deg);
}

/*===== Show menu =====*/
.show-menu {
  left: 0;
}

/*===== Active link =====*/
.active {
  color: var(--first-color);
}

/* ========== MEDIA QUERIES ==========*/
/* For small devices reduce search*/
@media screen and (max-width: 320px) {
  .header__search {
    width: 70%;
  }
}

@media screen and (min-width: 768px) {
  body {
    /*padding: 0.5rem 3rem 0 6rem;*/
    padding: 8px 0 0 68px;
  }
  .header {
    padding: 0 3rem 0 6rem;
  }
  .header__container {
    height: calc(var(--header-height) + 0.5rem);
  }
  .header__search {
    width: 300px;
    padding: 0.55rem 0.75rem;
  }
  .header__toggle {
    display: none;
  }
  .header__logo {
    display: block;
  }
  .header__img {
    width: 40px;
    height: 40px;
    order: 1;
  }
  .nav {
    left: 0;
    padding: 1.2rem 1.5rem 0;
    width: 68px; /* Reduced navbar */
  }
  .nav__items {
    row-gap: 1.7rem;
  }
  .nav__icon {
    font-size: 1.3rem;
  }

  /* Element opacity */
  .nav__logo-name,
  .nav__name,
  .nav__subtitle,
  .nav__dropdown-icon {
    opacity: 0;
    transition: 0.3s;
  }

  /* Navbar expanded */
  .nav:hover {
    width: var(--nav-width);
  }

  /* Visible elements */
  .nav:hover .nav__logo-name {
    opacity: 1;
  }
  .nav:hover .nav__subtitle {
    opacity: 1;
  }
  .nav:hover .nav__name {
    opacity: 1;
  }
  .nav:hover .nav__dropdown-icon {
    opacity: 1;
  }
}
</style>
