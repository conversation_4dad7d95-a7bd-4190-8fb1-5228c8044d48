<template>
  <div style="display: flex">
    <SidePane @item-click="ItemClick" statusId=",6,7," />
    <div
      v-show="forms.Nama"
      class="right-pane col-12 col-lg-10 col-md-9 col-sm-12"
      :class="$api.isMobile() ? '' : 'form-inline'"
      style="padding: 0"
    >
      <div
        style="
          background: #039ae4;
          color: white;
          padding: 15px 15px 10px 15px;
          display: flex;
        "
      >
        <div style="display: flex">
          {{ forms.Nama }}
          <div
            style="
              background: white;
              color: #039ae4;
              margin-left: 10px;
              padding: 5px 8px;
              border-radius: 5px;
              font-size: small;
              position: relative;
              top: -3px;
            "
          >
            {{ forms.NoPengujian }}
          </div>
        </div>

        <v-spacer />
        <v-btn
          small
          disabled
          style="color: white !important; position: relative; top: -2px"
        >
          SERTIFIKASI
        </v-btn>
      </div>
      <Grid
        class="table-cekprogress"
        :datagrid.sync="datagrid"
        dbref="UJI_SelCekSertifikat"
        :dbparams="dbparams"
        :disabled="true"
        width="100%"
        :columns="[
          {
            name: 'Parameter Uji',
            value: 'NamaParameter',
          },
          {
            name: 'Jml',
            value: 'JmlContoh',
            class: 'align-right',
            width: '100px',
          },
          {
            name: 'Metode',
            value: 'Metode',
            width: '150px',
          },
          {
            name: 'Waktu',
            value: 'Waktu',
            class: 'align-right',
            width: '150px',
          },
        ]"
      >
        <template v-slot:row-Waktu="{ row }"> {{ row.Waktu }} Hari </template>
        <template v-slot:row-Sertifikat="{ row }">
          <v-btn
            x-small
            color="primary"
            v-show="!row.SertifikatStatus"
            @click="UpdSertifikat(row.PermohonanID, row.ParameterID, 1)"
          >
            Terbitkan
          </v-btn>
          <v-btn
            x-small
            outlined
            color="primary"
            v-show="row.SertifikatStatus"
            @click="UpdSertifikat(row.PermohonanID, row.ParameterID, 0)"
          >
            Batalkan
          </v-btn>
        </template>
        <template v-slot:row-Pengantar="{ row }">
          <v-btn
            x-small
            color="primary"
            v-show="!row.PengantarStatus"
            @click="UpdPengantar(row.PermohonanID, row.ParameterID, 1)"
          >
            Terbitkan
          </v-btn>
          <v-btn
            x-small
            outlined
            color="primary"
            v-show="row.PengantarStatus"
            @click="UpdPengantar(row.PermohonanID, row.ParameterID, 0)"
          >
            Batalkan
          </v-btn>
        </template>
      </Grid>
      <div style="padding: 10px" v-if="!forms.BayarDate">
        <v-alert
          border="bottom"
          colored-border
          type="warning"
          elevation="2"
          style="width: 600px; margin: auto"
        >
          PENGAJUAN INI BELUM MENYELESAIKAN PEMBAYARAN
        </v-alert>
      </div>
      <div
        v-for="(lk, idx) in lembarKerja"
        :key="idx"
        style="
          font-size: 12px;
          background: #f3f3f3;
          display: flex;
          margin-bottom: 1px;
        "
      >
        <v-btn
          x-small
          text
          color="success"
          v-if="lk.SignedUrl"
          style="margin-top: 4px"
        >
          <v-icon> mdi-check-circle </v-icon>
        </v-btn>
        <v-btn
          small
          text
          color="primary"
          v-tooltip="'Download Lembar Kerja'"
          @click="Open('/' + lk.SignedUrl)"
        >
          {{ lk.Nama }}
        </v-btn>
        <v-spacer />
        <v-btn
          v-tooltip="'History'"
          @click="showHistory = true"
          x-small
          text
          outlined
          style="margin: 4px"
        >
          <v-icon x-small>mdi-history</v-icon>
        </v-btn>
        <div v-if="lk.Approved2By" style="display: flex">
          <MenuButton
            :menu="['Revisi', 'Hapus']"
            @item-click="MenuHapus($event, lk.LembarKerjaID)"
          >
            <template v-slot="{ on }">
              <v-btn v-on="on" x-small text outlined style="margin: 4px">
                <v-icon x-small>mdi-dots-horizontal</v-icon>
              </v-btn>
            </template>
          </MenuButton>
          <v-btn
            v-show="lk.RawUrl"
            text
            outlined
            x-small
            style="margin: 4px; margin-right: 16px"
            :color="lk.SetifikatUrl ? 'success' : 'primary'"
            v-tooltip="'Download Sertifikat'"
            @click="Open(lk.SetifikatUrl || lk.RawUrl)"
          >
            <v-icon x-small left>mdi-download</v-icon>
            SERTIFIKAT
          </v-btn>
          <Uploader
            ref="sertuploader"
            v-show="!lk.RawUrl"
            @change="fileUploadedSR($event, lk.LembarKerjaID)"
            convert="pdf"
          >
            <template v-slot="{ opener }">
              <v-btn
                x-small
                style="margin: 4px; margin-right: 16px"
                color="primary"
                v-tooltip="'Upload Sertifikat'"
                @click="opener"
              >
                <v-icon x-small left>mdi-upload</v-icon>
                SERTIFIKAT
              </v-btn>
            </template>
          </Uploader>
          <v-btn
            x-small
            style="margin: 4px"
            color="primary"
            v-tooltip="'Download Surat Pengantar'"
            @click="DownloadPengantar(lk.PermohonanID)"
          >
            <v-icon left x-small>mdi-download</v-icon>
            SURAT PENGANTAR
          </v-btn>
        </div>
        <v-btn
          v-else
          x-small
          text
          outlined
          :disabled="true"
          style="margin: 4px"
        >
          DALAM PROSES
        </v-btn>
      </div>
      <div
        v-if="!lembarKerja.length"
        style="padding: 10px; display: flex; background: #eee"
      >
        <Uploader
          v-if="!forms.SignedUrl"
          @change="fileUploaded"
          :multiple="true"
          convert="pdf"
        >
          <template v-slot="{ opener }">
            <v-btn
              :outlined="forms.RawUrl"
              color="primary"
              @click="opener"
              :disabled="forms.SignedUrl"
            >
              {{ forms.RawUrl ? 'SUDAH DIUPLOAD' : 'UPLOAD SERTIFIKAT' }}
            </v-btn>
          </template>
        </Uploader>
        <v-btn v-else color="success" @click="DownloadSertifikat">
          DOWNLOAD SERTIFIKAT
        </v-btn>
        <v-spacer />
        <v-btn
          v-show="forms.SignedUrl"
          color="success"
          @click="DownloadPengantar"
        >
          DOWNLOAD PENGANTAR
        </v-btn>
      </div>
      <!-- <div v-else v-for="(lk, idx) in lembarKerja" :key="idx"
        style="font-size:12px; background: #f3f3f3; display:flex; margin-bottom:1px; padding:8px;">
        <v-btn small text color="primary" @click="Open(lk.LkUrl, lk.LembarKerjaID)">
          {{lk.Nama}}
        </v-btn>
        <v-spacer />
        <v-btn x-small style="margin: 4px" color="primary"
          v-if="lk.SignedUrl">
          UPLOAD SERTIFIKAT
        </v-btn>
        <v-btn v-else x-small text outlined :disabled="true" style="margin: 4px">
          DALAM PROSES
        </v-btn>
      </div> -->
    </div>
    <multiple-download :forms="multiSertifikat" />
    <History
      :show.sync="showHistory"
      :permohonanId="forms.PermohonanID"
    ></History>
  </div>
</template>
<script>
import SidePane from '../Loket/SidePane.vue'
import MultipleDownload from './MultipleDownload.vue'
import History from './History.vue'
export default {
  components: {
    SidePane,
    MultipleDownload,
    History,
  },
  data: () => ({
    datagrid: [],
    lembarKerja: [],
    dbparams: { PermohonanID: 0 },
    forms: {},
    multiSertifikat: { show: false },
    showHistory: false,
  }),
  methods: {
    async ItemClick(val) {
      this.Populate(val.PermohonanID)
    },
    async Populate(id) {
      this.dbparams = { PermohonanID: id }
      this.PopulateLK(id)
      var ret = await this.$api.call('UJI.SelPermohonan', {
        PermohonanID: id,
      })
      if (ret.data.length) {
        this.forms = ret.data[0]
      } else {
        this.forms = {}
      }
      // if (this.forms.StatusID >= 5) {
      this.PopulateLK(id)
      // }
    },
    async PopulateLK(id) {
      this.lembarKerja = []
      var ret = await this.$api.call('UJI.SelLembarKerja', {
        PermohonanID: id,
      })
      this.lembarKerja = ret.data
    },
    async UpdPengantar(id, param_id, val) {
      if (val == 1) {
        if (!confirm('Terbitkan Pengantar?')) return
      } else {
        if (!confirm('Batalkan Penerbitan Pengantar?')) return
      }
      var res = await this.$api.call('UJI_UpdPengantar', {
        PermohonanID: id,
        ParameterID: param_id,
        PengantarStatus: val,
      })

      if (res.success) {
        if (res.data[0].IsFinished.data == 1) {
          var text = `Kami dari BP2 Prov. Jawa Tengah menginformasikan bahwa pengujian dengan no ${this.forms.NoPengujian}, telah selesai dan sertifikat siap untuk diambil. 
            Pengambilan sertifikat hasil uji *wajib* membawa bukti pembayaran.`

          window.open(
            `https://wa.me/${this.forms.Phone.replace(
              /^08/,
              '628'
            )}?text=${text}`,
            '_blank'
          )
        }

        this.Populate(id)
      }
    },
    async UpdSertifikat(id, param_id, val) {
      if (val == 1) {
        if (!confirm('Terbitkan Sertifikat?')) return
      } else {
        if (!confirm('Batalkan Penerbitan Sertifikat?')) return
      }

      var res = await this.$api.call('UJI_UpdSertifikat', {
        PermohonanID: id,
        ParameterID: param_id,
        SertifikatStatus: val,
      })

      if (res.success) {
        this.Populate(id)
      }
    },
    Open(url) {
      // console.log(url)
      window.open(this.$api.url + url.replace(/\.{3,4}$/, '.pdf'), '_blank')
    },
    async fileUploaded(file) {
      // let res = await this.$api.get(
      //   '/reports/uji/serialnumber?FilePath=' + file.data
      // )
      this.$api.notify('Upload Sukses')
      let rex = await this.$api.call('UJI_SavSertifikat', {
        PermohonanID: this.forms.PermohonanID,
        // SerialNumber: res.data,
        XmlRawUrl: file.data,
      })
      if (rex.success) {
        // this.forms.SerialNumber = res.data
        this.forms.RawUrl = file.data
        // window.open(this.$api.url + file.data.replace(/xlsx/, 'pdf'), '_blank')
      }
    },
    async MenuHapus(txt, lkId) {
      if (txt == 'Hapus') {
        let rex = await this.DelSertifikat(lkId)
        if (rex.success) {
          this.PopulateLK(this.forms.PermohonanID)
        }
      } else if (txt == 'Revisi') {
        await this.DelSertifikat(lkId)
        this.$refs.sertuploader[0].handleOpen()
      } else if (txt == 'History') {
        this.showHistory = true
      }
    },
    async DelSertifikat(lkId) {
      if (!confirm('Anda yakin menghapus/revisi Sertifikat?')) return
      let rex = await this.$api.call('UJI_DelSertifikatLK', {
        PermohonanID: this.forms.PermohonanID,
        LembarKerjaID: lkId,
      })
      return rex
    },
    async fileUploadedSR(file, lkId) {
      this.$api.notify('Upload Sukses')

      let rex = await this.$api.call('UJI_SavSertifikatLK', {
        PermohonanID: this.forms.PermohonanID,
        LembarKerjaID: lkId,
        RawUrl: file.data,
      })
      if (rex.success) {
        // this.forms.SerialNumber = res.data
        this.forms.RawUrl = file.data
        this.PopulateLK(this.forms.PermohonanID)
        // window.open(this.$api.url + file.data.replace(/xlsx/, 'pdf'), '_blank')
      }
    },
    DownloadSertifikat() {
      let urls = this.forms.SignedUrl.split(',')
      if (urls.length > 1) {
        this.multiSertifikat.data = urls
        this.multiSertifikat.show = true
      } else {
        window.open(this.$api.url + urls[0], '_blank')
      }
    },
    DownloadPengantar() {
      window.open(
        this.$api.url + '/reports/uji/pengantar/' + this.forms.PermohonanID,
        '_blank'
      )
    },
  },
}
</script>
<style lang="scss">
.table-cekprogress {
  table {
    min-width: 750px;
  }
}
</style>
