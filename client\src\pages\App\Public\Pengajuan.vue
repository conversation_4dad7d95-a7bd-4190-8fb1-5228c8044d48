<template>
  <div class="panel-pengajuan">
    <div style="background: lightyellow; padding: 5px 0px; display: flex">
      <v-btn text color="primary" @click="OpenManual">
        TATA CARA PENGISIAN FORMULIR
      </v-btn>
      <v-spacer />
      <v-btn color="error" @click="Close">
        <v-icon>mdi-close</v-icon>
      </v-btn>
    </div>
    <div
      class="form-content"
      :class="{
        'form-inline': !isMobile,
      }"
    >
      <Input
        type="text"
        label="Nama Pelanggan*"
        :value.sync="forms.Nama"
        width="385px"
      />
      <Input
        type="text"
        label="No. Ponsel"
        :value.sync="forms.Phone"
        width="385px"
        v-show="showDaftar"
      />
      <div class="form-info" v-show="showDaftar">
        Isilah dengan nomor WhatsApp untuk mendapatkan pelayanan maksimal. Nomor
        HANYA akan digunakan untuk kepentingan pelayanan BP2, dan <PERSON> AKAN
        diberikan kepada pihak lain.
      </div>
      <Input
        type="text"
        label="Email"
        :value.sync="forms.Email"
        width="385px"
      />
      <TextArea label="Alamat*" :value.sync="forms.Alamat" width="385px" />
      <Select
        :items="jenis"
        label="Nama/Jenis Contoh*"
        :value.sync="forms.JenisID"
        width="385px"
      />
      <DatePicker
        v-show="false"
        label="Tanggal Masuk"
        :value.sync="forms.TglMasuk"
        style="padding-top: 4px"
      />
      <TextArea
        label="Kegiatan/Paket Pekerjaan*"
        :value.sync="forms.NamaKegiatan"
        width="385px"
      />
      <Label label="Sumber/SatKer*">
        <Select
          :items="satker"
          :value.sync="forms.SumberDana"
          :required="true"
        />
        <Input
          v-if="['1', '3', '4'].includes(forms.SumberDana) || satkerNotExist"
          type="text"
          placeholder="Satker / Kab / Kota"
          :value.sync="forms.SatKer"
          style="margin-left: 5px"
          width="295px"
        />
        <Select
          v-else-if="forms.SumberDana == 2"
          :items="bmck"
          :value.sync="forms.SatKer"
          style="margin-left: 5px"
          width="295px"
          :placeholder="forms.SatKer"
        />
      </Label>
      <Label label="Surat Permohonan/Tgl.">
        <Input
          type="text"
          :value.sync="forms.SuratNo"
          placeholder="No. Surat"
        />
        <DatePicker :value.sync="forms.SuratTgl" style="margin-left: 5px" />
      </Label>
      <Label label="Surat Permohonan">
        <Uploader :value.sync="forms.SuratUrl" :key="rebindUpload">
          <template v-slot="{ opener, fileName }">
            <v-btn
              small
              text
              outlined
              v-show="fileName"
              style="margin-right: 8px"
              @click="Download(forms.SuratUrl)"
              >{{ fileName || forms.SuratUrl }}</v-btn
            >
            <v-btn small @click="opener">
              <v-icon>mdi-upload</v-icon>
            </v-btn>
          </template>
        </Uploader>
      </Label>
      <Grid
        :datagrid.sync="datagrid"
        dbref="WEB.PermohonanDet"
        :dbparams="dbparams"
        :disabled="true"
        :columns="[
          {
            name: 'Parameter Uji',
            value: 'NamaParameter',
          },
          {
            name: 'Nama Contoh',
            value: 'NamaContoh',
          },
          {
            name: 'Jml',
            value: 'JmlContoh',
          },
          {
            name: 'Metode',
            value: 'Metode',
          },
          {
            name: 'Harga',
            value: 'Harga',
            class: 'align-right',
          },
          {
            name: '',
            value: 'Delete',
          },
        ]"
      >
        <template v-slot:row-NamaContoh="{ row }">
          <Input
            :value.sync="row.NamaContoh"
            placeholder="(asal/ukuran contoh)"
            width="200px"
          />
        </template>
        <template v-slot:row-JmlContoh="{ row }">
          <Input
            type="number"
            :value.sync="row.JmlContoh"
            placeholder="Jml"
            width="60px"
          />
        </template>
        <template v-slot:row-Harga="{ row }">
          {{ (row.Harga * row.JmlContoh) | format }}
        </template>
        <template v-slot:row-Delete="{ idx }">
          <v-icon color="error" @click="DelParameter(idx)"
            >mdi-trash-can</v-icon
          >
        </template>
        <template v-slot:footer>
          <tr>
            <td colspan="4">
              <v-btn text small @click="paramUji.show = true">
                <v-icon left>mdi-plus-circle</v-icon>
                Tambah Parameter Uji
              </v-btn>
            </td>
            <td
              class="align-right"
              style="font-weight: bold; padding: 8px 12px"
            >
              {{
                datagrid.reduce((res, d) => {
                  return res + d.Harga * d.JmlContoh
                }, 0) | format
              }}
            </td>
            <td></td>
          </tr>
        </template>
      </Grid>
      <br />
      <br />
      <div style="display: flex">
        <v-spacer />
        <v-btn
          text
          outlined
          color="primary"
          style="margin-left: 5px"
          v-show="!forms.PermohonanID"
          @click="AjukanPendaftaran(true)"
        >
          AJUKAN DAN DAFTAR LAGI
        </v-btn>
        <v-btn
          color="primary"
          style="margin-left: 5px"
          v-show="!forms.Kesimpulan"
          @click="AjukanPendaftaran(false)"
        >
          {{ forms.PermohonanID ? 'SIMPAN' : 'AJUKAN PERMOHONAN' }}
        </v-btn>
      </div>
    </div>
    <ReportPopup
      v-show="showReport"
      :reportUrl="reportUrl"
      v-click-outside="CloseReport"
    />
    <ParameterUji
      :forms="paramUji"
      :jenisId="forms.JenisID"
      @submit="AddParameter"
    />
    <Modal
      title="Daftarkan Akun Anda"
      :show.sync="startShow"
      submitText="DAFTAR"
      cancelText="LEWATI"
      @submit="$router.push('/login?mode=daftar')"
    >
      <div style="padding: 5px">
        <div style="font-weight: bold">
          DAFTARKAN AKUN ANDA<br />
          UNTUK PENGALAMAN LEBIH BAIK<br />
        </div>
        <div>atau klik "lewati" untuk mengajukan tanpa mendaftar</div>
      </div>
    </Modal>
    <Modal
      title="Daftar Pengujian Baru"
      :show.sync="syarat.show"
      :submitText="forms.IsAgree ? 'AJUKAN' : ''"
      @submit="Save"
    >
      <div>
        <p style="font-weight: bold" v-show="bahan.length">
          DENGAN MELAKUKAN PENDAFTARAN INI <br />
          ANDA MENYETUJUI UNTUK MEMBAWA BAHAN UJI SEBAGAI BERIKUT:
          <br />
        </p>
        <div v-show="bahan.length">
          <div v-for="(item, idx) in bahan" :key="idx">
            <!-- - {{ item.Bahan }} {{ item.Jumlah }} (per sample)
            {{ item.Keterangan }} ({{ item.Nama }}, {{ item.Metode }}) -->
            - {{ item.Keterangan }} ({{ item.Nama }}, {{ item.Metode }})
          </div>
          <br />
        </div>
        <p>
          Untuk mempersingkat waktu, anda dapat mencetak formulir pendaftaran
          ini <br />
          untuk disertakan pada saat menyerahkan bahan uji. <br />
          Data yang diisikan didalam form pendaftaran akan
          <b>digunakan dalam penerbitan sertifikat</b>.
        </p>
        <hr />
        <Checkbox text="Saya Setuju" :value.sync="forms.IsAgree" />
        <Checkbox
          text="Download Formulir Pendaftaran"
          :value.sync="forms.DownloadForm"
        />
        <hr />
      </div>
    </Modal>
    <ReportPopup
      v-show="showReport"
      :reportUrl="reportUrl"
      v-click-outside="CloseReport"
    />
  </div>
</template>
<script>
import ReportPopup from '../../ReportPopup.vue'
import ParameterUji from '../../Loket/ParameterUji.vue'
export default {
  components: {
    ParameterUji,
    ReportPopup,
  },
  data: () => ({
    datagrid: [],
    startShow: false,
    rebindSidebar: 0,
    rebindUpload: 0,
    dbparams: { PermohonanID: 0 },
    forms: {},
    syarat: { show: false },
    paramUji: {
      show: false,
    },
    bahan: [],
    showReport: false,
    reportUrl: '',
    daftarLagi: false,
    jenis: [
      { val: 'A', txt: 'Mutu Air & Lingkungan' },
      { val: 'B', txt: 'Bahan Bangunan' },
      { val: 'Ba', txt: 'Aspal' },
      { val: 'T', txt: 'Tanah (Geoteknik)' },
    ],
    satker: [
      { val: '1', txt: 'APBN' },
      { val: '2', txt: 'APBD I - BMCK' },
      { val: '3', txt: 'APBD I - NON BMCK' },
      { val: '4', txt: 'APBD II' },
      { val: '5', txt: 'Swasta' },
      { val: '6', txt: 'Perorangan' },
      { val: '7', txt: 'Lainnya' },
    ],
    bmck: [
      { val: 'RANCANG BANGUN', txt: 'RANCANG BANGUN' },
      { val: 'WILAYAH TIMUR', txt: 'WILAYAH TIMUR' },
      { val: 'WILAYAH BARAT', txt: 'WILAYAH BARAT' },
      { val: 'SARANA PRASARANA', txt: 'SARANA PRASARANA' },
      { val: 'BPJ SEMARANG', txt: 'BPJ SEMARANG' },
      { val: 'BPJ PATI', txt: 'BPJ PATI' },
      { val: 'BPJ PURWODADI', txt: 'BPJ PURWODADI' },
      { val: 'BPJ SURAKARTA', txt: 'BPJ SURAKARTA' },
      { val: 'BPJ MAGELANG', txt: 'BPJ MAGELANG' },
      { val: 'BPJ WONOSOBO', txt: 'BPJ WONOSOBO' },
      { val: 'BPJ CILACAP', txt: 'BPJ CILACAP' },
      { val: 'BPJ TEGAL', txt: 'BPJ TEGAL' },
      { val: 'BPJ PEKALONGAN', txt: 'BPJ PEKALONGAN' },
    ],
  }),
  props: {
    showDaftar: Boolean,
    permohonanId: [String, Number],
  },
  computed: {
    isMobile() {
      return window.innerWidth < window.innerHeight
    },
    satkerNotExist() {
      return (
        this.forms.SumberDana == 2 &&
        this.forms.SatKer &&
        !this.bmck.map((v) => v.val).includes(this.forms.SatKer)
      )
    },
  },
  watch: {
    'syarat.show'(val) {
      if (!val) {
        this.forms.IsAgree = false
        this.forms.DownloadForm = false
      }
    },
    permohonanId(id) {
      this.Populate(id)
    },
  },
  mounted() {
    if (this.showDaftar) this.startShow = true
    this.Populate(this.permohonanId)
  },
  methods: {
    async ItemClick(val) {
      this.Populate(val.PermohonanID)
    },
    async Download(val) {
      this.$api.download(this.$api.url + val, true)
    },
    async Populate(id) {
      this.rebindUpload++
      this.dbparams = { PermohonanID: id }
      var ret = await this.$api.call('WEB.SelPermohonan', {
        PermohonanID: id,
      })
      if (ret.data.length) {
        this.forms = ret.data[0]
      } else {
        this.forms = {}
      }
    },
    async Save() {
      if (this.forms.IsAgree) {
        var res = await this.$api.call('WEB.SavPermohonan', {
          ...this.forms,
          XmlPermohonanDet: this.datagrid,
        })
        if (res.success) {
          if (this.forms.DownloadForm) {
            this.showReport = true
            this.reportUrl =
              '/reports/uji/permohonanonline/' + res.data[0].PermohonanID
          } else {
            if (!this.daftarLagi) this.$emit('close')
            else {
              this.bahan = []
              this.datagrid = []
            }
          }
          this.syarat.show = false
        }
      }
    },
    AddParameter(params) {
      this.datagrid.push(...params)
    },
    DelParameter(idx) {
      this.datagrid.splice(idx, 1)
    },
    OpenReport() {
      setTimeout(() => {
        this.showReport = true
      }, 100)
    },
    CloseReport() {
      if (this.showReport) {
        this.showReport = false
        if (!this.daftarLagi) this.$emit('close')
      }
    },
    OpenManual() {
      window.open('/web/ManualBP2.docx', '_blank')
    },
    async AjukanPendaftaran(daftarLagi) {
      if (this.forms.PermohonanID) {
        var res = await this.$api.call('WEB.SavPermohonan', {
          ...this.forms,
          XmlPermohonanDet: this.datagrid,
        })
        if (res.success) {
          this.$emit('close')
        }
      } else {
        this.daftarLagi = daftarLagi
        let d = await this.$api.call('WEB.SelSyaratByParameter', {
          ParameterList: this.datagrid.map((p) => p.ParameterID).join(','),
        })
        if (d.success) {
          this.syarat.show = true
          this.bahan = d.data
        }
      }
    },
    Close() {
      this.$emit('close')
    },
  },
}
</script>
<style lang="scss" scoped>
.dvWarn {
  position: absolute;
  top: -48px;
  color: white;
  background: red;
  padding: 5px 10px;
  border-radius: 5px;
}
.v-btn {
  font-size: inherit !important;
}
.panel-pengajuan {
  background: white;
  width: 1170px;
  position: fixed;
  top: 105px;
  height: calc(100vh - 105px);
  z-index: 2;

  .form-content {
    padding: 20px 20px;
    /* width: calc(100vw - 324px);*/
    overflow: auto;
    height: calc(100vh - 150px);

    .form-info {
      font-size: small;
      color: gray;
      padding: 0 300px 20px 200px;
    }
  }
}
.is-mobile {
  .panel-pengajuan {
    top: 110px;
    height: calc(100vh - 110px);
    width: 100%;

    .form-content {
      padding: 20px 20px;
      width: 100vw;
      overflow: auto;
      height: 100%;

      .form-info {
        padding: 0;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
