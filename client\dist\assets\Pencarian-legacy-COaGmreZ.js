!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t){return function(t){if(Array.isArray(t))return r(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return r(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach(function(e){o(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function o(e,r,n){return(r=function(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,r||"default");if("object"!=t(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==t(r)?r:r+""}(r))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function o(r,n,a,o){var i=n&&n.prototype instanceof l?n:l,c=Object.create(i.prototype);return u(c,"_invoke",function(r,n,a){var o,i,u,l=0,c=a||[],f=!1,m={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,r){return o=e,i=0,u=t,m.n=r,s}};function p(r,n){for(i=r,u=n,e=0;!f&&l&&!a&&e<c.length;e++){var a,o=c[e],p=m.p,d=o[2];r>3?(a=d===n)&&(u=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=t):o[0]<=p&&((a=r<2&&p<o[1])?(i=0,m.v=n,m.n=o[1]):p<d&&(a=r<3||o[0]>n||n>d)&&(o[4]=r,o[5]=n,m.n=d,i=0))}if(a||r>1)return s;throw f=!0,n}return function(a,c,d){if(l>1)throw TypeError("Generator is already running");for(f&&1===c&&p(c,d),i=c,u=d;(e=i<2?t:u)||!f;){o||(i?i<3?(i>1&&(m.n=-1),p(i,u)):m.n=u:m.v=u);try{if(l=2,o){if(i||(a="next"),e=o[a]){if(!(e=e.call(o,u)))throw TypeError("iterator result is not an object");if(!e.done)return e;u=e.value,i<2&&(i=0)}else 1===i&&(e=o.return)&&e.call(o),i<2&&(u=TypeError("The iterator does not provide a '"+a+"' method"),i=1);o=t}else if((e=(f=m.n<0)?u:r.call(n,m))!==s)break}catch(e){o=t,i=1,u=e}finally{l=1}}return{value:e,done:f}}}(r,a,o),!0),c}var s={};function l(){}function c(){}function f(){}e=Object.getPrototypeOf;var m=[][n]?e(e([][n]())):(u(e={},n,function(){return this}),e),p=f.prototype=l.prototype=Object.create(m);function d(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,u(t,a,"GeneratorFunction")),t.prototype=Object.create(p),t}return c.prototype=f,u(p,"constructor",f),u(f,"constructor",c),c.displayName="GeneratorFunction",u(f,a,"GeneratorFunction"),u(p),u(p,a,"Generator"),u(p,n,function(){return this}),u(p,"toString",function(){return"[object Generator]"}),(i=function(){return{w:o,m:d}})()}function u(t,e,r,n){var a=Object.defineProperty;try{a({},"",{})}catch(t){a=0}u=function(t,e,r,n){function o(e,r){u(t,e,function(t){return this._invoke(e,r,t)})}e?a?a(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n}):t[e]=r:(o("next",0),o("throw",1),o("return",2))},u(t,e,r,n)}function s(t,e,r,n,a,o,i){try{var u=t[o](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,a)}function l(t){return function(){var e=this,r=arguments;return new Promise(function(n,a){var o=t.apply(e,r);function i(t){s(o,n,a,i,u,"next",t)}function u(t){s(o,n,a,i,u,"throw",t)}i(void 0)})}}System.register(["./index-legacy-BUdDePUl.js"],function(t,r){"use strict";var n,o,u,s,c,f,m,p,d,h,v,b,y,w;return{setters:[function(t){n=t.n,o=t.R,u=t.P,s=t.S,c=t._,f=t.a,m=t.b,p=t.c,d=t.d,h=t.e,v=t.f,b=t.g,y=t.h,w=t.i}],execute:function(){var r=document.createElement("style");r.textContent=".dvWarn[data-v-54e48c71]{position:absolute;top:-48px;color:#fff;background:red;padding:5px 10px;border-radius:5px}\n/*$vite$:1*/",document.head.appendChild(r);t("default",n({components:{SidePane:s,ParameterUji:u,ReportPopup:o},data:function(){return{datagrid:[],rebindSidebar:0,rebindUpload:0,dbparams:{PermohonanID:0},forms:{},deletion:{show:!1},paramUji:{show:!1},showReport:!1,tahapan:[],jenis:[{val:"A",txt:"Mutu Air & Lingkungan"},{val:"B",txt:"Bahan Bangunan"},{val:"Ba",txt:"Aspal"},{val:"T",txt:"Tanah (Geoteknik)"}],satker:[{val:"1",txt:"APBN"},{val:"2",txt:"APBD I - BMCK"},{val:"3",txt:"APBD I - NON BMCK"},{val:"4",txt:"APBD II"},{val:"5",txt:"Swasta"},{val:"6",txt:"Perorangan"},{val:"7",txt:"Lainnya"}]}},computed:{reportUrl:function(){return this.showReport?this.forms.StatusID>=3?"/reports/uji/permohonan-paid/"+this.forms.PermohonanID:"/reports/uji/permohonan/"+this.forms.PermohonanID:"/tunggu-sebentar"}},mounted:function(){this.PopulateTahapan()},methods:{ItemClick:function(t){var e=this;return l(i().m(function r(){return i().w(function(r){for(;;)switch(r.n){case 0:e.Populate(t.PermohonanID);case 1:return r.a(2)}},r)}))()},Download:function(t){var e=this;return l(i().m(function r(){return i().w(function(r){for(;;)switch(r.n){case 0:e.$api.download(e.$api.url+t,!0);case 1:return r.a(2)}},r)}))()},Populate:function(t){var e=this;return l(i().m(function r(){var n;return i().w(function(r){for(;;)switch(r.n){case 0:return e.rebindUpload++,e.dbparams={PermohonanID:t},r.n=1,e.$api.call("UJI.SelPermohonan",{PermohonanID:t});case 1:(n=r.v).data.length?e.forms=n.data[0]:e.forms={PermohonanID:0};case 2:return r.a(2)}},r)}))()},PopulateTahapan:function(){var t=this;return l(i().m(function e(){var r;return i().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("UJI.SelTahapan");case 1:r=e.v,t.tahapan=r.data;case 2:return e.a(2)}},e)}))()},Save:function(){var t=this;return l(i().m(function e(){return i().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("UJI.SavPermohonan",a(a({},t.forms),{},{XmlPermohonanDet:t.datagrid}));case 1:e.v.success&&t.rebindSidebar++;case 2:return e.a(2)}},e)}))()},CatatanKhusus:function(){var t=this;return l(i().m(function e(){return i().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("UJI_UpdCatatanKhusus",{PermohonanID:t.forms.PermohonanID,CatatanKhsusus:"Belum Dibayarkan"});case 1:t.Populate(t.forms.PermohonanID);case 2:return e.a(2)}},e)}))()},UpdBayar:function(){var t=this;return l(i().m(function e(){return i().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("UJI_UpdBayar",{PermohonanID:t.forms.PermohonanID,BayarStatus:1});case 1:e.v.success&&t.Populate(t.forms.PermohonanID);case 2:return e.a(2)}},e)}))()},UpdPengambilan:function(){return l(i().m(function t(){return i().w(function(t){for(;;)if(0===t.n)return t.a(2)},t)}))()},Delete:function(){var t=this;return l(i().m(function e(){return i().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("UJI_DelPermohonan",t.forms);case 1:e.v.success&&(t.deletion.show=!1,t.forms.PermohonanID=0,t.Populate(t.forms.PermohonanID),t.rebindSidebar++);case 2:return e.a(2)}},e)}))()},ResendNotif:function(){var t=this;return l(i().m(function e(){var r;return i().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.post("/reports/uji/resend",{PermohonanID:t.forms.PermohonanID});case 1:(r=e.v).success?t.$api.notify(r.message,"success"):t.$api.notify(r.message,"error");case 2:return e.a(2)}},e)}))()},DownloadKwitansi:function(){var t=this;return l(i().m(function e(){return i().w(function(e){for(;;)switch(e.n){case 0:window.open(t.$api.url+"/reports/uji/kwitansi/"+t.forms.PermohonanID,"_blank");case 1:return e.a(2)}},e)}))()},AddParameter:function(t){var r;(r=this.datagrid).push.apply(r,e(t)),this.paramUji.show=!1},DelParameter:function(t){this.datagrid.splice(t,1)},OpenReport:function(){var t=this;setTimeout(function(){t.showReport=!0},100)},CloseReport:function(){this.showReport=!1}}},function(){var t=this,e=t._self._c;return e("div",{staticStyle:{display:"flex"}},[e("SidePane",{attrs:{statusId:",1,2,3,4,5,6,7,8,9,",rebind:t.rebindSidebar,addButton:!1},on:{"item-click":t.ItemClick}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.PermohonanID||0===t.forms.PermohonanID,expression:"forms.PermohonanID || forms.PermohonanID === 0"}],staticClass:"right-pane",class:t.$api.isMobile()?"":"form-inline",staticStyle:{padding:"20px 20px",width:"",height:"calc(100vh - 66px)",overflow:"auto"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:2==t.forms.BayarStatus,expression:"forms.BayarStatus == 2"}],staticClass:"dvWarn"},[t._v(" "+t._s(t.forms.CatatanKhusus)+" ")]),e("div",{staticStyle:{float:"right","font-family":"raleway",background:"white","margin-top":"-20px"},attrs:{id:"dvRightBox"}},[e("div",{staticStyle:{padding:"10px 20px","text-align":"center"}},[e("div",{staticStyle:{"font-size":"xx-large"}},[t._v(t._s(t.forms.NoPengujian))]),e("div",[t._v(t._s(t.forms.ShareCode))])]),e(c,{staticStyle:{"border-top":"1px solid #e3e3e3","text-align":"center"},attrs:{text:"",small:"",color:"primary"}},[t._v(" "+t._s(t.forms.StatusName)+" ")])],1),e(f,{attrs:{type:"text",label:"Nama Pelanggan",value:t.forms.Nama,disabled:!0,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"Nama",e)}}}),e(m,{attrs:{label:"Alamat",value:t.forms.Alamat,width:"600px",disabled:!0},on:{"update:value":function(e){return t.$set(t.forms,"Alamat",e)}}}),t._v(" "),e(f,{attrs:{type:"text",label:"No. Ponsel",value:t.forms.Phone,disabled:!0,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"Phone",e)}}}),e(f,{attrs:{type:"text",label:"Email",value:t.forms.Email,disabled:!0,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"Email",e)}}}),e(p,{attrs:{items:t.jenis,label:"Nama/Jenis Contoh",value:t.forms.JenisID,disabled:!0,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"JenisID",e)}}}),e(d,{staticStyle:{"padding-top":"4px"},attrs:{label:"Tanggal Masuk",value:t.forms.TglMasuk,disabled:!0},on:{"update:value":function(e){return t.$set(t.forms,"TglMasuk",e)}}}),e(m,{attrs:{label:"Kegiatan/Paket Pekerjaan",value:t.forms.NamaKegiatan,disabled:!0,width:"600px"},on:{"update:value":function(e){return t.$set(t.forms,"NamaKegiatan",e)}}}),t._v(" "),e(h,{attrs:{label:"Sumber/SatKer"}},[e(p,{attrs:{items:t.satker,value:t.forms.SumberDana,disabled:!0,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"SumberDana",e)}}}),e(f,{directives:[{name:"show",rawName:"v-show",value:t.forms.SumberDana<"5",expression:"forms.SumberDana < '5'"}],staticStyle:{"margin-left":"5px"},attrs:{type:"text",placeholder:"Satker / Kab / Kota",value:t.forms.SatKer,disabled:!0,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"SatKer",e)}}})],1),e(h,{attrs:{label:"Surat Permohonan/Tgl."}},[e(f,{attrs:{type:"text",value:t.forms.SuratNo,placeholder:"No. Surat",width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"SuratNo",e)}}}),e(d,{staticStyle:{"margin-left":"5px"},attrs:{value:t.forms.SuratTgl,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"SuratTgl",e)}}})],1),e(h,{attrs:{label:"Surat Permohonan"}},[e(v,{key:t.rebindUpload,attrs:{value:t.forms.SuratUrl},on:{"update:value":function(e){return t.$set(t.forms,"SuratUrl",e)}},scopedSlots:t._u([{key:"default",fn:function(r){var n=r.opener,a=r.fileName;return[e(c,{directives:[{name:"show",rawName:"v-show",value:a,expression:"fileName"}],staticStyle:{"margin-right":"8px"},attrs:{small:"",text:"",outlined:""},on:{click:function(e){return t.Download(t.forms.SuratUrl)}}},[t._v(t._s(a||t.forms.SuratUrl))]),e(c,{attrs:{small:""},on:{click:n}},[e(b,[t._v("mdi-upload")])],1)]}}])})],1),e(y,{attrs:{datagrid:t.datagrid,dbref:"UJI_SelPermohonanDet",dbparams:t.dbparams,disabled:!0,columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Nama Contoh",value:"NamaContoh"},{name:"Jml",value:"JmlContoh"},{name:"Metode",value:"Metode"},{name:"Harga",value:"Harga",class:"align-right"},{name:"",value:"Delete"}]},on:{"update:datagrid":function(e){t.datagrid=e}},scopedSlots:t._u([{key:"row-NamaContoh",fn:function(r){var n=r.row;return[e(f,{attrs:{value:n.NamaContoh,disabled:!0,placeholder:"(asal/ukuran contoh)"},on:{"update:value":function(e){return t.$set(n,"NamaContoh",e)}}})]}},{key:"row-JmlContoh",fn:function(r){var n=r.row;return[e(f,{attrs:{type:"number",value:n.JmlContoh,disabled:!0,placeholder:"Jml",width:"60px"},on:{"update:value":function(e){return t.$set(n,"JmlContoh",e)}}})]}},{key:"row-Harga",fn:function(e){var r=e.row;return[t._v(" "+t._s(t._f("format")(r.Harga*r.JmlContoh))+" ")]}}])})],1),e("ReportPopup",{directives:[{name:"show",rawName:"v-show",value:t.showReport,expression:"showReport"},{name:"click-outside",rawName:"v-click-outside",value:t.CloseReport,expression:"CloseReport"}],attrs:{reportUrl:t.reportUrl}}),e("ParameterUji",{attrs:{forms:t.paramUji,jenisId:t.forms.JenisID}}),e(w,{attrs:{title:"Pembatalan",show:t.deletion.show},on:{"update:show":function(e){return t.$set(t.deletion,"show",e)}}},[e(m,{attrs:{label:"Alasan Pembatalan",value:t.forms.Keterangan,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"Keterangan",e)}}})],1)],1)},[],!1,null,"54e48c71").exports)}}})}();
