const {Telegraf, <PERSON><PERSON>} = require('telegraf')
const fs = require('fs')
const db = require('./db')
// const events = require('../api/events');


// const bot = new Telegraf('6202031893:AAHUsWd1xN5-WX-5SLm7FKf8UzyGs9344TQ')
const bot = new Telegraf('6981179311:AAE_HbuyiiJ7ZcVGqTWXZXGw1TRd9klsdxk')
const ADMIN_ID = '662216470'

const session = {
  set: (ctx, k, v) => {
    if(!session.params[ctx.chat.id]) session.params[ctx.chat.id] = {}
    if (typeof k == 'object') {
      session.params[ctx.chat.id] = {...session.params[ctx.chat.id], ...k}
    } else {
      session.params[ctx.chat.id][k] = v
    }
  },
  get:(ctx, k) => {
    if (!session.params[ctx.chat.id]) return null
    return session.params[ctx.chat.id][k]
  },
  params: {},
  isAdmin: (ctx) => {
    return ctx.chat.id == ADMIN_ID
  }
}

bot.command('start', ctx => {
  // console.log(ctx.update.message.from)
  ctx.reply('Terimakasih anda telah mendaftar di Telegram Silakon BangJali\n\nSilakan gunakan perintah /status untuk melihat status pengujian')
})

bot.command('link', ctx => {
  ctx.reply(
    "tautkan nomor hp anda dengan Silakon",
    Markup.keyboard([
      Markup.button.contactRequest("Tautkan"),
    ]),
  )
})

bot.telegram.setMyCommands([
  {
    command: 'start',
    description: 'Mendaftarkan akun anda ke Silakon',
  },
  {
    command: 'link',
    description: 'Tautkan nomor anda dengan Silakon',
  },
  {
    command: 'status',
    description: 'Cek status pengujian anda',
  },
]);

// bot.command('useronline', async ctx => {
//   // let msg = ctx.message.text
//   let reply = ''
//   let len = 0
//   for (let key in events.clients) {
//     reply += key + '\n'
//     len++;
//   }
//   bot.telegram.sendMessage(ctx.chat.id, `Jumlah user yg terkoneksi adalah ${len}:\n\n` + reply, {})
// })

// bot.command('cekrekom', async ctx => {
//   const res = await db.query('SELECT Tahun, Rekom FROM prm_rekom WHERE IsClosed = 0')
//   if (res.length > 0) {
//     bot.telegram.sendMessage(ctx.chat.id, `Rekom yang berjalan untuk tahun ${res[0].Tahun} adalah ${res[0].Rekom}`, {})
//   } else {
//     bot.telegram.sendMessage(ctx.chat.id, `Tidak ada rekom yang sedang berjalan`, {})
//   }
// })

// bot.command('logs', async ctx => {
//   let msg = ctx.message.text
//   if (ctx.chat.id == ADMIN_ID) {
//     const logs = fs.readFileSync('/app/tmp/backend.log', 'utf-8')
//     bot.telegram.sendMessage(ctx.chat.id, logs, {})
//   } else {
//     bot.telegram.sendMessage(ctx.chat.id, 'Perintah ini hanya untuk admin', {})
//   }
// })

const ISSUES = {
  'status': {
    prompt:(ctx) => {
      let msg = ctx.message.text
      let params = msg.match(/(\d{1,2})/)
      if(params) {
        const rekom = params[1]
        ISSUES['buka_rekom'].callback(ctx, rekom)
      } else {
        ctx.reply(`Masukkan angka REKOM:`)
      }
    },
    callback: async (ctx, rekom) => {
      await db.query(`UPDATE prm_rekom SET Rekom = 'REKOM ${rekom}' WHERE IsClosed = 0 AND Tahun = YEAR(NOW())`)
      ctx.reply(`REKOM ${rekom} telah dibuka`)
    }
  }
}
// register ISSUES
// for (let k in ISSUES) {
//   ISSUES[k].callback()
// }

bot.command('bukarekom', async ctx => {
  let msg = ctx.message.text
  if (ctx.chat.id == ADMIN_ID) {
    if (!ctx.app) ctx.app = {}
    session.set(ctx, 'cmd', 'buka_rekom')
    let rekom = msg.match(/bukarekom (\d{1,2})/)
    if (rekom) {
      ISSUES['buka_rekom'].callback(ctx, rekom[1])
    } else {
      ctx.reply(`Masukkan angka REKOM:`)
    }
  } else {
    ctx.reply('Perintah ini hanya untuk admin')
  }
})

bot.command('cek', ctx => {
  if (!ctx.app) ctx.app = {}
  session.set(ctx, 'cmd', 'cek_nik')
  let msg = ctx.message.text
  let params = msg.match(/cek (\d{16})/)
  if (params) {
    const NIK = params[1]
    ISSUES['cek_nik'].callback(ctx, NIK)
  } else {
    ctx.reply(`masukkan NIK:`)
  }
})

bot.command('onetime', (ctx) =>
  ctx.reply(
    "Keyboard wrap",
    Markup.inlineKeyboard([
      Markup.button.callback("Batal", "cancel"),
      Markup.button.callback("Ya", "ok"),
    ]),
  )
)

bot.action('cancel', (ctx) => {
  ctx.editMessageReplyMarkup();
  ctx.reply('Canceled')
})

bot.action('ok', (ctx) => {
  ctx.editMessageReplyMarkup();
  ctx.reply('[Not Implemented]')
})

bot.on('text', (ctx) => {
  const cmd = session.get(ctx, 'cmd')
  if(!cmd) ctx.reply('Perintah tidak dikenali')
  else if (ISSUES[cmd]) ISSUES[cmd].prompt(ctx) 
})

bot.launch()
console.log('Telegram Bot Active')

// Enable graceful stop
process.once('SIGINT', () => bot.stop('SIGINT'))
process.once('SIGTERM', () => bot.stop('SIGTERM'))

module.exports = {
  send: (id, text) => {
    if (text)
      bot.telegram.sendMessage(id, text, {})
  },
  sendAdmin: (text) => {
    if (text)
      bot.telegram.sendMessage(ADMIN_ID, text, {})
  }
}
