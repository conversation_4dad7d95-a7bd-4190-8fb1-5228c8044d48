!function(){function t(t,e){var a="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!a){if(Array.isArray(t)||(a=function(t,e){if(t){if("string"==typeof t)return r(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?r(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){a&&(t=a);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,u=!1;return{s:function(){a=a.call(t)},n:function(){var t=a.next();return s=t.done,t},e:function(t){u=!0,i=t},f:function(){try{s||null==a.return||a.return()}finally{if(u)throw i}}}}function r(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,a=Array(r);e<r;e++)a[e]=t[e];return a}function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,r,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function s(e,n,o,i){var s=n&&n.prototype instanceof c?n:c,l=Object.create(s.prototype);return a(l,"_invoke",function(e,a,n){var o,i,s,c=0,l=n||[],f=!1,m={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(r,e){return o=r,i=0,s=t,m.n=e,u}};function p(e,a){for(i=e,s=a,r=0;!f&&c&&!n&&r<l.length;r++){var n,o=l[r],p=m.p,d=o[2];e>3?(n=d===a)&&(s=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=t):o[0]<=p&&((n=e<2&&p<o[1])?(i=0,m.v=a,m.n=o[1]):p<d&&(n=e<3||o[0]>a||a>d)&&(o[4]=e,o[5]=a,m.n=d,i=0))}if(n||e>1)return u;throw f=!0,a}return function(n,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),i=l,s=d;(r=i<2?t:s)||!f;){o||(i?i<3?(i>1&&(m.n=-1),p(i,s)):m.n=s:m.v=s);try{if(c=2,o){if(i||(n="next"),r=o[n]){if(!(r=r.call(o,s)))throw TypeError("iterator result is not an object");if(!r.done)return r;s=r.value,i<2&&(i=0)}else 1===i&&(r=o.return)&&r.call(o),i<2&&(s=TypeError("The iterator does not provide a '"+n+"' method"),i=1);o=t}else if((r=(f=m.n<0)?s:e.call(a,m))!==u)break}catch(r){o=t,i=1,s=r}finally{c=1}}return{value:r,done:f}}}(e,o,i),!0),l}var u={};function c(){}function l(){}function f(){}r=Object.getPrototypeOf;var m=[][o]?r(r([][o]())):(a(r={},o,function(){return this}),r),p=f.prototype=c.prototype=Object.create(m);function d(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,a(t,i,"GeneratorFunction")),t.prototype=Object.create(p),t}return l.prototype=f,a(p,"constructor",f),a(f,"constructor",l),l.displayName="GeneratorFunction",a(f,i,"GeneratorFunction"),a(p),a(p,i,"Generator"),a(p,o,function(){return this}),a(p,"toString",function(){return"[object Generator]"}),(e=function(){return{w:s,m:d}})()}function a(t,r,e,n){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}a=function(t,r,e,n){function i(r,e){a(t,r,function(t){return this._invoke(r,e,t)})}r?o?o(t,r,{value:e,enumerable:!n,configurable:!n,writable:!n}):t[r]=e:(i("next",0),i("throw",1),i("return",2))},a(t,r,e,n)}function n(t,r,e,a,n,o,i){try{var s=t[o](i),u=s.value}catch(t){return void e(t)}s.done?r(u):Promise.resolve(u).then(a,n)}function o(t){return function(){var r=this,e=arguments;return new Promise(function(a,o){var i=t.apply(r,e);function s(t){n(i,a,o,s,u,"next",t)}function u(t){n(i,a,o,s,u,"throw",t)}s(void 0)})}}System.register(["./index-legacy-BUdDePUl.js"],function(r,a){"use strict";var n,i,s,u,c,l,f;return{setters:[function(t){n=t.n,i=t.S,s=t.k,u=t._,c=t.h,l=t.i,f=t.b}],execute:function(){var a=document.createElement("style");a.textContent=".table-cekprogress table{min-width:750px}.table-cekprogress table .is-masalah{padding-left:10px;position:absolute;margin-top:-9px;max-width:400px;overflow:hidden;text-overflow:ellipsis}.table-cekprogress table .is-masalah.is-done{color:gray}\n/*$vite$:1*/",document.head.appendChild(a);r("default",n({components:{SidePane:i},data:function(){return{datagrid:[],dbparams:{PermohonanID:0},lembarKerja:[],forms:{PermohonanID:0,ParameterID:0,Masalah:""},rebind:1,masalah:{show:!1}}},computed:{showSubmitButton:function(){var t=this.datagrid.filter(function(t){return!t.HasilStatus}),r=this.lembarKerja.filter(function(t){return!t.Alasan&&!t.ApprovedBy});return 0==t.length&&r.length>0}},methods:{ItemClick:function(t){var r=this;return o(e().m(function a(){return e().w(function(e){for(;;)switch(e.n){case 0:r.Populate(t.PermohonanID);case 1:return e.a(2)}},a)}))()},Populate:function(t){var r=this;return o(e().m(function a(){var n;return e().w(function(e){for(;;)switch(e.n){case 0:return r.dbparams={PermohonanID:t},e.n=1,r.$api.call("UJI.SelPermohonan",{PermohonanID:t});case 1:(n=e.v).data.length?r.forms=n.data[0]:r.forms={},r.PopulateLK(t);case 2:return e.a(2)}},a)}))()},Open:function(t){window.open(this.$api.url+t,"_blank")},OpenPDF:function(t){window.open(this.$api.url+t.replace(/\.\w{3,4}$/,"pdf"),"_blank")},PopulateLK:function(t){var r=this;return o(e().m(function a(){var n;return e().w(function(e){for(;;)switch(e.n){case 0:return r.lembarKerja=[],e.n=1,r.$api.call("UJI.SelLembarKerja",{PermohonanID:t});case 1:n=e.v,r.lembarKerja=n.data;case 2:return e.a(2)}},a)}))()},UpdMulaiPengujian:function(){var t=this;return o(e().m(function r(){return e().w(function(r){for(;;)switch(r.n){case 0:if(confirm("Mulai Pengujian?")){r.n=1;break}return r.a(2);case 1:return r.n=2,t.$api.call("UJI_UpdMulaiPerngujian",{PermohonanID:t.forms.PermohonanID,MulaiStatus:1});case 2:r.v.success&&t.Populate(t.forms.PermohonanID);case 3:return r.a(2)}},r)}))()},UpdHasil:function(t,r,a){var n=this;return o(e().m(function o(){return e().w(function(e){for(;;)switch(e.n){case 0:if(!(n.datagrid.filter(function(t){return!t.HasilStatus}).length<=1)){e.n=1;break}if(confirm("Pengujian Telah Selesai?")){e.n=1;break}return e.a(2);case 1:return e.n=2,n.$api.call("UJI_UpdHasil",{PermohonanID:t,ParameterID:r,HasilStatus:a});case 2:e.v.success&&n.Populate(n.forms.PermohonanID);case 3:return e.a(2)}},o)}))()},ShowMasalah:function(t,r){var a=this;return o(e().m(function n(){return e().w(function(e){for(;;)switch(e.n){case 0:a.masalah.show=!0,a.forms.ParameterID=t,a.forms.ParameterID=r;case 1:return e.a(2)}},n)}))()},InsMasalah:function(){var t=this;return o(e().m(function r(){return e().w(function(r){for(;;)switch(r.n){case 0:return r.n=1,t.$api.call("UJI_InsMasalah",t.forms);case 1:t.rebind++,t.masalah.show=!1;case 2:return r.a(2)}},r)}))()},Resolve:function(t){var r=this;return o(e().m(function a(){return e().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,r.$api.call("UJI_SavResolveMasalah",{MasalahID:t});case 1:r.rebind++;case 2:return e.a(2)}},a)}))()},Reject:function(t){var r=this;return o(e().m(function a(){var n;return e().w(function(e){for(;;)switch(e.n){case 0:if(!(n=prompt("Alasan?"))){e.n=2;break}return e.n=1,r.$api.call("UJI_SavLembarKerjaRejection",{LembarKerjaID:t,Alasan:n});case 1:e.v.success&&(r.rawUrl="",r.PopulateLK(r.forms.PermohonanID),r.rebind++);case 2:return e.a(2)}},a)}))()},Approve:function(t){var r=this;return o(e().m(function a(){return e().w(function(e){for(;;)switch(e.n){case 0:if(!confirm("Setujui?")){e.n=2;break}return e.n=1,r.$api.call("UJI_SavLembarKerjaApproval",{LembarKerjaID:t,FilePath:r.rawUrlOri});case 1:e.v.success&&(r.rawUrl="",r.PopulateLK(r.forms.PermohonanID),r.rebind++);case 2:return e.a(2)}},a)}))()},fileUploaded:function(r,a){var n=this;return o(e().m(function r(){var o,i,s;return e().w(function(r){for(;;)switch(r.n){case 0:return n.$api.notify("Upload Sukses"),r.n=1,n.$api.call("UJI_SavLembarKerja",{PermohonanID:n.forms.PermohonanID,XmlRawUrl:a});case 1:if(!r.v.success){r.n=3;break}return r.n=2,n.PopulateLK(n.forms.PermohonanID);case 2:o=t(n.lembarKerja);try{for(o.s();!(i=o.n()).done;)s=i.value,n.$api.get("/reports/uji/convert-lk/"+s.LembarKerjaID)}catch(e){o.e(e)}finally{o.f()}case 3:return r.a(2)}},r)}))()}}},function(){var t=this,r=t._self._c;return r("div",{staticStyle:{display:"flex"}},[r("SidePane",{attrs:{statusId:",7,8,9,"},on:{"item-click":t.ItemClick}}),r("div",{directives:[{name:"show",rawName:"v-show",value:t.forms.Nama,expression:"forms.Nama"}],staticStyle:{overflow:"auto",height:"calc(100vh - 66px)"}},[r("div",{staticStyle:{background:"#039ae4",color:"white",padding:"15px 15px 10px 15px",display:"flex"}},[r("div",{staticStyle:{display:"flex",width:"calc(100% - 200px)"}},[r("div",{staticStyle:{"max-width":"450px",overflow:"hidden","text-wrap":"nowrap","text-overflow":"ellipsis"}},[t._v(" "+t._s(t.forms.Nama)+" ")]),r("div",{staticStyle:{background:"white",color:"#039ae4","margin-left":"10px",padding:"5px 8px","border-radius":"5px","font-size":"small",position:"relative",top:"-3px"}},[t._v(" "+t._s(t.forms.NoPengujian)+" ")])]),r(s),5==t.forms.StatusID?r(u,{staticStyle:{color:"white !important",position:"relative",top:"-2px"},attrs:{small:"",disabled:""}},[t._v(" DALAM PROSES ")]):t._e(),6==t.forms.StatusID?r(u,{staticStyle:{color:"white !important",position:"relative",top:"-2px"},attrs:{small:"",disabled:""}},[t._v(" SELESAI ")]):t._e()],1),r(c,{staticClass:"table-cekprogress",attrs:{datagrid:t.datagrid,dbref:"UJI_SelCekProgress",doRebind:t.rebind,dbparams:t.dbparams,disabled:!0,columns:[{name:"Parameter Uji",value:"NamaParameter"},{name:"Jml",value:"JmlContoh"},{name:"Keterangan",value:"Keterangan"},{name:"Waktu",value:"Waktu"}]},on:{"update:datagrid":function(r){t.datagrid=r}},scopedSlots:t._u([{key:"row-NamaParameter",fn:function(e){var a=e.row;return[r("div",{class:{"is-masalah":2==a.Ordr,"is-done":a.HasilStatus}},[r("div",[t._v(t._s(a.NamaParameter))]),r("div",[t._v(t._s(1==a.Ordr?a.Metode:""))])])]}},{key:"row-Keterangan",fn:function(r){var e=r.row;return[t._v(" "+t._s(e.Keterangan)+" ")]}},{key:"row-Waktu",fn:function(r){var e=r.row;return[t._v(" "+t._s(e.Waktu)+" Hari ")]}}])}),t._l(t.lembarKerja,function(e,a){return r("div",{key:a,staticStyle:{"font-size":"12px",background:"#f3f3f3",display:"flex","margin-bottom":"1px",padding:"8px"}},[r(u,{attrs:{small:"",text:"",color:"primary"},on:{click:function(r){return t.Open(e.LkUrl,e.LembarKerjaID)}}},[t._v(" "+t._s(e.Nama)+" ")]),r(s),e.ApprovedBy?r(u,{staticStyle:{margin:"4px"},attrs:{"x-small":"",text:"",outlined:""}},[t._v(" SUDAH DISETUJUI ")]):t._e(),e.Alasan?r(u,{directives:[{name:"tooltip",rawName:"v-tooltip",value:e.Alasan,expression:"lk.Alasan"}],staticStyle:{margin:"4px"},attrs:{"x-small":"",text:"",outlined:"",color:"error"}},[t._v(" DITOLAK ")]):t._e(),e.Alasan?t._e():r(u,{staticStyle:{margin:"4px"},attrs:{"x-small":"",color:"error"},on:{click:function(r){return t.Reject(e.LembarKerjaID)}}},[t._v(" TOLAK / REVISI ")])],1)})],2),r(l,{attrs:{title:"Tambah Keterangan Masalah",show:t.masalah.show},on:{"update:show":function(r){return t.$set(t.masalah,"show",r)},submit:t.InsMasalah}},[r(f,{staticStyle:{height:"100px"},attrs:{value:t.forms.Masalah,placeholder:"Keterangan Masalah",width:"350px"},on:{"update:value":function(r){return t.$set(t.forms,"Masalah",r)}}})],1)],1)},[],!1,null,null).exports)}}})}();
