<template>
  <Modal title="Detail <PERSON>ji<PERSON>" :show.sync="data.show">
    <Grid
      :datagrid.sync="datagrid"
      dbref="UJI.PermohonanDet"
      :dbparams="dbparams"
      :disabled="true"
      :doRebind="rebind"
      :columns="[
        {
          name: 'Parameter Uji',
          value: 'Nam<PERSON><PERSON><PERSON><PERSON>',
        },
        {
          name: '<PERSON><PERSON>',
          value: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        },
        {
          name: '<PERSON><PERSON>',
          value: 'Metode',
        },
        {
          name: '<PERSON><PERSON><PERSON>',
          value: '<PERSON><PERSON><PERSON>',
        },
        {
          name: 'Status',
          value: '<PERSON>Name',
        },
      ]"
    >
    </Grid>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    datagrid: [],
    rebind: 0,
    dbparams: {
      PermohonanID: 0,
    },
  }),
  props: {
    data: Object,
  },
  watch: {
    'data.PermohonanID'(val) {
      this.dbparams.PermohonanID = val
      this.rebind++
    },
  },
}
</script>
