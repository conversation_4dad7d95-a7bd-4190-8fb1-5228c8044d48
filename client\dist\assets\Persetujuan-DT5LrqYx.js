import{n as s,a as n,l,_ as r,g as o}from"./index-DYIZrBBo.js";const d={data:()=>({keyword:""}),props:{statusId:String,rebind:Number,addButton:Boolean},computed:{dbparams(){return{IsApproved:0,Keyword:this.keyword||""}}},methods:{ItemClick(i){this.$emit("item-click",i)},DaftarBaru(){this.$emit("item-click",{PengujianID:0})}}};var c=function(){var t=this,a=t._self._c;return a("div",{staticClass:"sidepane"},[a("div",{staticStyle:{padding:"10px",display:"flex"}},[a(n,{staticClass:"searchbar",attrs:{type:"text",value:t.keyword,placeholder:"Cari ..",width:"270px",rightIcon:"mdi-magnify"},on:{"update:value":function(e){t.keyword=e}}})],1),a("div",{staticStyle:{height:"calc(100% - 47px)"}},[a(l,{attrs:{dbref:"UJI_SelTandaTanganList",dbparams:t.dbparams,height:t.addButton?"calc(100% - 60px)":"100%",rebind:t.rebind,selectOnLoad:!0},on:{itemClick:t.ItemClick},scopedSlots:t._u([{key:"default",fn:function({row:e}){return[a("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"0 10px"}},[a("div",{staticStyle:{color:"gray",float:"right"}},[t._v(" "+t._s(t._f("format")(e.TglMasuk))+" ")]),a("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(e.NamaPelanggan)+" ")]),a("div",{staticStyle:{color:"gray",float:"right"}},[t._v(" "+t._s(e.StatusID>=8&&e.BayarStatus!=1?"BELUM BAYAR":e.StatusName)+" ")]),a("div",{staticStyle:{color:"gray",display:"flex"}},[a("span",{staticStyle:{"font-size":"10px",padding:"3px 5px 0 5px",background:"#ddd","border-radius":"5px",color:"#333","margin-right":"5px"}},[t._v(" "+t._s(e.NoPengujian)+" ")]),a("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis",width:"160px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(e.NamaLK||"-")+" ")])])])]}}])}),t.addButton?a("div",{staticStyle:{padding:"10px","border-top":"1px solid #ddd"}},[a(r,{staticStyle:{width:"calc(100% - 10px)"},attrs:{outlined:"",color:"primary"},on:{click:t.DaftarBaru}},[a(o,{attrs:{left:""}},[t._v("mdi-plus")]),t._v(" DAFTAR BARU ")],1)],1):t._e()],1)])},p=[],_=s(d,c,p,!1,null,null);const f=_.exports,m={components:{SidePane:f},data:()=>({rawUrlOri:"",rawUrl:"",forms:{},passphrase:"",showPassphrase:!1,loading:!1}),methods:{ShowSertifikat(i){this.forms=i,this.rawUrlOri=i.RawUrl,this.rawUrl=this.$api.url+i.RawUrl.replace(/(xlsx|docx)$/,"pdf")},async Sign(){confirm("Setujui?")&&this.$api.call("UJI_SavSertifikatApproval",{PermohonanID:this.forms.PermohonanID,FilePath:this.rawUrlOri}).success&&(this.rawUrl="")}}};var h=function(){var t=this,a=t._self._c;return a("div",{staticStyle:{display:"flex"}},[a("SidePane",{on:{"item-click":t.ShowSertifikat}}),t.rawUrl?a("div",{staticClass:"right-pane"},[a("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.rawUrl,frameborder:"0"}}),a("div",{staticStyle:{position:"fixed",bottom:"20px",right:"40px",display:"flex"}},[a(r,{directives:[{name:"show",rawName:"v-show",value:!t.loading,expression:"!loading"}],staticClass:"close-right-pane",staticStyle:{"margin-right":"8px"},on:{click:function(e){t.rawUrl=""}}},[t._v(" BATAL ")]),a(r,{attrs:{color:"primary",disabled:t.loading},on:{click:t.Sign}},[t._v(" SETUJUI ")])],1)]):t._e()],1)},u=[],g=s(m,h,u,!1,null,null);const x=g.exports;export{x as default};
