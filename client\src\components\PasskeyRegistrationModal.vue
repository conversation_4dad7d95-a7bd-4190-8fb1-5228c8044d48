<script>
import { startRegistration } from '@simplewebauthn/browser'
</script>

<template>
  <Modal
    :show="show"
    title="Daftarkan Login Biometrik"
    :submit-text="loading ? 'Mendaftarkan...' : 'Daftarkan'"
    :cancel-text="'<PERSON><PERSON>'"
    :loading="loading"
    @submit="registerPasskey"
    @cancel="skipRegistration"
  >
    <div>
      <div class="passkey-registration-content">
        <div class="passkey-icon">
          <v-icon size="64" color="primary">mdi-fingerprint</v-icon>
        </div>
        <p>
          Daftarkan Login Biometrik pada akun anda<br />
          untuk pengalaman login lebih cepat dan aman.
        </p>
      </div>
      <div class="note">
        <v-icon size="16" class="note-icon">mdi-information</v-icon>
        <small>
          Hanya daftarkan jika laptop/komputer ini DIPAKAI SENDIRI,<br />
          BUKAN DIPAKAI OLEH UMUM
        </small>
      </div>
      <div v-if="error" class="error-message">
        <v-icon color="error">mdi-alert-circle</v-icon>
        {{ error }}
      </div>
    </div>
  </Modal>
</template>

<script>
import Modal from './Modal.vue'
import { startRegistration } from '@simplewebauthn/browser'

export default {
  components: {
    Modal,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: Number,
      required: true,
    },
    username: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      error: null,
    }
  },
  methods: {
    async registerPasskey() {
      this.loading = true
      this.error = null

      try {
        // Get registration options from server
        const optionsResponse = await this.$api.post('/api/passkey/register')

        if (!optionsResponse.success) {
          this.$api.notify(
            optionsResponse.message || 'Gagal memulai registrasi',
            'error',
          )
          return
        }

        // Start registration with the browser
        let credential = null
        const isSupported =
          await PublicKeyCredential.isConditionalMediationAvailable?.()
        if (!isSupported) {
          credential = await startRegistration(optionsResponse.data)
        } else {
          credential = await this.customStartRegistration(
            optionsResponse.data,
            {
              mediation: 'conditional', // 👈 This triggers browser autofill-style passkey UI
            },
          )
        }

        // Send credential to server for verification
        const verificationResponse = await this.$api.post(
          '/api/passkey/registered',
          {
            credential,
          },
        )

        if (verificationResponse.success) {
          let basics = localStorage.getItem('basics')
          basics = basics ? JSON.parse(basics) : {}
          basics.hasBiometric = true
          localStorage.setItem('basics', JSON.stringify(basics))

          this.$api.notify('Biometrik berhasil didaftarkan!', 'success')
          this.xshow = false
          this.$emit('registered')
          this.$emit('update:show', false)
        } else {
          this.$api.notify(
            verificationResponse.message || 'Gagal mendaftarkan biometrik',
            'error',
          )
        }
      } catch (error) {
        console.error('FIDO2 registration error:', error)
        if (error.name === 'NotAllowedError') {
          this.$api.notify(
            'Registrasi dibatalkan atau tidak diizinkan',
            'error',
          )
        } else if (error.name === 'NotSupportedError') {
          this.$api.notify(
            'Perangkat tidak mendukung autentikasi biometrik',
            'error',
          )
        } else {
          this.$api.notify(
            'Gagal mendaftarkan biometrik: ' + error.message,
            'error',
          )
        }
      } finally {
        this.loading = false
      }
    },

    skipRegistration() {
      this.$emit('skipped')
      this.$emit('update:show', false)
    },

    /**
     * Custom startRegistration function that supports mediation options
     * Compatible with optionsResponse.data structure
     */
    async customStartRegistration(options, mediationOptions = {}) {
      // Convert the options to the format expected by navigator.credentials.create
      const publicKeyCredentialCreationOptions = {
        challenge: new Uint8Array(options.challenge),
        rp: options.rp,
        user: {
          id: new Uint8Array(options.user.id),
          name: options.user.name,
          displayName: options.user.displayName,
        },
        pubKeyCredParams: options.pubKeyCredParams,
        authenticatorSelection: options.authenticatorSelection,
        timeout: options.timeout,
        attestation: options.attestation,
        excludeCredentials:
          options.excludeCredentials?.map((cred) => ({
            ...cred,
            id: new Uint8Array(cred.id),
          })) || [],
      }

      // Create the credential with mediation support
      const credential = await navigator.credentials.create({
        publicKey: publicKeyCredentialCreationOptions,
        ...mediationOptions,
      })

      if (!credential) {
        throw new Error('Failed to create credential')
      }

      // Convert the credential response to the format expected by the server
      return {
        id: credential.id,
        rawId: Array.from(new Uint8Array(credential.rawId)),
        response: {
          attestationObject: Array.from(
            new Uint8Array(credential.response.attestationObject),
          ),
          clientDataJSON: Array.from(
            new Uint8Array(credential.response.clientDataJSON),
          ),
        },
        type: credential.type,
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.passkey-registration-content {
  padding: 20px 0;
  display: flex;

  .passkey-icon {
    margin-bottom: 20px;
  }

  h3 {
    color: #333;
    margin-bottom: 16px;
    font-weight: 500;
  }

  p {
    color: #666;
    margin-bottom: 24px;
    line-height: 1.5;
    padding: 8px 20px;
  }

  .benefits {
    text-align: left;
    margin-bottom: 24px;

    .benefit-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .benefit-icon {
        margin-right: 12px;
        flex-shrink: 0;
      }

      span {
        color: #555;
        font-size: 14px;
      }
    }
  }

  .error-message {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #d32f2f;
    background-color: #ffebee;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 16px;
    font-size: 14px;

    .v-icon {
      margin-right: 8px;
    }
  }
}
.note {
  display: flex;
  align-items: flex-start;
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  color: #666;
  text-align: left;

  .note-icon {
    margin-right: 8px;
    margin-top: 2px;
    flex-shrink: 0;
  }
}
</style>
