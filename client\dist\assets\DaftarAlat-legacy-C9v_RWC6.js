!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,i,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function l(e,n,a,o){var l=n&&n.prototype instanceof c?n:c,u=Object.create(l.prototype);return r(u,"_invoke",function(e,r,n){var a,o,l,c=0,u=n||[],d=!1,p={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,r){return a=e,o=0,l=t,p.n=r,s}};function f(e,r){for(o=e,l=r,i=0;!d&&c&&!n&&i<u.length;i++){var n,a=u[i],f=p.p,m=a[2];e>3?(n=m===r)&&(l=a[(o=a[4])?5:(o=3,3)],a[4]=a[5]=t):a[0]<=f&&((n=e<2&&f<a[1])?(o=0,p.v=r,p.n=a[1]):f<m&&(n=e<3||a[0]>r||r>m)&&(a[4]=e,a[5]=r,p.n=m,o=0))}if(n||e>1)return s;throw d=!0,r}return function(n,u,m){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&f(u,m),o=u,l=m;(i=o<2?t:l)||!d;){a||(o?o<3?(o>1&&(p.n=-1),f(o,l)):p.n=l:p.v=l);try{if(c=2,a){if(o||(n="next"),i=a[n]){if(!(i=i.call(a,l)))throw TypeError("iterator result is not an object");if(!i.done)return i;l=i.value,o<2&&(o=0)}else 1===o&&(i=a.return)&&i.call(a),o<2&&(l=TypeError("The iterator does not provide a '"+n+"' method"),o=1);a=t}else if((i=(d=p.n<0)?l:e.call(r,p))!==s)break}catch(i){a=t,o=1,l=i}finally{c=1}}return{value:i,done:d}}}(e,a,o),!0),u}var s={};function c(){}function u(){}function d(){}i=Object.getPrototypeOf;var p=[][a]?i(i([][a]())):(r(i={},a,function(){return this}),i),f=d.prototype=c.prototype=Object.create(p);function m(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,r(t,o,"GeneratorFunction")),t.prototype=Object.create(f),t}return u.prototype=d,r(f,"constructor",d),r(d,"constructor",u),u.displayName="GeneratorFunction",r(d,o,"GeneratorFunction"),r(f),r(f,o,"Generator"),r(f,a,function(){return this}),r(f,"toString",function(){return"[object Generator]"}),(e=function(){return{w:l,m:m}})()}function r(t,e,i,n){var a=Object.defineProperty;try{a({},"",{})}catch(t){a=0}r=function(t,e,i,n){function o(e,i){r(t,e,function(t){return this._invoke(e,i,t)})}e?a?a(t,e,{value:i,enumerable:!n,configurable:!n,writable:!n}):t[e]=i:(o("next",0),o("throw",1),o("return",2))},r(t,e,i,n)}function i(t,e,r,i,n,a,o){try{var l=t[a](o),s=l.value}catch(t){return void r(t)}l.done?e(s):Promise.resolve(s).then(i,n)}function n(t){return function(){var e=this,r=arguments;return new Promise(function(n,a){var o=t.apply(e,r);function l(t){i(o,n,a,l,s,"next",t)}function s(t){i(o,n,a,l,s,"throw",t)}l(void 0)})}}function a(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(Object(r),!0).forEach(function(e){l(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l(e,r,i){return(r=function(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,r||"default");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==t(r)?r:r+""}(r))in e?Object.defineProperty(e,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[r]=i,e}System.register(["./index-legacy-BUdDePUl.js","./AlatDet-legacy-Dv-MfQFx.js"],function(t,r){"use strict";var i,a,l,s,c,u,d,p,f,m,h,b,v,y;return{setters:[function(t){i=t.n,a=t.a,l=t.l,s=t._,c=t.g,u=t.m,d=t.o,p=t.V,f=t.p,m=t.f,h=t.c,b=t.h,v=t.k},function(t){y=t.A}],execute:function(){var r=document.createElement("style");r.textContent=".sidepane{padding:0!important;width:300px;border-right:1px solid #ddd;height:calc(100vh - 64px);overflow:hidden}.sidepane .searchbar{margin-bottom:0;width:100%}.sidepane .searchbar input{background:transparent!important;border-bottom:0!important}.alat-img{min-width:220px;height:220px;padding-top:20px;margin-bottom:20px;justify-items:center}.alat-img .imgbox{width:220px!important;height:210px!important}.table-cekprogress table{min-width:750px}.table-cekprogress table .is-masalah{padding-left:10px;position:absolute;margin-top:-9px;max-width:400px;overflow:hidden;text-overflow:ellipsis}.table-cekprogress table .is-masalah.is-done{color:gray}.is-mobile .--input,.is-mobile .ui-dropdown--button{width:100%!important}.is-mobile .alat-img{padding:0;width:100vw;height:80vw}.is-mobile .alat-img .imgbox{width:100vw!important;height:80vw!important}\n/*$vite$:1*/",document.head.appendChild(r);var w={data:function(){return{keyword:""}},props:{statusId:String,rebind:Number,addButton:Boolean,filters:Object},computed:{dbparams:function(){return{Keyword:this.keyword||""}}},methods:{ItemClick:function(t){this.$emit("item-click",t)},DaftarBaru:function(){this.$emit("item-click",{AlatId:"-"})}}},g=i(w,function(){var t=this,e=t._self._c;return e("div",{staticClass:"sidepane"},[e("div",{staticStyle:{padding:"10px",display:"flex"}},[e(a,{staticClass:"searchbar",attrs:{type:"text",value:t.keyword,placeholder:"Cari ..",width:"100%",rightIcon:"mdi-magnify"},on:{"update:value":function(e){t.keyword=e}}})],1),e("div",{staticStyle:{height:"calc(100% - 47px)"}},[e(l,{attrs:{dbref:"PML.SelAlatList",dbparams:t.dbparams,height:t.addButton?"calc(100% - 60px)":"100%",rebind:t.rebind},on:{itemClick:t.ItemClick},scopedSlots:t._u([{key:"default",fn:function(r){var i=r.row;return[e("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"5px 10px"}},[e("div",{staticStyle:{color:"gray",float:"right"}},[t._v(" "+t._s(i.Tahun)+" ")]),e("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(i.NamaAlat)+" ")]),e("div",{staticStyle:{color:"gray",display:"flex"}},[e("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis",width:"160px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(i.Merk)+" / "+t._s(i.Tipe)+" ")])])])]}}])}),e("div",{staticStyle:{padding:"10px","border-top":"1px solid #ddd"}},[e(s,{staticStyle:{width:"calc(100% - 10px)"},attrs:{outlined:"",color:"primary"},on:{click:t.DaftarBaru}},[e(c,{attrs:{left:""}},[t._v("mdi-plus")]),t._v(" TAMBAH ALAT ")],1)],1)],1)])},[],!1,null,null).exports,x={components:{VueQrcode:p,SidePane:g,AlatDet:y},data:function(){return{datagrid:[],dbparams:{AlatId:""},lembarKerja:[],loading:!1,showDet:!1,forms:{},rebind:1,rebindSidebar:1,masalah:{show:!1}}},watch:{showDet:function(t){t||this.rebind++}},computed:o(o({},d({user:"getUser"})),{},{showSubmitButton:function(){return this.lembarKerja.filter(function(t){return!t.Alasan&&!t.ApprovedBy}).length>0},qrUrl:function(){return window.location.origin+"/daftar-alat/"+this.forms.AlatId},disabled:function(){return 12==this.user.RolePositionID&&this.user.UserID!=this.forms.PIC}}),methods:o(o({},u(["setPageFocused"])),{},{ItemClick:function(t){var r=this;return n(e().m(function i(){return e().w(function(e){for(;;)switch(e.n){case 0:r.setPageFocused(!0),r.Populate(r.$api.toUUID(t.AlatId));case 1:return e.a(2)}},i)}))()},Populate:function(t){var r=this;return n(e().m(function i(){var n;return e().w(function(e){for(;;)switch(e.n){case 0:return r.dbparams={AlatId:t},e.n=1,r.$api.call("PML.SelAlat",{AlatId:t});case 1:(n=e.v).data.length?r.forms=n.data[0]:r.forms={};case 2:return e.a(2)}},i)}))()},Save:function(){var t=this;return n(e().m(function r(){var i;return e().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,t.$api.call("PML.SavAlat",t.forms);case 1:(i=e.v).success&&(t.Populate(i.data[0].AlatId),t.rebindSidebar++);case 2:return e.a(2)}},r)}))()},Delete:function(){var t=this;return n(e().m(function r(){return e().w(function(e){for(;;)switch(e.n){case 0:if(confirm("Yakin menghapus alat ini?")){e.n=1;break}return e.a(2);case 1:return e.n=2,t.$api.call("PML.DelAlat",t.forms);case 2:e.v.success&&(t.Populate(0),t.rebindSidebar++);case 3:return e.a(2)}},r)}))()},DelActivity:function(t){var r=this;return n(e().m(function i(){return e().w(function(e){for(;;)switch(e.n){case 0:if(confirm("Yakin menghapus baris ini?")){e.n=1;break}return e.a(2);case 1:return e.n=2,r.$api.call("PML.DelAlatActivity",{AlatActivityId:t});case 2:return e.a(2)}},i)}))()},PrintQR:function(){var t=document.getElementById("qralat").src,e=window.open("about:blank","_new");e.document.open(),e.document.write(["<html>","   <head>",'<link rel="preconnect" href="https://fonts.googleapis.com">','<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>','<link href="https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">',"   </head>",'   <body onload="window.print()" onafterprint="window.close()">','       <img src="'+t+'" width="200px"/><br />','       <div style="width:200px; text-align:center; font-family:raleway; margin-top:-8px;">',this.forms.NamaAlat,"</div>","   </body>","</html>"].join("")),e.document.close()}})};t("default",i(x,function(){var t=this,e=t._self._c;return e(f,{attrs:{sidebar:!0}},[e("SidePane",{attrs:{addButton:!0,rebind:t.rebindSidebar},on:{"item-click":t.ItemClick}}),e("div",{class:t.$api.isMobile()?"":"form-inline",staticStyle:{"max-width":"1000px",padding:"0 20px","padding-bottom":"50px"},style:{"margin-left":t.$api.isMobile()?"10px":"0"}},[t.forms.AlatId?e("div",{staticStyle:{float:"right","font-family":"raleway",background:"white","margin-top":"-20px",width:"210px"},attrs:{id:"dvRightBox"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:!t.$api.isMobile(),expression:"!$api.isMobile()"}],staticStyle:{padding:"10px 20px","text-align":"center","margin-top":"10px"}},[e("VueQrcode",{style:{width:"150px",height:"150px"},attrs:{id:"qralat",value:t.qrUrl}})],1),e(s,{directives:[{name:"show",rawName:"v-show",value:!t.$api.isMobile(),expression:"!$api.isMobile()"}],staticStyle:{"border-top":"1px solid #e3e3e3","text-align":"center",width:"210px"},attrs:{text:"",small:"",color:"primary"},on:{click:t.PrintQR}},[t._v(" PRINT BARCODE ")])],1):t._e(),e("div",{style:{display:t.$api.isMobile()?"":"flex"}},[e("div",{staticClass:"alat-img",style:{width:t.$api.isMobile()?"100%":""}},[e(m,{attrs:{value:t.forms.Foto},on:{"update:value":function(e){return t.$set(t.forms,"Foto",e)}}})],1),e("div",{class:t.$api.isMobile()?"":"form-inline",staticStyle:{"max-width":"450px",width:"100%"},style:{"margin-left":t.$api.isMobile()?"0":"20px","margin-top":t.$api.isMobile()?"0":"20px"}},[e(a,{attrs:{type:"text",label:"Nama Alat",value:t.forms.NamaAlat,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"NamaAlat",e)}}}),e(a,{attrs:{type:"text",label:"Nomor",value:t.forms.NomorAlat,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"NomorAlat",e)}}}),e(a,{attrs:{type:"text",label:"Merk",value:t.forms.Merk,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"Merk",e)}}}),e(a,{attrs:{type:"text",label:"Tipe",value:t.forms.Tipe,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"Tipe",e)}}}),e(a,{attrs:{type:"number",label:"Tahun",value:t.forms.Tahun,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"Tahun",e)}}}),e(h,{attrs:{dbref:"PML_SelTeknisi",dbparams:{nocache:!0},label:"PIC",value:t.forms.PIC,width:"300px"},on:{"update:value":function(e){return t.$set(t.forms,"PIC",e)}}}),e(a,{attrs:{type:"number",label:"Periode Pemeliharaan",value:t.forms.PeriodePemeliharaan,width:"300px",postfix:"tahun"},on:{"update:value":function(e){return t.$set(t.forms,"PeriodePemeliharaan",e)}}})],1)]),e("br"),t.forms.AlatId?e(b,{attrs:{datagrid:t.datagrid,dbref:"PML_SelAlatActivity",dbparams:t.dbparams,doRebind:t.rebind,disabled:!0,columns:[{name:"Tanggal",value:"Tanggal"},{name:"Aktifitas",value:"Aktifitas",width:"100px"},{name:"Keterangan",value:"Keterangan",width:"300px"},{name:"Bukti",value:"Bukti"},{name:"",value:"Delete"}]},on:{"update:datagrid":function(e){t.datagrid=e}},scopedSlots:t._u([{key:"row-Tanggal",fn:function(e){var r=e.row;return[t._v(" "+t._s(t._f("format")(r.Tanggal))+" ")]}},{key:"row-Delete",fn:function(r){var i=r.row;return[e(c,{attrs:{color:"error"},on:{click:function(e){return t.DelActivity(i.AlatActivityId)}}},[t._v(" mdi-trash-can ")])]}},{key:"footer",fn:function(){return[e("tr",[e("td",{attrs:{colspan:"4"}},[e(s,{attrs:{text:"",small:""},on:{click:function(e){t.showDet=!0}}},[e(c,{attrs:{left:""}},[t._v("mdi-plus-circle")]),t._v(" Tambah ")],1)],1)])]},proxy:!0},{key:"row-Bukti",fn:function(r){var i=r.row;return[e("a",{attrs:{href:t.$api.url+i.Bukti,target:"_blank"}},[e(c,{attrs:{color:"primary"}},[t._v("mdi-open-in-new")]),t._v(" Buka ")],1)]}}],null,!1,4126180271)}):t._e(),e("br"),e("br"),t.disabled?t._e():e("div",{staticStyle:{display:"flex","margin-bottom":"30px"}},[e(s,{staticStyle:{"margin-left":"5px"},attrs:{color:"primary"},on:{click:t.Save}},[t._v(" SIMPAN ")]),e(v),e(s,{staticStyle:{"margin-left":"5px"},attrs:{color:"error"},on:{click:t.Delete}},[t._v(" HAPUS ")])],1)],1),e("alat-det",{attrs:{show:t.showDet,alatId:t.forms.AlatId},on:{"update:show":function(e){t.showDet=e}}})],1)},[],!1,null,null).exports)}}})}();
