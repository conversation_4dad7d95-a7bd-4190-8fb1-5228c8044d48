from ble_client import Lywsd03mmcClientSyncContext, Lywsd03mmcData, Lywsd03mmcOneHourHistoryData, Lywsd03mmcClient
import asyncio
import aiohttp

DEVICES = [
    { "ADDRESS": "A4:C1:38:73:CD:77", "HASH": "c6faff7b" },
    { "ADDRESS": "A4:C1:38:08:E4:C0", "HASH": "40b013c2" },
    { "ADDRESS": "A4:C1:38:C0:9F:01", "HASH": "87dfa550" },
    { "ADDRESS": "A4:C1:38:36:65:36", "HASH": "913f89d4" },
]

async def main():
    for device in DEVICES:
        MAC_ADDRESS_OR_UUID = device["ADDRESS"]
        print(f"Connecting to {MAC_ADDRESS_OR_UUID}...")
        try:
            client: Lywsd03mmcClient = Lywsd03mmcClient(MAC_ADDRESS_OR_UUID, timeout=60)
            await client.connect()
            print("Connected")
            data: Lywsd03mmcData = await client.get_data()
            print(data.temperature, data.humidity)

            async with aiohttp.ClientSession() as session:
                payload = {
                    "RuangHash": device["HASH"],
                    "Suhu": data.temperature,
                    "Kelembapan": data.humidity
                }
                async with session.post("http://localhost:8001/api/call/EVO_SavRuangJam", json=payload) as resp:
                    print("POST status:", resp.status)
                    print("Response:", await resp.text())

            # data = await client.get_history_data()
            # for d in data:
            #     print(d.timestamp, d.temperature_max, d.humidity_max, d.temperature_min, d.humidity_min)
            print()
            await client.close()
        except Exception as ex:
            print(f"Error for {MAC_ADDRESS_OR_UUID}: {ex}")

if __name__ == "__main__":
    asyncio.run(main())