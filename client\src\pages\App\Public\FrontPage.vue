<template>
  <v-container style="max-width: 100vw; padding: 0">
    <Header />
    <Carousel />
    <section
      class="wow fadeIn"
      style="padding: 20px; background: #ddd; text-align: center"
    >
      <v-btn color="info" style="font-size: 14px" onclick="daftar(true)">
        AJUKAN PERMOHONAN PENGUJIAN
      </v-btn>
    </section>
    <Lacak />
    <Features />
    <Contact />
    <Pengajuan
      v-if="showPengajuan"
      @close="showPengajuan = false"
      :showDaftar="true"
    />
    <footer id="footer">
      <div class="container">
        <div class="row" style="margin: 0">
          <div class="col-sm-6">
            &copy; 2019 Balai Pengujian Peralatan Provinsi Jawa Tengah
          </div>
          <div class="col-sm-6">
            <ul class="social-icons">
              <li>
                <a href="#"><i class="fa fa-facebook"></i></a>
              </li>
              <li>
                <a href="#"><i class="fa fa-twitter"></i></a>
              </li>
              <!-- <li><a href="#"><i class="fa fa-google-plus"></i></a></li>
                      <li><a href="#"><i class="fa fa-pinterest"></i></a></li>
                      <li><a href="#"><i class="fa fa-dribbble"></i></a></li>
                      <li><a href="#"><i class="fa fa-behance"></i></a></li>
                      <li><a href="#"><i class="fa fa-flickr"></i></a></li> -->
              <li>
                <a href="#"><i class="fa fa-youtube"></i></a>
              </li>
              <li>
                <a href="#"><i class="fa fa-linkedin"></i></a>
              </li>
              <!-- <li><a href="#"><i class="fa fa-github"></i></a></li> -->
            </ul>
          </div>
        </div>
      </div>
    </footer>
  </v-container>
</template>

<script>
import Header from './Header.vue'
import Pengajuan from './Pengajuan.vue'
import Carousel from './OwlCarousel.vue'
import Lacak from './Lacak.vue'
import Features from './Features.vue'
import Contact from './Contact.vue'
import { mapActions } from 'vuex'
export default {
  components: {
    Header,
    Pengajuan,
    Carousel,
    Lacak,
    Features,
    Contact,
  },
  data: () => ({
    doCycle: true,
    showPengajuan: false,
  }),
  computed: {
    isMobile() {
      return window.innerWidth < window.innerHeight
    },
  },
  mounted() {
    let jquery = document.createElement('script')
    jquery.setAttribute('src', '/web/jquery.js')
    jquery.async = true
    document.head.appendChild(jquery)

    setTimeout(() => {
      let $ = window.$
      $('.navbar-toggle').click(() => {
        if (!$('.navbar-collapse').hasClass('in'))
          $('.navbar-collapse').addClass('in')
        else $('.navbar-collapse').removeClass('in')
      })
      $('.navx a').click((evt) => {
        if (
          $(evt.target) &&
          $(evt.target).attr('href') != 'javascript:void(0)'
        ) {
          $('.navbar-collapse').removeClass('in')
        }
      })
    }, 2000)

    let user = localStorage.getItem('user')
    if (user) {
      user = JSON.parse(user)
      if (user.RolePositionID < 66) {
        this.$router.push({ path: '/Main/App/Home' })
      } else {
        this.$router.push({ path: '/User/App/Home' })
      }
    } else {
      setTimeout(() => {
        this.doCycle = false
      }, 12000)
    }

    const plugin = document.createElement('script')
    plugin.setAttribute('src', 'https://use.fontawesome.com/63ca95cbc8.js')
    plugin.async = true
    document.head.appendChild(plugin)

    window.daftar = () => {
      this.showPengajuan = true
      window.$('.navbar-collapse').removeClass('in')
    }
  },
  methods: {
    ...mapActions(['setMenu', 'setUser']),
    async StopCycle() {
      this.doCycle = false
    },
  },
}
</script>
<style lang="scss">
html,
body {
  overflow: auto;
}
.v-carousel {
  .v-responsive__content {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
