var express = require('express')
var utils = require('../common/utils')
var queries = require('./queries_select')
var db = require('../common/db')
var multer = require('multer')
var moment = require('moment')
var sharp = require('sharp')
var fs = require('fs')
var crypto = require('crypto')
var router = express.Router()
const jwt = require('jsonwebtoken')
var ertlh = require('./thirdparty/ertlh')
var dinsos = require('./thirdparty/dinsos')
const JWT_SECRET = 'ganteng'
const path = require('path')
const Excel = require('exceljs')
const axios = require("axios");
const carbone = require("carbone");
const {
  generateRegistrationOptions,
  verifyRegistrationResponse,
  generateAuthenticationOptions,
  verifyAuthenticationResponse
} = require('@simplewebauthn/server')

var upload = multer({ dest: 'uploads/' })
// middleware that is specific to this router
// router.use(async function app_mid(req, res, next) {
//   var s = req.session;
//   if (!req.body._UserIDRef && s.user && s.user.UserID)
//     req.body._UserIDRef = s.user.UserID;

//   next();
// });
const getAuthToken = req => {
  // if (process.env.DEVELOPMENT) {
  //   const tokenHeader = req.headers.authorization
  //   if (tokenHeader && tokenHeader.startsWith('Bearer ')) {
  //     return tokenHeader.slice(7, tokenHeader.length)
  //   } else {
  //     return false
  //   }
  // } else {
  // if(req.signedCookies) {
  //   return req.signedCookies['twj'] 
  // } else {

  // }
  if (req.headers.cookie) {
    let x = req.headers.cookie.split(';').filter((h) => {
      if (h.match(/twj=/)) return h
      else return false
    })
    if (x.length && x[0].split) {
      return x[0].split('=')[1]
    } else {
      return false
    }
  } else {
    return req.signedCookies['twj']
  }
  // }
}

const authMiddleware = (req, res, next) => {
  let token = getAuthToken(req)
  // req.body._userId = 1;
  // next();
  if (req.url.match(/^\/call\/EVO/) || req.url.match(/^\/call\/WEB/)) {
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        next()
      } else {
        if (decoded) {
          req.body._userId = decoded.userId
        }
        next()
      }
    })
  } else if (token) {
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        // console.error(err.message)
        // next(err);
        res.send({
          success: false,
          message: 'Not authorized, Please Login Again.',
        })
      } else {
        if (decoded) {
          // req.user = { id: decoded.userId };
          req.body._userId = decoded.userId
        }
        next()
      }
    })
  } else {
    res.send({
      success: false,
      message: 'Not authorized, Please Login Again.',
    })
    // req.body._userId = 1
    // next()
  }
}

// router.use(authMiddleware);

function jwtSignUser(user) {
  const ONE_DAY = 60 * 60 * 24 // * 7 * 4 * 4;
  return jwt.sign({ userId: user.UserID }, JWT_SECRET, {
    expiresIn: ONE_DAY,
  })
}

router.get('/', async (req, res) => {
  res.send({ success: true })
})

router.post('/login', async (req, res) => {
  const ipAddr = req.headers['x-real-ip'] || req.socket.remoteAddress
  db.query(`INSERT INTO arch_userlog (UserID, UserLogTypeID, LogDescription, LogStatus, CreatedDate, CreatedBy)
    VALUES(0, 'login', ${JSON.stringify({ ...req.body, ipAddr: ipAddr })}, 'success', NOW(), 0)`)

  var result = await db.exec('Arch_SelUserLogin', req.body)
  // console.log(result);
  if (result && result.length) {
    let token = jwtSignUser(result[0])
    res
      .cookie('twj', token, { secure: true, sameSite: true, httpOnly: true })
      .send({
        success: true,
        data: result,
        token: token,
        type: 'array',
        message: 'Login Successfully.',
      })
  } else {
    res.send({
      success: false,
      message: '[!] Invalid Login',
      type: 'error',
      data: null,
    })
  }
})


// FIDO2 WebAuthn Configuration
const rpName = 'SILAKON'
const rpID = process.env.NODE_ENV === 'development' ? 'localhost' : 'silakon.dpubinmarcipka.jatengprov.go.id/'
const origin = process.env.NODE_ENV === 'development' ? 'http://localhost:8000' : 'https://silakon.dpubinmarcipka.jatengprov.go.id/'

// Temporary storage for challenges (in production, use Redis or database)
const challenges = new Map()

// Passkey authentication routes
router.post('/passkey/check', async (req, res) => {
  try {
    const { userId } = req.body

    if (!userId) {
      return res.send({
        success: false,
        message: 'User ID is required'
      })
    }

    // Get user's registered passkeys
    const passkeysResult = await db.exec('Arch_SelUserPasskeys', { UserID: userId })

    const hasPasskeys = passkeysResult && passkeysResult.length > 0

    res.send({
      success: true,
      data: {
        hasPasskeys,
        passkeyCount: passkeysResult ? passkeysResult.length : 0
      }
    })
  } catch (error) {
    console.error('Passkey check error:', error)
    res.send({
      success: false,
      message: 'Internal server error'
    })
  }
})

// FIDO2 Registration - Generate options
router.post('/passkey/register', authMiddleware, async (req, res) => {
  const { _userId } = req.body

  if (!_userId) {
    return res.send({
      success: false,
      message: 'login is required'
    })
  }

  try {
    // TODO: Check if user exists in database
    const user = await db.exec('Arch_SelCurrentUser', req.body)
    if (!user.length) {
      return res.send({
        success: false,
        message: 'User not found'
      })
    }

    const options = await generateRegistrationOptions({
      rpName,
      rpID,
      userID: user[0].UserID,
      userName: user[0].Username,
      userDisplayName: user[0].FullName,
      attestationType: 'none',
      excludeCredentials: [], // TODO: Get existing credentials from database
      authenticatorSelection: {
        residentKey: 'preferred',
        userVerification: 'preferred',
        // authenticatorAttachment: 'platform', // Prefer platform authenticators (biometrics)
      },
    })

    // Store challenge temporarily
    challenges.set(user[0].UserID, options.challenge)

    res.send({
      success: true,
      data: options
    })
  } catch (error) {
    console.error('FIDO2 registration begin error:', error)
    res.send({
      success: false,
      message: 'Failed to generate registration options'
    })
  }
})

// FIDO2 Registration - Verify response
router.post('/passkey/registered', authMiddleware, async (req, res) => {
  const { username, credential } = req.body

  if (!credential) {
    return res.send({
      success: false,
      message: 'Username and credential are required'
    })
  }

  //console.log(req.body)
  try {
    const expectedChallenge = challenges.get(req.body._userId)
    if (!expectedChallenge) {
      return res.send({
        success: false,
        message: 'No challenge found for user'
      })
    }

    const verification = await verifyRegistrationResponse({
      response: credential,
      expectedChallenge,
      expectedOrigin: origin,
      expectedRPID: rpID,
    })

    console.log(verification)

    if (verification.verified) {
      // TODO: Store credential in database
      await db.exec('Arch_SavCredential', {
        ...req.body,
        _CredentialID: {
          type: 'Buffer', data: verification.registrationInfo.credentialID
        },
        _PublicKey: {
          type: 'Buffer', data: verification.registrationInfo.credentialPublicKey
        },
        _Counter: verification.registrationInfo.counter,
        _DeviceType: verification.registrationInfo.credentialDeviceType,
        _BackedUp: verification.registrationInfo.credentialBackedUp,
      }).catch(ex => console.error(ex))

      challenges.delete(req.body._userId)

      res.send({
        success: true,
        message: 'FIDO2 credential registered successfully'
      })
    } else {
      res.send({
        success: false,
        message: 'Failed to verify registration'
      })
    }
  } catch (error) {
    console.error('FIDO2 registration complete error:', error)
    res.send({
      success: false,
      message: 'Registration verification failed'
    })
  }
})

// FIDO2 Authentication - Generate options
router.post('/passkey/login', async (req, res) => {
  const { username } = req.body

  if (!username) {
    return res.send({
      success: false,
      message: 'Username is required'
    })
  }

  try {
    // TODO: Get user's credentials from database
    // const user = await db.exec('Arch_SelUser', {_Username: username})
    // if (!user.length) {
    //   return res.send({
    //     success: false,
    //     message: 'User not found'
    //   })
    // }

    const options = await generateAuthenticationOptions({
      rpID,
      allowCredentials: [], // TODO: Map from userCredentials
      userVerification: 'preferred',
    })

    // Store challenge temporarily
    challenges.set(username, options.challenge)

    res.send({
      success: true,
      data: options
    })
  } catch (error) {
    console.error('FIDO2 authentication begin error:', error)
    res.send({
      success: false,
      message: 'Failed to generate authentication options'
    })
  }
})


// FIDO2 Authentication - Verify response
router.post('/passkey/verify', async (req, res) => {
  const { username, credential } = req.body

  if (!username || !credential) {
    return res.send({
      success: false,
      message: 'Username and credential are required'
    })
  }

  try {
    const expectedChallenge = challenges.get(username)
    if (!expectedChallenge) {
      return res.send({
        success: false,
        message: 'No challenge found for user'
      })
    }

    // Convert Base64URL credential.id to Buffer for database comparison
    const deviceId = req.headers['x-device-id']
    const credentialIdBuffer = Buffer.from(credential.id, 'base64url')

    // TODO: Get credential from database
    const dbCredential = await db.exec('Arch_SelCredential', {
      _deviceId: deviceId,
      _Username: username,
      _CredentialID: { type: 'Buffer', data: credentialIdBuffer }
    })

    if (!dbCredential.length) {
      return res.send({
        success: false,
        message: 'Biometrik belum terdaftar'
      })
    }

    // Prepare authenticator object with proper data types
    const authenticator = {
      credentialID: credentialIdBuffer, // Use the converted Buffer
      credentialPublicKey: new Uint8Array(dbCredential[0].PublicKey), // Convert from database
      counter: dbCredential[0].Counter, // Get from database
    }

    const verification = await verifyAuthenticationResponse({
      response: credential,
      expectedChallenge,
      expectedOrigin: origin,
      expectedRPID: rpID,
      authenticator,
    }).catch(ex => {
      console.log(ex)
    })

    if (verification.verified) {
      // TODO: Update counter in database
      // await db.exec('Arch_UpdFIDO2Counter', {
      //   _Username: username,
      //   _CredentialID: credential.id,
      //   _Counter: verification.authenticationInfo.newCounter
      // })

      // Get user data for login
      // const result = await db.exec('Arch_SelUserLogin', {_Username: username})

      if (dbCredential && dbCredential.length && !dbCredential[0].ErrCode) {
        const token = jwtSignUser(dbCredential[0])
        challenges.delete(username)

        res
          .cookie('twj', token, { secure: true, sameSite: true, httpOnly: true })
          .send({
            success: true,
            data: {
              UserID: dbCredential[0].UserID,
              FullName: dbCredential[0].FullName,
              RolePositionID: dbCredential[0].RolePositionID,
              HomeUrl: dbCredential[0].HomeUrl,
              HasBiometric: dbCredential[0].HasBiometric,
            },
            token: token,
            type: 'array',
            message: 'FIDO2 Authentication successful',
          })
      } else {
        res.send({
          success: false,
          message: 'User not found or inactive'
        })
      }
    } else {
      res.send({
        success: false,
        message: 'Authentication verification failed'
      })
    }
  } catch (error) {
    console.error('FIDO2 authentication complete error:', error)
    res.send({
      success: false,
      message: 'Authentication failed'
    })
  }
})

router.get('/logout', async function (req, res) {
  var s = req.session
  s.user = undefined

  res
    .cookie('twj', null, { secure: true, sameSite: true, httpOnly: true, signed: true })
    .send({
      success: true,
      data: '/',
      type: 'redirect',
      message: 'Logged Out.',
    })
})

router.post("/track/:id", async function (req, res) {
  var captcha = await utils.verifyCaptcha(req.body.token);

  if (captcha.success) {
    var d = await db.exec("UJI_SelTracking", { _ShareCode: req.params.id });
    var data = d[0];
    if (d.length) {
      d = await db.exec("UJI_SelPermohonanDet", {
        _PermohonanID: data.PermohonanID
      });
      data.detail = d;

      res.send({
        Success: true,
        Data: data,
        Type: "array",
        Message: ""
      });
    } else {
      res.send({
        Success: false,
        Data: "",
        Type: "string",
        Message: "Data Tidak Ditemukan."
      });
    }
  } else {
    res.send({
      Success: false,
      Data: "data",
      Type: "string",
      Message: "Wrong Captcha!"
    });
  }
});

const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
}

const uploadMulti = async (req, res) => {
  let proms = []
  for (let file of req.files) {
    proms.push(new Promise((resolve, reject) => {
      uploadfunction(req.url, file, resolve)
    }))
    sleep(10)
  }
  let rets = await Promise.all(proms)
  let files = []
  for (let ret of rets) {
    if (!ret.success) {
      res.send(ret)
      break;
    } else {
      files.push(ret.data)
    }
  }
  res.send({
    ...rets[0],
    data: files
  })
}

const uploadSingle = async (req, res) => {
  let proms = []
  proms.push(new Promise((resolve, reject) => {
    uploadfunction(req.url, req.file, resolve)
  }))
  let rets = await Promise.all(proms)
  for (let ret of rets) {
    if (!ret.success) {
      res.send(ret)
      break;
    }
  }
  res.send(rets[0])
}

const uploadfunction = function (url, file, resolve, reject) {
  if (!file || !file.mimetype) {
    resolve({
      success: false,
      data: '',
      message: 'Tipe file tidak terdeteksi, hubungi admin.',
      type: 'error',
    })
  } else if (file.mimetype.match(/image/)) {
    let tmp_path = file.path

    let ext = '.jpg'
    if (file.mimetype == 'image/jpeg') ext = '.jpg'
    else ext = '.' + file.originalname.substr(-3)

    let year_dir = moment().format('YYWW')
    let filename = moment().format('DDHHmmssSSS') + ext
    let target_path = 'uploads/' + year_dir + '/ori/'
    if (!fs.existsSync('uploads/' + year_dir)) {
      fs.mkdirSync('uploads/' + year_dir)
      fs.mkdirSync('uploads/' + year_dir + '/ori/')
      fs.mkdirSync('uploads/' + year_dir + '/med/')
      fs.mkdirSync('uploads/' + year_dir + '/small/')
      fs.mkdirSync('uploads/' + year_dir + '/tiny/')
    }

    let src = fs.createReadStream(tmp_path)
    let dest = fs.createWriteStream(target_path + filename)
    src.pipe(dest)
    src.on('end', async function () {
      await sharp(tmp_path)
        .resize(600)
        .toFile(target_path.replace(/\/ori\//, '/med/') + filename)
        .catch(err => console.log(err))
      await sharp(target_path + filename)
        .resize(250)
        .toFile(target_path.replace(/\/ori\//, '/small/') + filename)
        .catch(err => console.log(err))
      await sharp(target_path + filename)
        .resize(80)
        .toFile(target_path.replace(/\/ori\//, '/tiny/') + filename)
        .catch(err => console.log(err))
      //res.render("complete");
      resolve({
        success: true,
        data: '/' + target_path + filename,
        meta: await utils.getExifData(tmp_path),
        message: 'File Uploaded!',
        type: 'string',
      })
    })
    src.on('error', function (err) {
      resolve({
        success: false,
        data: '',
        message: err.message,
        type: 'error',
      })
    })
  } else {
    let tmp_path = file.path
    let ext = file.originalname.substr(
      file.originalname.lastIndexOf('.')
    )

    let year_dir = moment().format('YYWW')
    let filename = moment().format('DDHHmmssSSS') + ext
    let target_path = 'uploads/' + year_dir + '/'
    if (!fs.existsSync('uploads/' + year_dir)) {
      fs.mkdirSync('uploads/' + year_dir)
      // fs.mkdirSync('uploads/' + year_dir + '/ori/')
      // fs.mkdirSync('uploads/' + year_dir + '/med/')
      // fs.mkdirSync('uploads/' + year_dir + '/small/')
      // fs.mkdirSync('uploads/' + year_dir + '/tiny/')
    }

    let src = fs.createReadStream(tmp_path)
    let dest = fs.createWriteStream(target_path + filename)
    src.pipe(dest)
    src.on('end', async function () {
      uploadConvert(url, target_path + filename)
      resolve({
        success: true,
        data: '/' + target_path + filename,
        message: 'File Uploaded!',
        type: 'string',
      })
    })
  }
}

const shell = require("shelljs");
const exportPDF = (file, outdir = '.', tipe = 'pdf:writer_pdf_Export') => {
  let libre = "/opt/libreoffice7.6/program/soffice"
  if (!fs.existsSync(libre)) {
    libre = `"C:\\Program Files\\LibreOffice\\program\\soffice.exe"`
  }
  const out = shell.exec(
    `${libre} --nolockcheck --headless --convert-to ${tipe} "${file}" --outdir ${outdir}`,
    {
      silent: true
    }
  )
  if (out.code !== 0) {
    console.error(`ERR: ${out.stderr}`)
  }
}

const uploadConvert = (url, path) => {
  if (url.match(/\/pdf$/)) {
    if (path.match(/xlsx$/)) {
      exportPDF('./' + path, './' + path.replace(/[a-zA-Z0-9]+\.xlsx$/, ''), 'ods')
      path = path.replace(/xlsx$/, 'ods')
    }

    carbone.render(
      './' + path,
      {},
      { convertTo: "pdf" },
      function (err, result) {
        if (err) return console.log(err);
        console.log("Converting.. ");
        fs.writeFileSync(__dirname + `/../` + path.replace(/(ods|xlsx|docx)$/, 'pdf'), result);
        //res.sendFile(path.resolve(__dirname + `/../../tmp/pendaftaran_${addf}.pdf`));
        //process.exit();
      }
    );
  }
}

const do_cleanres = async function (target_path) {
  const files = fs.readdirSync(target_path)
  files.forEach(async f => {
    let file = target_path + f
    if (fs.lstatSync(file).isDirectory()) do_cleanres(file + '/')
    else {
      let ret = await db.exec('Arch_CleanRes', { FileCheck: file })
      if (!ret.length) {
        console.log(`deleting ${file} ... `)
        fs.unlinkSync(file)
        if (fs.existsSync(file.replace('/ori/', '/med/')))
          fs.unlinkSync(file.replace('/ori/', '/med/'))
        if (fs.existsSync(file.replace('/ori/', '/small/')))
          fs.unlinkSync(file.replace('/ori/', '/small/'))
        if (fs.existsSync(file.replace('/ori/', '/tiny/')))
          fs.unlinkSync(file.replace('/ori/', '/tiny/'))
      }
    }
  })
}

router.get('/cleanres/:folder?', (req, res) => {
  let year_dir = moment().format('YYWW')
  if (req.params.folder) year_dir = req.params.folder
  var target_path = 'uploads/' + year_dir + '/ori/'
  let files = fs.readdirSync('uploads/')
  files.forEach(file => {
    if (
      fs.lstatSync('uploads/' + file).isFile() &&
      file.length === 32 &&
      !file.match(/\./)
    ) {
      fs.unlinkSync('uploads/' + file)
    }
  })
  files = fs.readdirSync('tmp/')
  files.forEach(file => {
    if (fs.lstatSync('tmp/' + file).isFile()) {
      fs.unlinkSync('tmp/' + file)
    }
  })
  do_cleanres(target_path)
  res.send({
    success: true,
  })
})

// public function cleanres(){

//   $folder = $this->f3->get('PARAMS.folder');
//   $date = new DateTime();
//   if($folder == 'now') $folder = $date->format('ym').substr($date->format('d'), 0, 1);

//   //$this->do_cleanres(BASE_PATH.'/uploads/1705/ori');
//   $this->do_cleanres(BASE_PATH.'/uploads/'.$folder.'/ori');
//   //$this->do_cleanres(BASE_PATH.'/uploads/17060/ori');
//   //$this->do_cleanres(BASE_PATH.'/uploads/17061/ori');
//   //$this->do_cleanres(BASE_PATH.'/uploads/17062/ori');
//   //$this->do_cleanres(BASE_PATH.'/uploads/17063/ori');
//   //echo "here";
// }

// private function do_cleanres($dir){
//   $ffs = scandir($dir);

//   unset($ffs[array_search('.', $ffs, true)]);
//   unset($ffs[array_search('..', $ffs, true)]);
//   //unset($ffs[array_search('med', $ffs, true)]);
//   //unset($ffs[array_search('small', $ffs, true)]);
//   //unset($ffs[array_search('tiny', $ffs, true)]);

//   // prevent empty ordered elements
//   if (count($ffs) < 1)
//       return;

//   foreach($ffs as $ff){
//       if(is_dir($dir.'/'.$ff)) $this->do_cleanres($dir.'/'.$ff);

//       $res = $this->Exec("Arch_CleanRes" , ['_FileCheck' => str_replace(BASE_PATH, "", $dir.'/'.$ff)]);
//       if(count($res) == 0){
//           echo "deleting ".str_replace(BASE_PATH, "", $dir.'/'.$ff)."<br>\r\n";
//           if(file_exists(str_replace('/ori/','/med/',$dir.'/'.$ff)))      unlink(str_replace('/ori/','/med/',$dir.'/'.$ff));
//           if(file_exists(str_replace('/ori/','/small/',$dir.'/'.$ff)))    unlink(str_replace('/ori/','/small/',$dir.'/'.$ff));
//           if(file_exists(str_replace('/ori/','/tiny/',$dir.'/'.$ff)))     unlink(str_replace('/ori/','/tiny/',$dir.'/'.$ff));
//           unlink($dir.'/'.$ff);

//           //ob_flush();
//           //flush();
//       }
//   }
// }

router.post('/upload', upload.single('file'), uploadSingle)
router.post('/upload/pdf', upload.single('file'), uploadSingle)
router.post('/upload-multi', upload.array('files', 10), uploadMulti)
router.post('/upload-multi/pdf', upload.array('files', 10), uploadMulti)

router.post('/call/:sp', authMiddleware, async function (req, res) {
  try {
    var d = await db.exec(req.params.sp, req.body)
    var data = d
    if (req.body['__grid']) {
      data = { data: d }
    }

    res.send({
      success: data && data.length && data[0].ErrCode ? false : true,
      data: data,
      type: 'array',
      message: data && data.length && data[0].Message ? data[0].Message : '',
    })
  } catch (ex) {
    res.send({
      success: false,
      data: 'Error',
      type: 'error',
      message: 'Error ' + ex.message,
    })
  }
})

router.post('/public/:sp', async function (req, res) {
  try {
    if (!req.params.sp.match(/^EVO/)) {
      res.send({
        success: false,
        message: 'Not authorized, Please Login Again.',
      })
    }
    var d = await db.exec(req.params.sp, req.body)
    var data = d
    if (req.body['__grid']) {
      data = { data: d }
    }

    res.send({
      success: data && data.length && data[0].ErrCode ? false : true,
      data: data,
      type: 'array',
      message: data && data.length && data[0].Message ? data[0].Message : '',
    })
  } catch (ex) {
    res.send({
      success: false,
      data: 'Error',
      type: 'error',
      message: 'Error ' + ex.message,
    })
  }
})

router.post('/select/:object', authMiddleware, async function (req, res) {
  try {
    var d = await db.query(queries.build(queries[req.params.object]))
    var data = d

    res.send({
      success: data && data.length && data[0].ErrCode ? false : true,
      data: data,
      type: 'array',
      message: data && data.length && data[0].Message ? data[0].Message : '',
    })
  } catch (ex) {
    res.send({
      success: false,
      data: 'Error',
      type: 'error',
      message: 'Error ' + ex.message,
    })
  }
})

router.post('/import', authMiddleware, async function (req, res) {
  // try {
  let { from, sp, to, _userId } = req.body
  if (from.match(/.xlsx$/) && to) {
    let fn = from.match(/\/(\d+).xlsx$/)
    fn = fn.length ? fn[1] : '0'
    const workbook = new Excel.Workbook()
    await workbook.xlsx.readFile(path.resolve('./' + from))
    let qq = []
    let cols = await db.getColumns(to)
    workbook.eachSheet((sheet) => {
      sheet.eachRow((row, rowIndex) => {
        // SKIP HEADER
        if (rowIndex > 1) {
          let values = row.values.splice(1, cols.length - 1)
          if (values[0] && values.length > 6) {
            values = values.map(v => {
              if (v.toString && v.toString().match(/\d\d\sGMT/)) {
                v = moment(v).format('YYYY-MM-DD')
              }
              return v
            })
            values.push(fn)
            qq.push(`('${values.join("','")}')`)
          }

          // if (v[0]) {
          //   v.push(fn)
          //   qq.push(`('${v.join("','").replace(' GMT+0700 (Western Indonesia Time)','')}')`)
          // }
        }
      })
    })
    let failed = []
    let sql = `INSERT INTO ${to} VALUES ${qq.join(',').replace(/,''/g, ',NULL')}`
    let dbres = await db.query(sql)
    if (dbres.length && dbres[0].length && dbres[0][0].ErrCode) {
      // console.log(sql)
      res.send({
        success: false,
        data: failed,
        type: 'Error',
        message: dbres[0][0].Error,
      })
    } else {
      if (sp) failed = await db.exec(sp, { Filename: fn, _userId })
      if (failed.length && failed[0].ErrCode) {
        res.send({
          success: false,
          data: failed[0],
          type: 'Error',
          message: 'Import Failed',
        })
      } else {
        res.send({
          success: true,
          data: failed,
          type: 'Success',
          message: 'Imported Successfully',
        })
      }
    }
  } else {
    res.send({
      success: false,
      data: 'Error',
      type: 'error',
      message: 'File type not suppported',
    })
  }
})

const checkToken = async function (token, raw) {
  if (token === 'Basic YmFua0phdGVuZzp2c1MxbEBrMG4=') return true;
  else {
    try {
      let [hash, pubkey] = token.split('.')
      console.log(hash, pubkey)
      var d = await db.exec('Arch_SelPubKey', { PubKey: pubkey })
      console.log(d)
      return (
        hash ==
        crypto
          .createHash('md5')
          .update(raw + d[0].privkey)
          .digest('hex')
      )
    } catch {
      return false
    }
  }
}

router.get('/bank/:sp', async function (req, res) {
  try {
    let token = req.headers.authorization
    let logs = db.exec('Arch_InsApiLogs', {
      PubKey: token,
      RequestUrl: req.params.sp,
      RequestData: JSON.stringify(req.query)
    })
    if (await checkToken(token, req.url.replace('/bank/', ''))) {
      let d = await db.exec('BNK_' + req.params.sp, req.query)
      await logs
      if (req.query.filetype === 'csv') {
        if (d.length) {
          let csv = Object.keys(d[0]).join(',')
          for (let i = 0; i < d.length; i++) {
            csv += Object.values(d[i]).join(',') + '\n'
          }
          res.send(csv)
        } else {
          res.send('')
        }
      } else {
        if (d.length) {
          let needParse = []
          for (let key in d[0]) {
            if (key.match(/^_json_/)) {
              needParse.push(key)
              console.log(d[0][key])
            }
          }
          if (needParse.length) {
            d = d.map(o => {
              for (let key of needParse) {
                let newKey = key.replace(/^_json_/, '')
                o[newKey] = JSON.parse(o[key])
                delete o[key]
              }
              return o;
            })
          }
          res.send(d[0])
        } else {
          res.send({
            resp_code: '99',
            resp_desc: 'data tidak ditemukan'
          })
        }
      }
    } else {
      res.send({
        resp_code: '99',
        resp_desc: 'Error Invalid Auth',
      })
    }
  } catch (ex) {
    res.send({
      resp_code: '99',
      resp_desc: 'Error ' + ex.message,
    })
  }
})

router.post('/bank/:sp', async function (req, res) {
  try {
    let token = req.headers.authorization
    let logs = db.exec('Arch_InsApiLogs', {
      PubKey: token,
      RequestUrl: req.params.sp,
      RequestData: JSON.stringify(req.query)
    })
    if (await checkToken(token, JSON.stringify(req.body))) {
      let d = await db.exec('BNK_' + req.params.sp, req.body)
      if (d.length) {
        let needParse = []
        for (let key in d[0]) {
          if (key.match(/^_json_/)) {
            needParse.push(key)
            console.log(d[0][key])
          }
        }
        if (needParse.length) {
          d = d.map(o => {
            for (let key of needParse) {
              let newKey = key.replace(/^_json_/, '')
              o[newKey] = JSON.parse(o[key])
              delete o[key]
            }
            return o;
          })
        }
        res.send(d[0])
      } else {
        res.send({
          resp_code: '99',
          resp_desc: 'data tidak ditemukan'
        })
      }
    } else {
      res.send({
        resp_code: '99',
        resp_desc: 'Error Invalid Auth',
      })
    }
  } catch (ex) {
    res.send({
      resp_code: '99',
      resp_desc: 'Error ' + ex.message,
    })
  }
})

router.get('/pub/:sp', async function (req, res) {
  try {
    let token = req.headers.authorization
    let logs = db.exec('Arch_InsApiLogs', {
      PubKey: token,
      RequestUrl: req.params.sp,
      RequestData: JSON.stringify(req.query)
    })
    if (await checkToken(token, req.url.replace('/pub/', ''))) {
      let d = await db.exec('PUB_' + req.params.sp, req.query)
      await logs
      if (req.query.filetype === 'csv') {
        if (d.length) {
          let csv = Object.keys(d[0]).join(',')
          for (let i = 0; i < d.length; i++) {
            csv += Object.values(d[i]).join(',') + '\n'
          }
          res.send(csv)
        } else {
          res.send('')
        }
      } else {
        if (d.length) {
          let needParse = []
          for (let key in d[0]) {
            if (key.match(/^_json_/)) {
              needParse.push(key)
              console.log(d[0][key])
            }
          }
          if (needParse.length) {
            d = d.map(o => {
              for (let key of needParse) {
                let newKey = key.replace(/^_json_/, '')
                o[newKey] = JSON.parse(o[key])
                delete o[key]
              }
              return o;
            })
          }
        }
        res.send({
          Success: true,
          Data: d,
          Type: 'array',
          Message: '',
        })
      }
    } else {
      res.send({
        Success: false,
        Data: 'Error',
        Type: 'error',
        Message: 'Error Invalid Token',
      })
    }
  } catch (ex) {
    res.send({
      Success: false,
      Data: 'Error',
      Type: 'error',
      Message: 'Error ' + ex.message,
    })
  }
})

router.post('/pub/:sp', async function (req, res) {
  try {
    let token = req.headers.authorization
    if (await checkToken(token, JSON.stringify(req.body))) {
      let d = await db.exec('PUB_' + req.params.sp, req.body)
      if (req.params.sp == 'SaveDataRTLH' && d.length) {
        ertlh.insert(d[0].NIK)
      }
      if (d.length) {
        let needParse = []
        for (let key in d[0]) {
          if (key.match(/^_json_/)) {
            needParse.push(key)
            console.log(d[0][key])
          }
        }
        if (needParse.length) {
          d = d.map(o => {
            for (let key of needParse) {
              let newKey = key.replace(/^_json_/, '')
              o[newKey] = JSON.parse(o[key])
              delete o[key]
            }
            return o;
          })
        }
      }
      res.send({
        Success: d.length && !d[0].ErrCode ? true : false,
        Data: d,
        Type: 'array',
        Message: d.length ? d[0].Message : 'Data Tidak Ditemukan',
      })
    } else {
      res.send({
        Success: false,
        Data: 'Error',
        Type: 'error',
        Message: 'Error Invalid Token',
      })
    }
  } catch (ex) {
    res.send({
      Success: false,
      Data: 'Error',
      Type: 'error',
      Message: 'Error ' + ex.message,
    })
  }
})

const sendOtp = (to, otp) => {
  const https = require('https');
  const apiKey = 'by94c2RVOHcrMUcyWUVGOS9PRkUrdz09';
  const url = `https://getotp.co/api?otp=${otp}&to=${to}&key=${apiKey}`;

  return new Promise((resolve, reject) => {
    https.get(url, (resp) => {
      let data = '';

      resp.on('data', (chunk) => {
        data += chunk;
      });

      resp.on('end', () => {
        if (data.trim() == '1') {
          console.log('OTP Sent');
        } else {
          console.log('OTP Error: ' + data);
          throw new Error('Error sending OTP');
        }
      });

    }).on('error', (err) => {
      console.log('OTP Error: ' + err.message);
      throw new Error('Error sending OTP');
    });
  });
}

const registerRoute = (wa) => {
  router.post('/otp', async function (req, res) {
    let { Phone, Name } = req.body
    let otp = Math.floor(Math.random() * 1000000);
    Phone = Phone.replace(/\s/g, '').replace(/^0/, '+62').replace(/[^\w\d+]/, '')

    if (!Phone.match(/^\+62\d+$/)) {
      res.send({
        success: false,
        data: '',
        type: 'string',
        message: 'Nomor Whatsapp tidak valid',
      })
      return
    }
    var d = await db.exec("Arch_GenerateToken", { _Name: Name, _Phone: Phone, _Token: otp });

    let error = false
    try {
      let destNumber = Phone.replace('+', '') + '@c.us'
      await sendOtp(Phone, otp)
      // let m = await wa.sendMessage(destNumber, `Kode login SILAKON anda adalah: ${otp}`);
      // await db.query(`INSERT INTO web_whatsapp values (NOW(), 'Kode login SILAKON anda adalah: ${otp}', '${destNumber}', NOW())`)
    } catch (ex) {
      error = true;
    }
    if (error) {
      res.send({
        success: false,
        data: error,
        type: 'string',
        message: 'Kode Gagal Terkirim',
      })
    } else {
      // console.log(`OTP ${Phone}:${otp} messageId: ${m.id.id}`)
      res.send({
        success: true,
        data: '',
        type: 'string',
        message: 'Kode Konfirmasi Terkirim',
      })
    }
  })
}

module.exports = (wa) => {
  registerRoute(wa)
  return router
}