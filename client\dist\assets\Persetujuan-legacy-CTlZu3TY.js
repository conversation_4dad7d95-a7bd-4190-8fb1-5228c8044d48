!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,n,a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",o=a.toStringTag||"@@toStringTag";function c(t,a,i,o){var c=a&&a.prototype instanceof l?a:l,u=Object.create(c.prototype);return r(u,"_invoke",function(t,r,a){var i,o,c,l=0,u=a||[],d=!1,p={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,r){return i=t,o=0,c=e,p.n=r,s}};function f(t,r){for(o=t,c=r,n=0;!d&&l&&!a&&n<u.length;n++){var a,i=u[n],f=p.p,h=i[2];t>3?(a=h===r)&&(c=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=e):i[0]<=f&&((a=t<2&&f<i[1])?(o=0,p.v=r,p.n=i[1]):f<h&&(a=t<3||i[0]>r||r>h)&&(i[4]=t,i[5]=r,p.n=h,o=0))}if(a||t>1)return s;throw d=!0,r}return function(a,u,h){if(l>1)throw TypeError("Generator is already running");for(d&&1===u&&f(u,h),o=u,c=h;(n=o<2?e:c)||!d;){i||(o?o<3?(o>1&&(p.n=-1),f(o,c)):p.n=c:p.v=c);try{if(l=2,i){if(o||(a="next"),n=i[a]){if(!(n=n.call(i,c)))throw TypeError("iterator result is not an object");if(!n.done)return n;c=n.value,o<2&&(o=0)}else 1===o&&(n=i.return)&&n.call(i),o<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),o=1);i=e}else if((n=(d=p.n<0)?c:t.call(r,p))!==s)break}catch(n){i=e,o=1,c=n}finally{l=1}}return{value:n,done:d}}}(t,i,o),!0),u}var s={};function l(){}function u(){}function d(){}n=Object.getPrototypeOf;var p=[][i]?n(n([][i]())):(r(n={},i,function(){return this}),n),f=d.prototype=l.prototype=Object.create(p);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,r(t,o,"GeneratorFunction")),t.prototype=Object.create(f),t}return u.prototype=d,r(f,"constructor",d),r(d,"constructor",u),u.displayName="GeneratorFunction",r(d,o,"GeneratorFunction"),r(f),r(f,o,"Generator"),r(f,i,function(){return this}),r(f,"toString",function(){return"[object Generator]"}),(t=function(){return{w:c,m:h}})()}function r(t,e,n,a){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}r=function(t,e,n,a){function o(e,n){r(t,e,function(t){return this._invoke(e,n,t)})}e?i?i(t,e,{value:n,enumerable:!a,configurable:!a,writable:!a}):t[e]=n:(o("next",0),o("throw",1),o("return",2))},r(t,e,n,a)}function e(t,r,e,n,a,i,o){try{var c=t[i](o),s=c.value}catch(t){return void e(t)}c.done?r(s):Promise.resolve(s).then(n,a)}function n(t){return function(){var r=this,n=arguments;return new Promise(function(a,i){var o=t.apply(r,n);function c(t){e(o,a,i,c,s,"next",t)}function s(t){e(o,a,i,c,s,"throw",t)}c(void 0)})}}System.register(["./index-legacy-BUdDePUl.js"],function(r,e){"use strict";var a,i,o,c,s;return{setters:[function(t){a=t.n,i=t.a,o=t.l,c=t._,s=t.k}],execute:function(){var e=document.createElement("style");e.textContent=".sidepane{padding:0!important;width:300px;border-right:1px solid #ddd;height:calc(100vh - 64px);overflow:hidden}.sidepane .searchbar{margin-bottom:0}.sidepane .searchbar input{background:transparent!important;border-bottom:0!important}\n/*$vite$:1*/",document.head.appendChild(e);var l={data:function(){return{keyword:""}},props:{statusId:String,rebind:Number,addButton:Boolean},computed:{dbparams:function(){return{ApprovalStep:0,Keyword:this.keyword||""}}},methods:{ItemClick:function(t){this.$emit("item-click",t)},DaftarBaru:function(){this.$emit("item-click",{PengujianID:0})}}},u=a(l,function(){var t=this,r=t._self._c;return r("div",{staticClass:"sidepane"},[r("div",{staticStyle:{padding:"10px",display:"flex"}},[r(i,{staticClass:"searchbar",attrs:{type:"text",value:t.keyword,placeholder:"Cari ..",width:"270px",rightIcon:"mdi-magnify"},on:{"update:value":function(r){t.keyword=r}}})],1),r("div",{staticStyle:{height:"calc(100% - 47px)"}},[r(o,{attrs:{dbref:"UJI_SelPersetujuanLK",dbparams:t.dbparams,height:t.addButton?"calc(100% - 60px)":"100%",rebind:t.rebind,selectOnLoad:!0},on:{itemClick:t.ItemClick},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("div",{staticStyle:{"font-size":"13px","margin-bottom":"10px",padding:"0 10px"}},[r("div",{staticStyle:{"font-weight":"bold","font-family":"Raleway","text-transform":"uppercase",overflow:"hidden","text-overflow":"ellipsis",width:"180px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(n.NamaPelanggan)+" ")]),r("div",{staticStyle:{color:"gray",display:"flex"}},[r("span",{staticStyle:{"font-size":"10px",padding:"3px 5px 0 5px",background:"#ddd","border-radius":"5px",color:"#333","margin-right":"5px"}},[t._v(" "+t._s(n.NoPengujian)+" ")]),r("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis",width:"160px",height:"20px","white-space":"nowrap"}},[t._v(" "+t._s(n.Nama)+" ")])])])]}}])})],1)])},[],!1,null,null).exports;r("default",a({components:{SidePane:u},data:function(){return{rawUrlOri:"",rawUrl:"",forms:{},passphrase:"",showPassphrase:!1,loading:!1,rebind:1}},methods:{ShowSertifikat:function(t){this.forms=t,this.rawUrlOri=t.LkUrl,this.rawUrl=this.$api.url+t.LkUrl.replace(/(xlsx|docx)$/,"pdf")},Reject:function(){var r=this;return n(t().m(function e(){var n;return t().w(function(t){for(;;)switch(t.n){case 0:if(!(n=prompt("Alasan?"))){t.n=2;break}return t.n=1,r.$api.call("UJI_SavLembarKerjaRejection",{LembarKerjaID:r.forms.LembarKerjaID,Alasan:n});case 1:t.v.success&&(r.rawUrl="",r.rebind++);case 2:return t.a(2)}},e)}))()},Refine:function(){var r=this;return n(t().m(function e(){return t().w(function(t){for(;;)switch(t.n){case 0:return r.loading=!0,t.n=1,r.$api.get("/reports/uji/refine-lk/"+r.forms.LembarKerjaID);case 1:r.rawUrl=r.rawUrl+"?123",r.loading=!1;case 2:return t.a(2)}},e)}))()},Download:function(){window.open(this.$api.url+this.rawUrlOri,"_blank")},Sign:function(){var r=this;return n(t().m(function e(){return t().w(function(t){for(;;)switch(t.n){case 0:if(!confirm("Setujui?")){t.n=2;break}return t.n=1,r.$api.call("UJI_SavLembarKerjaApproval",{LembarKerjaID:r.forms.LembarKerjaID,FilePath:r.rawUrlOri});case 1:t.v.success&&(r.rawUrl="",r.rebind++);case 2:return t.a(2)}},e)}))()}}},function(){var t=this,r=t._self._c;return r("div",{staticStyle:{display:"flex"}},[r("SidePane",{attrs:{rebind:t.rebind},on:{"item-click":t.ShowSertifikat}}),t.rawUrl?r("div",{staticClass:"right-pane"},[r("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.rawUrl,frameborder:"0"}}),r("div",{staticStyle:{position:"fixed",bottom:"0",right:"20px",left:"calc(25% - 5px)",display:"flex",background:"rgba(255, 255, 255, 0.7)",padding:"20px"}},[r(c,{attrs:{color:"error",disabled:t.loading},on:{click:t.Reject}},[t._v(" TOLAK ")]),r(s),r(c,{directives:[{name:"show",rawName:"v-show",value:!t.rawUrl.match(/\?123/),expression:"!rawUrl.match(/\\?123/)"}],attrs:{text:"",disabled:t.loading},on:{click:t.Refine}},[t._v(" REFINE ")]),r(c,{attrs:{text:"",color:"success",disabled:t.loading},on:{click:t.Download}},[t._v(" EXCEL ")]),r(c,{directives:[{name:"show",rawName:"v-show",value:!t.loading,expression:"!loading"}],staticStyle:{"margin-right":"8px"},on:{click:function(r){t.rawUrl=""}}},[t._v(" BATAL ")]),r(c,{attrs:{color:"primary",disabled:t.loading},on:{click:t.Sign}},[t._v(" SETUJUI ")])],1)]):t._e()],1)},[],!1,null,null).exports)}}})}();
